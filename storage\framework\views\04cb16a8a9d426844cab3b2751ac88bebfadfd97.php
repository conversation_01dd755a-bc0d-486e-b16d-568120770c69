

<?php $__env->startSection('title', 'Dashboard'); ?>

<?php $__env->startSection('content'); ?>
  <!-- Compact Modern Dashboard -->
  <div class="compact-dashboard">
    <!-- Background Blur Effect -->
    <div class="dashboard-background">
      <div class="blur-overlay"></div>
      <div class="gradient-mesh"></div>
    </div>

    <!-- Compact Header -->
    <div class="compact-header">
      <div class="header-container">
        <div class="header-left">
          <h1 class="page-title">Dashboard</h1>
          <p class="page-subtitle">
            Good <?php echo e(date('H') < 12 ? 'morning' : (date('H') < 18 ? 'afternoon' : 'evening'), false); ?>, <?php echo e(Auth::user()->username, false); ?>

            <span class="date-badge"><?php echo e(date('M j'), false); ?></span>
          </p>
        </div>

      </div>
    </div>

    <!-- Main Content -->
    <div class="dashboard-content">
      <!-- Stats Grid -->
      <div class="stats-grid">
        <!-- Students Card -->
        <div class="stat-card students" data-card="students" data-count="<?php echo e($studentCount ?? 0, false); ?>">
          <div class="stat-card-content">
            <div class="stat-icon">
              <i class="bx bx-user-pin"></i>
            </div>
            <div class="stat-info">
              <h3 class="stat-number" data-target="<?php echo e($studentCount ?? 0, false); ?>"><?php echo e($studentCount ?? 0, false); ?></h3>
              <p class="stat-label">Students</p>
              <span class="stat-change positive">
                <i class="bx bx-trending-up"></i>
                +8.2%
              </span>
            </div>
          </div>
          <div class="stat-card-bg"></div>
        </div>

        <!-- Tutors Card -->
        <div class="stat-card tutors" data-card="tutors" data-count="<?php echo e($tutorCount ?? 0, false); ?>">
          <div class="stat-card-content">
            <div class="stat-icon">
              <i class="bx bx-user-voice"></i>
            </div>
            <div class="stat-info">
              <h3 class="stat-number" data-target="<?php echo e($tutorCount ?? 0, false); ?>"><?php echo e($tutorCount ?? 0, false); ?></h3>
              <p class="stat-label">Tutors</p>
              <span class="stat-change neutral">
                <i class="bx bx-minus"></i>
                0%
              </span>
            </div>
          </div>
          <div class="stat-card-bg"></div>
        </div>

        <!-- Sessions Card -->
        <div class="stat-card sessions" data-card="sessions" data-count="156">
          <div class="stat-card-content">
            <div class="stat-icon">
              <i class="bx bx-calendar-event"></i>
            </div>
            <div class="stat-info">
              <h3 class="stat-number" data-target="156">156</h3>
              <p class="stat-label">Sessions</p>
              <span class="stat-change positive">
                <i class="bx bx-trending-up"></i>
                +15.3%
              </span>
            </div>
          </div>
          <div class="stat-card-bg"></div>
        </div>
      </div>

      <!-- Content Grid -->
      <div class="content-grid">
        <!-- Recent Activity Card -->
        <div class="activity-card">
          <div class="card-header">
            <h3 class="card-title">Recent Activity</h3>
            <button class="card-action">
              <i class="bx bx-dots-horizontal-rounded"></i>
            </button>
          </div>
          <div class="card-content">
            <div class="activity-list">
              <?php $__empty_1 = true; $__currentLoopData = $recentSessions ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $session): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
              <div class="activity-item">
                <div class="activity-avatar <?php echo e($session->session_type == 'regular' ? 'primary' : 'info', false); ?>">
                  <i class="bx bx-book-reader"></i>
                </div>
                <div class="activity-details">
                  <h4 class="activity-title"><?php echo e($session->title, false); ?></h4>
                  <p class="activity-desc"><?php echo e(ucfirst($session->session_type), false); ?> - $<?php echo e(number_format($session->price_per_slot, 2), false); ?></p>
                  <span class="activity-time">
                    <?php if($session->created_at && $session->created_at instanceof \Carbon\Carbon): ?>
                      <?php if($session->created_at->isToday()): ?>
                        Today
                      <?php elseif($session->created_at->isYesterday()): ?>
                        Yesterday
                      <?php else: ?>
                        <?php echo e($session->created_at->format('M d'), false); ?>

                      <?php endif; ?>
                    <?php else: ?>
                      N/A
                    <?php endif; ?>
                  </span>
                </div>
              </div>
              <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
              <!-- Mock Recent Activities -->
              <div class="activity-item">
                <div class="activity-avatar primary">
                  <i class="bx bx-book-reader"></i>
                </div>
                <div class="activity-details">
                  <h4 class="activity-title">Full-Length Proctored Diagnostic SAT/ACT Assessment with 80% Extended Time</h4>
                  <p class="activity-desc">Regular - $200.00</p>
                  <span class="activity-time">2 hours ago</span>
                </div>
              </div>

              <div class="activity-item">
                <div class="activity-avatar info">
                  <i class="bx bx-edit-alt"></i>
                </div>
                <div class="activity-details">
                  <h4 class="activity-title">Full-Length Proctored Diagnostic SAT/ACT Assessment with Regular Time</h4>
                  <p class="activity-desc">Extended - $180.00</p>
                  <span class="activity-time">4 hours ago</span>
                </div>
              </div>

              <div class="activity-item">
                <div class="activity-avatar primary">
                  <i class="bx bx-calculator"></i>
                </div>
                <div class="activity-details">
                  <h4 class="activity-title">Full-Length Proctored Practice SAT Test with Regular Time</h4>
                  <p class="activity-desc">Regular - $150.00</p>
                  <span class="activity-time">6 hours ago</span>
                </div>
              </div>

              <div class="activity-item">
                <div class="activity-avatar info">
                  <i class="bx bx-pencil"></i>
                </div>
                <div class="activity-details">
                  <h4 class="activity-title">Full-Length Proctored Practice SAT Test with 50% Extended Time</h4>
                  <p class="activity-desc">Extended - $175.00</p>
                  <span class="activity-time">8 hours ago</span>
                </div>
              </div>

              <div class="activity-item">
                <div class="activity-avatar primary">
                  <i class="bx bx-brain"></i>
                </div>
                <div class="activity-details">
                  <h4 class="activity-title">Full-Length Proctored Practice ACT Test with Regular Time</h4>
                  <p class="activity-desc">Regular - $160.00</p>
                  <span class="activity-time">Yesterday</span>
                </div>
              </div>

              <div class="activity-item">
                <div class="activity-avatar info">
                  <i class="bx bx-time-five"></i>
                </div>
                <div class="activity-details">
                  <h4 class="activity-title">College Essay Writing Workshop Session</h4>
                  <p class="activity-desc">Workshop - $120.00</p>
                  <span class="activity-time">Yesterday</span>
                </div>
              </div>

              <div class="activity-item">
                <div class="activity-avatar primary">
                  <i class="bx bx-trophy"></i>
                </div>
                <div class="activity-details">
                  <h4 class="activity-title">SAT Math Subject Test Preparation</h4>
                  <p class="activity-desc">Regular - $140.00</p>
                  <span class="activity-time">2 days ago</span>
                </div>
              </div>

              <div class="activity-item">
                <div class="activity-avatar info">
                  <i class="bx bx-graduation"></i>
                </div>
                <div class="activity-details">
                  <h4 class="activity-title">College Application Review Session</h4>
                  <p class="activity-desc">Consultation - $100.00</p>
                  <span class="activity-time">2 days ago</span>
                </div>
              </div>

              <div class="activity-item">
                <div class="activity-avatar primary">
                  <i class="bx bx-file-blank"></i>
                </div>
                <div class="activity-details">
                  <h4 class="activity-title">AP Literature Essay Writing Session</h4>
                  <p class="activity-desc">Advanced - $130.00</p>
                  <span class="activity-time">3 days ago</span>
                </div>
              </div>

              <div class="activity-item">
                <div class="activity-avatar info">
                  <i class="bx bx-math"></i>
                </div>
                <div class="activity-details">
                  <h4 class="activity-title">SAT Math Section Intensive Review</h4>
                  <p class="activity-desc">Regular - $110.00</p>
                  <span class="activity-time">3 days ago</span>
                </div>
              </div>

              <div class="activity-item">
                <div class="activity-avatar primary">
                  <i class="bx bx-chat"></i>
                </div>
                <div class="activity-details">
                  <h4 class="activity-title">College Interview Preparation Workshop</h4>
                  <p class="activity-desc">Workshop - $95.00</p>
                  <span class="activity-time">4 days ago</span>
                </div>
              </div>

              <div class="activity-item">
                <div class="activity-avatar info">
                  <i class="bx bx-library"></i>
                </div>
                <div class="activity-details">
                  <h4 class="activity-title">ACT English Section Strategy Session</h4>
                  <p class="activity-desc">Regular - $125.00</p>
                  <span class="activity-time">4 days ago</span>
                </div>
              </div>

              <div class="activity-item">
                <div class="activity-avatar primary">
                  <i class="bx bx-line-chart"></i>
                </div>
                <div class="activity-details">
                  <h4 class="activity-title">SAT Score Analysis and Improvement Plan</h4>
                  <p class="activity-desc">Consultation - $85.00</p>
                  <span class="activity-time">5 days ago</span>
                </div>
              </div>

              <div class="activity-item">
                <div class="activity-avatar info">
                  <i class="bx bx-bookmark"></i>
                </div>
                <div class="activity-details">
                  <h4 class="activity-title">Personal Statement Writing Workshop</h4>
                  <p class="activity-desc">Workshop - $115.00</p>
                  <span class="activity-time">5 days ago</span>
                </div>
              </div>

              <div class="activity-item">
                <div class="activity-avatar primary">
                  <i class="bx bx-target-lock"></i>
                </div>
                <div class="activity-details">
                  <h4 class="activity-title">ACT Science Section Practice Test</h4>
                  <p class="activity-desc">Regular - $105.00</p>
                  <span class="activity-time">6 days ago</span>
                </div>
              </div>

              <div class="activity-item">
                <div class="activity-avatar info">
                  <i class="bx bx-bulb"></i>
                </div>
                <div class="activity-details">
                  <h4 class="activity-title">Study Skills and Time Management Session</h4>
                  <p class="activity-desc">Consultation - $90.00</p>
                  <span class="activity-time">6 days ago</span>
                </div>
              </div>

              <div class="activity-item">
                <div class="activity-avatar primary">
                  <i class="bx bx-medal"></i>
                </div>
                <div class="activity-details">
                  <h4 class="activity-title">Scholarship Application Review</h4>
                  <p class="activity-desc">Consultation - $120.00</p>
                  <span class="activity-time">1 week ago</span>
                </div>
              </div>
              <?php endif; ?>
            </div>
          </div>
        </div>

        <!-- Modern Calendar Card -->
        <div class="calendar-card">
          <div class="card-header">
            <h3 class="card-title">
              <i class="bx bx-calendar-event"></i>
              Schedule Calendar
            </h3>
            <div class="card-actions">
              <button class="card-action">
                <i class="bx bx-plus"></i>
              </button>
              <button class="card-action">
                <i class="bx bx-filter-alt"></i>
              </button>
            </div>
          </div>
          <div class="card-content">
            <div class="calendar-container">
              <div id="calendar"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
  <link href="https://cdn.jsdelivr.net/npm/fullcalendar@5.11.3/main.min.css" rel="stylesheet" />
  <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css" rel="stylesheet" />
  <style>
    /* Override container padding for dashboard */
    .container-xxl.container-p-y {
      padding-top: 0 !important;
      padding-bottom: 0 !important;
      padding-left: 0 !important;
      padding-right: 0 !important;
    }

    /* Remove any additional spacing from content wrapper */
    .content-wrapper {
      padding-top: 0 !important;
    }

    /* Remove spacing from layout page */
    .layout-page {
      padding-top: 0 !important;
    }

    /* Hide or minimize the profile header section for dashboard */
    .layout-page > .d-flex.justify-content-end {
      display: none !important;
    }

    /* Enhanced Modern Dashboard Styles */
    .compact-dashboard {
      min-height: 100vh;
      position: relative;
      background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
      padding: 0;
      margin: 0.75rem -1.5rem -1.5rem -1.5rem; /* Small top margin, remove other container padding */
      overflow-x: hidden;
      border-radius: 1.5rem 1.5rem 0 0; /* Curved top corners */
    }

    /* Background Effects */
    .dashboard-background {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: -1;
      overflow: hidden;
    }

    .blur-overlay {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(135deg,
        rgba(37, 99, 235, 0.06) 0%,
        rgba(29, 78, 216, 0.06) 25%,
        rgba(14, 165, 233, 0.06) 50%,
        rgba(30, 64, 175, 0.06) 75%,
        rgba(55, 48, 163, 0.06) 100%);
      backdrop-filter: blur(100px);
    }

    .gradient-mesh {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background:
        radial-gradient(circle at 20% 20%, rgba(37, 99, 235, 0.08) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(14, 165, 233, 0.08) 0%, transparent 50%),
        radial-gradient(circle at 40% 60%, rgba(30, 64, 175, 0.08) 0%, transparent 50%);
      animation: meshMove 20s ease-in-out infinite;
    }

    @keyframes meshMove {
      0%, 100% { transform: translate(0, 0) scale(1); }
      33% { transform: translate(-20px, -20px) scale(1.1); }
      66% { transform: translate(20px, -10px) scale(0.9); }
    }

    /* Enhanced Header */
    .compact-header {
      position: relative;
      z-index: 10;
      padding: 1.5rem 2rem;
      margin: 1rem 1rem 0 1rem;
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(25px);
      border: 1px solid rgba(37, 99, 235, 0.1);
      border-radius: 1.5rem;
      box-shadow: 0 8px 32px rgba(37, 99, 235, 0.12);
      transition: all 0.3s ease;
    }

    /* Blue line at top of header removed */

    /* Shimmer animation removed */

    .header-container {
      display: flex;
      align-items: center;
      justify-content: space-between;
      max-width: 1400px;
      margin: 0 auto;
    }

    .header-left {
      flex: 1;
    }

    .page-title {
      font-size: 1.75rem;
      font-weight: 700;
      color: #4a5568;
      margin: 0;
      line-height: 1.2;
    }

    .page-subtitle {
      font-size: 0.875rem;
      color: #6b7280;
      margin: 0.25rem 0 0 0;
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }

    .date-badge {
      background: linear-gradient(135deg, #2563eb, #1d4ed8);
      color: white;
      padding: 0.125rem 0.5rem;
      border-radius: 0.375rem;
      font-size: 0.75rem;
      font-weight: 500;
    }

    .header-right {
      display: flex;
      align-items: center;
      gap: 1.5rem;
    }

    .header-stats {
      display: flex;
      gap: 1rem;
    }

    .mini-stat {
      display: flex;
      align-items: center;
      gap: 0.75rem;
      padding: 0.75rem 1.25rem;
      background: rgba(255, 255, 255, 0.8);
      backdrop-filter: blur(20px);
      border: 1px solid rgba(37, 99, 235, 0.1);
      border-radius: 1rem;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      cursor: pointer;
      box-shadow: 0 4px 12px rgba(37, 99, 235, 0.05);
      position: relative;
      overflow: hidden;
    }

    .mini-stat::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, rgba(37, 99, 235, 0.02), rgba(14, 165, 233, 0.02));
      opacity: 0;
      transition: opacity 0.3s ease;
      pointer-events: none;
    }

    .mini-stat:hover {
      transform: translateY(-2px) scale(1.02);
      box-shadow: 0 8px 20px rgba(37, 99, 235, 0.12);
      border-color: rgba(37, 99, 235, 0.2);
    }

    .mini-stat:hover::before {
      opacity: 1;
    }

    .mini-stat-icon {
      width: 2.5rem;
      height: 2.5rem;
      border-radius: 0.75rem;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 1.125rem;
      color: white;
      transition: all 0.3s ease;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      position: relative;
    }

    .mini-stat-icon::before {
      content: '';
      position: absolute;
      top: -1px;
      left: -1px;
      right: -1px;
      bottom: -1px;
      border-radius: 0.875rem;
      background: linear-gradient(45deg, rgba(255, 255, 255, 0.2), transparent);
      z-index: -1;
    }

    .students-stat .mini-stat-icon {
      background: linear-gradient(135deg, #0ea5e9, #0284c7);
    }

    .tutors-stat .mini-stat-icon {
      background: linear-gradient(135deg, #1e40af, #1e3a8a);
    }

    .mini-stat:hover .mini-stat-icon {
      transform: scale(1.1) rotate(5deg);
      box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
    }

    .mini-stat-content {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      position: relative;
      z-index: 2;
    }

    .mini-stat-number {
      display: block;
      font-size: 1.5rem;
      font-weight: 800;
      color: #4a5568;
      line-height: 1;
      background: linear-gradient(135deg, #2563eb, #1d4ed8);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      transition: all 0.3s ease;
    }

    .mini-stat:hover .mini-stat-number {
      transform: scale(1.05);
    }

    .mini-stat-label {
      font-size: 0.8rem;
      color: #6b7280;
      margin-top: 0.125rem;
      font-weight: 600;
      letter-spacing: 0.025em;
      transition: all 0.3s ease;
    }

    .mini-stat:hover .mini-stat-label {
      color: #4a5568;
    }

    /* Responsive Design for Header Stats */
    @media (max-width: 768px) {
      .header-container {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
      }

      .header-right {
        width: 100%;
        justify-content: center;
      }

      .header-stats {
        gap: 0.75rem;
        flex-wrap: wrap;
        justify-content: center;
      }

      .mini-stat {
        padding: 0.5rem 1rem;
        gap: 0.5rem;
      }

      .mini-stat-icon {
        width: 2rem;
        height: 2rem;
        font-size: 1rem;
      }

      .mini-stat-number {
        font-size: 1.25rem;
      }

      .mini-stat-label {
        font-size: 0.75rem;
      }
    }

    @media (max-width: 480px) {
      .header-stats {
        width: 100%;
        justify-content: space-around;
      }

      .mini-stat {
        flex: 1;
        min-width: 120px;
        justify-content: center;
      }
    }

    .header-actions {
      display: flex;
      gap: 0.5rem;
    }

    .action-btn {
      width: 2.5rem;
      height: 2.5rem;
      border-radius: 0.75rem;
      border: none;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 1.125rem;
      cursor: pointer;
      transition: all 0.2s ease;
      position: relative;
      overflow: hidden;
    }

    .action-btn.secondary {
      background: rgba(107, 114, 128, 0.1);
      color: #6b7280;
    }

    .action-btn.secondary:hover {
      background: rgba(107, 114, 128, 0.2);
      color: #374151;
      transform: translateY(-1px);
    }

    .action-btn.primary {
      background: linear-gradient(135deg, #2563eb, #1d4ed8);
      color: white;
      padding: 0 1rem;
      width: auto;
      gap: 0.5rem;
    }

    .action-btn.primary:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(37, 99, 235, 0.4);
    }

    .action-btn span {
      font-size: 0.875rem;
      font-weight: 500;
    }

    /* Dashboard Content */
    .dashboard-content {
      position: relative;
      z-index: 5;
      padding: 1.5rem 2rem;
      max-width: 1400px;
      margin: 1rem auto;
    }

    /* Stats Grid */
    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 1.5rem;
      margin-bottom: 2rem;
    }

    .stat-card {
      position: relative;
      background: rgba(255, 255, 255, 0.98);
      backdrop-filter: blur(30px);
      border: 1px solid rgba(37, 99, 235, 0.12);
      border-radius: 1.5rem;
      padding: 2rem;
      overflow: hidden;
      transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
      cursor: pointer;
      box-shadow:
        0 8px 32px rgba(37, 99, 235, 0.08),
        0 4px 16px rgba(37, 99, 235, 0.04),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
    }

    .stat-card::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg,
        rgba(37, 99, 235, 0.03),
        rgba(14, 165, 233, 0.03),
        rgba(30, 64, 175, 0.03));
      opacity: 0;
      transition: opacity 0.4s ease;
      pointer-events: none;
    }

    .stat-card::after {
      content: '';
      position: absolute;
      top: -2px;
      left: -2px;
      right: -2px;
      bottom: -2px;
      background: linear-gradient(135deg,
        rgba(37, 99, 235, 0.1),
        rgba(14, 165, 233, 0.1),
        rgba(30, 64, 175, 0.1));
      border-radius: 1.625rem;
      opacity: 0;
      transition: opacity 0.4s ease;
      pointer-events: none;
      z-index: -1;
    }

    .stat-card:hover {
      transform: translateY(-12px) scale(1.03);
      box-shadow:
        0 25px 50px rgba(37, 99, 235, 0.18),
        0 12px 24px rgba(37, 99, 235, 0.12),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);
      border-color: rgba(37, 99, 235, 0.25);
    }

    .stat-card:hover::before {
      opacity: 1;
    }

    .stat-card:hover::after {
      opacity: 1;
    }

    .stat-card-content {
      position: relative;
      z-index: 2;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .stat-icon {
      width: 4rem;
      height: 4rem;
      border-radius: 1.25rem;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 1.75rem;
      color: white;
      position: relative;
      transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
      box-shadow:
        0 8px 24px rgba(0, 0, 0, 0.12),
        0 4px 12px rgba(0, 0, 0, 0.08),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
    }

    .stat-icon::before {
      content: '';
      position: absolute;
      top: -3px;
      left: -3px;
      right: -3px;
      bottom: -3px;
      border-radius: 1.375rem;
      background: linear-gradient(45deg,
        rgba(255, 255, 255, 0.3),
        rgba(255, 255, 255, 0.1),
        transparent);
      z-index: -1;
      transition: all 0.4s ease;
    }

    .stat-card:hover .stat-icon {
      transform: scale(1.15) rotate(8deg);
      box-shadow:
        0 12px 32px rgba(0, 0, 0, 0.18),
        0 6px 16px rgba(0, 0, 0, 0.12),
        inset 0 1px 0 rgba(255, 255, 255, 0.4);
    }

    .stat-card.students .stat-icon {
      background: linear-gradient(135deg, #0ea5e9, #0284c7, #0369a1);
      box-shadow:
        0 8px 24px rgba(14, 165, 233, 0.3),
        0 4px 12px rgba(14, 165, 233, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
    }

    .stat-card.tutors .stat-icon {
      background: linear-gradient(135deg, #1e40af, #1e3a8a, #1d4ed8);
      box-shadow:
        0 8px 24px rgba(30, 64, 175, 0.3),
        0 4px 12px rgba(30, 64, 175, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
    }

    .stat-card.sessions .stat-icon {
      background: linear-gradient(135deg, #3730a3, #312e81, #4338ca);
      box-shadow:
        0 8px 24px rgba(55, 48, 163, 0.3),
        0 4px 12px rgba(55, 48, 163, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
    }

    .stat-info {
      text-align: right;
      flex: 1;
      margin-left: 1rem;
    }

    .stat-number {
      font-size: 2.5rem;
      font-weight: 900;
      color: #4a5568;
      margin: 0;
      line-height: 1;
      background: linear-gradient(135deg, #2563eb, #1d4ed8, #1e40af);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
      text-shadow: 0 2px 4px rgba(37, 99, 235, 0.1);
      letter-spacing: -0.02em;
    }

    .stat-card:hover .stat-number {
      transform: scale(1.08);
      background: linear-gradient(135deg, #1d4ed8, #1e40af, #3730a3);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    .stat-label {
      font-size: 0.95rem;
      color: #6b7280;
      margin: 0.5rem 0 0.75rem 0;
      font-weight: 600;
      letter-spacing: 0.025em;
      transition: all 0.3s ease;
    }

    .stat-card:hover .stat-label {
      color: #4a5568;
      transform: translateY(-1px);
    }

    .stat-change {
      font-size: 0.8rem;
      font-weight: 700;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      gap: 0.375rem;
      padding: 0.25rem 0.75rem;
      border-radius: 1rem;
      transition: all 0.3s ease;
      backdrop-filter: blur(10px);
    }

    .stat-change.positive {
      color: #10b981;
      background: rgba(16, 185, 129, 0.1);
      border: 1px solid rgba(16, 185, 129, 0.2);
    }

    .stat-change.negative {
      color: #ef4444;
      background: rgba(239, 68, 68, 0.1);
      border: 1px solid rgba(239, 68, 68, 0.2);
    }

    .stat-change.neutral {
      color: #6b7280;
      background: rgba(107, 114, 128, 0.1);
      border: 1px solid rgba(107, 114, 128, 0.2);
    }

    .stat-card:hover .stat-change {
      transform: translateY(-1px) scale(1.05);
    }

    .stat-change i {
      font-size: 0.875rem;
    }

    .stat-card-bg {
      position: absolute;
      top: -50%;
      right: -50%;
      width: 200%;
      height: 200%;
      background: radial-gradient(circle,
        rgba(255, 255, 255, 0.15) 0%,
        rgba(37, 99, 235, 0.05) 30%,
        transparent 70%);
      opacity: 0;
      transition: opacity 0.4s ease;
      pointer-events: none;
      animation: float 6s ease-in-out infinite;
    }

    .stat-card:hover .stat-card-bg {
      opacity: 1;
    }

    /* Add floating particles effect using stat-card-bg */
    .stat-card-bg::before {
      content: '';
      position: absolute;
      top: 20%;
      right: 15%;
      width: 4px;
      height: 4px;
      background: rgba(37, 99, 235, 0.4);
      border-radius: 50%;
      animation: particle-float 4s ease-in-out infinite;
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    .stat-card:hover .stat-card-bg::before {
      opacity: 1;
    }

    @keyframes particle-float {
      0%, 100% {
        transform: translateY(0px) translateX(0px) scale(1);
        opacity: 0.3;
      }
      50% {
        transform: translateY(-10px) translateX(5px) scale(1.2);
        opacity: 0.6;
      }
    }

    /* Subtle pulse animation for icons */
    .stat-icon {
      animation: subtle-pulse 3s ease-in-out infinite;
    }

    @keyframes subtle-pulse {
      0%, 100% {
        box-shadow:
          0 8px 24px rgba(0, 0, 0, 0.12),
          0 4px 12px rgba(0, 0, 0, 0.08),
          inset 0 1px 0 rgba(255, 255, 255, 0.3);
      }
      50% {
        box-shadow:
          0 10px 28px rgba(0, 0, 0, 0.15),
          0 6px 16px rgba(0, 0, 0, 0.1),
          inset 0 1px 0 rgba(255, 255, 255, 0.4);
      }
    }

    @keyframes icon-shake {
      0%, 100% { transform: scale(1.15) rotate(8deg); }
      25% { transform: scale(1.18) rotate(10deg); }
      50% { transform: scale(1.15) rotate(6deg); }
      75% { transform: scale(1.18) rotate(10deg); }
    }

    /* Content Grid */
    .content-grid {
      display: grid;
      grid-template-columns: 1fr 2fr;
      gap: 1.5rem;
      margin: 0 1rem;
    }

    /* Enhanced Activity & Calendar Cards */
    .activity-card, .calendar-card {
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(25px);
      border: 1px solid rgba(37, 99, 235, 0.1);
      border-radius: 1.5rem;
      overflow: hidden;
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      box-shadow: 0 8px 24px rgba(37, 99, 235, 0.08);
      position: relative;
    }

    .activity-card::before, .calendar-card::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 3px;
      background: linear-gradient(90deg, #2563eb, #0ea5e9, #1e40af);
      opacity: 0;
      transition: opacity 0.3s ease;
      display: none; /* Remove the blue line hover effect */
    }

    .activity-card:hover, .calendar-card:hover {
      transform: translateY(-6px) scale(1.01);
      box-shadow: 0 15px 35px rgba(37, 99, 235, 0.15);
      border-color: rgba(37, 99, 235, 0.2);
    }

    /* Blue line hover effect removed */

    .card-header {
      padding: 1.25rem 1.5rem;
      border-bottom: 1px solid rgba(229, 231, 235, 0.5);
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .card-title {
      font-size: 1.125rem;
      font-weight: 600;
      color: #4a5568;
      margin: 0;
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }

    .card-action, .card-actions {
      display: flex;
      gap: 0.5rem;
    }

    .card-action {
      width: 2rem;
      height: 2rem;
      border-radius: 0.5rem;
      border: none;
      background: rgba(107, 114, 128, 0.1);
      color: #6b7280;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.2s ease;
    }

    .card-action:hover {
      background: rgba(107, 114, 128, 0.2);
      color: #374151;
    }

    .card-content {
      padding: 1.5rem;
    }

    /* Activity List */
    .activity-list {
      display: flex;
      flex-direction: column;
      gap: 1rem;
    }

    .activity-item {
      display: flex;
      align-items: center;
      gap: 1rem;
      padding: 1rem;
      border-radius: 1rem;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      position: relative;
      overflow: hidden;
    }

    .activity-item::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(37, 99, 235, 0.05), transparent);
      transition: left 0.5s ease;
    }

    .activity-item:hover {
      background: rgba(37, 99, 235, 0.03);
      transform: translateX(4px);
      box-shadow: 0 4px 15px rgba(37, 99, 235, 0.08);
    }

    .activity-item:hover::before {
      left: 100%;
    }

    .activity-avatar {
      width: 3rem;
      height: 3rem;
      border-radius: 1rem;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 1.125rem;
      color: white;
      flex-shrink: 0;
      transition: all 0.3s ease;
      position: relative;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .activity-avatar::before {
      content: '';
      position: absolute;
      top: -1px;
      left: -1px;
      right: -1px;
      bottom: -1px;
      border-radius: 1.125rem;
      background: linear-gradient(45deg, rgba(255, 255, 255, 0.2), transparent);
      z-index: -1;
    }

    .activity-item:hover .activity-avatar {
      transform: scale(1.1) rotate(-5deg);
      box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
    }

    .activity-avatar.primary {
      background: linear-gradient(135deg, #2563eb, #1d4ed8);
    }

    .activity-avatar.info {
      background: linear-gradient(135deg, #0ea5e9, #0284c7);
    }

    .activity-details {
      flex: 1;
      min-width: 0;
    }

    .activity-title {
      font-size: 0.9rem;
      font-weight: 600;
      color: #4a5568;
      margin: 0 0 0.25rem 0;
      line-height: 1.3;
      transition: color 0.3s ease;
    }

    .activity-item:hover .activity-title {
      color: #2563eb;
    }

    .activity-desc {
      font-size: 0.75rem;
      color: #6b7280;
      margin: 0 0 0.25rem 0;
      line-height: 1.3;
    }

    .activity-time {
      font-size: 0.75rem;
      color: #9ca3af;
    }

    .empty-state {
      text-align: center;
      padding: 2rem;
      color: #9ca3af;
    }

    .empty-state i {
      font-size: 2rem;
      margin-bottom: 0.5rem;
      opacity: 0.5;
    }

    /* Enhanced Calendar Styles */
    .calendar-container {
      background: rgba(255, 255, 255, 0.7);
      border-radius: 1rem;
      padding: 1.5rem;
      backdrop-filter: blur(20px);
      border: 1px solid rgba(37, 99, 235, 0.1);
      box-shadow: 0 4px 20px rgba(37, 99, 235, 0.05);
    }

    #calendar {
      background: transparent;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    }

    /* Enhanced Toolbar */
    .fc-toolbar {
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(15px);
      padding: 1.25rem 1.5rem;
      border-radius: 1rem;
      margin-bottom: 1.5rem;
      border: 1px solid rgba(37, 99, 235, 0.1);
      box-shadow: 0 4px 15px rgba(37, 99, 235, 0.08);
      gap: 1rem;
    }

    .fc-toolbar-chunk {
      display: flex;
      align-items: center;
      gap: 1.5rem;
    }

    /* Left toolbar chunk - navigation buttons */
    .fc-toolbar-chunk:first-child {
      gap: 0.5rem;
    }

    .fc-toolbar-chunk:first-child .fc-button-group {
      margin-right: 1rem;
    }

    /* Navigation Button Group */
    .fc-button-group {
      display: flex !important;
      gap: 0.25rem !important;
      margin-right: 1.5rem !important;
    }

    /* Navigation Buttons */
    .fc-prev-button, .fc-next-button {
      background: linear-gradient(135deg, #2563eb, #1d4ed8) !important;
      border: none !important;
      color: white !important;
      border-radius: 0.75rem !important;
      padding: 0.75rem 1rem !important;
      font-weight: 600 !important;
      font-size: 0.875rem !important;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
      box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3) !important;
      min-width: 44px !important;
      height: 44px !important;
      margin: 0 !important;
    }

    /* Today Button - Separate styling */
    .fc-today-button {
      background: linear-gradient(135deg, #2563eb, #1d4ed8) !important;
      border: none !important;
      color: white !important;
      border-radius: 0.75rem !important;
      padding: 0.75rem 1.25rem !important;
      font-weight: 600 !important;
      font-size: 0.875rem !important;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
      box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3) !important;
      min-width: 80px !important;
      height: 44px !important;
      margin-left: 1rem !important;
    }

    .fc-prev-button:hover, .fc-next-button:hover, .fc-today-button:hover {
      transform: translateY(-2px) scale(1.05) !important;
      box-shadow: 0 6px 20px rgba(37, 99, 235, 0.4) !important;
      background: linear-gradient(135deg, #1d4ed8, #1e40af) !important;
    }

    .fc-prev-button:active, .fc-next-button:active, .fc-today-button:active {
      transform: translateY(0) scale(0.98) !important;
    }

    /* View Buttons */
    .fc-dayGridMonth-button, .fc-timeGridWeek-button, .fc-listWeek-button {
      background: rgba(37, 99, 235, 0.1) !important;
      border: 1px solid rgba(37, 99, 235, 0.2) !important;
      color: #2563eb !important;
      border-radius: 0.75rem !important;
      padding: 0.75rem 1.25rem !important;
      font-weight: 600 !important;
      font-size: 0.875rem !important;
      transition: all 0.3s ease !important;
      margin: 0 0.25rem !important;
    }

    .fc-dayGridMonth-button:hover, .fc-timeGridWeek-button:hover, .fc-listWeek-button:hover {
      background: rgba(37, 99, 235, 0.15) !important;
      transform: translateY(-1px) !important;
      box-shadow: 0 4px 12px rgba(37, 99, 235, 0.2) !important;
    }

    .fc-button-active {
      background: linear-gradient(135deg, #2563eb, #1d4ed8) !important;
      color: white !important;
      box-shadow: 0 4px 15px rgba(37, 99, 235, 0.3) !important;
    }

    /* Title Styling */
    .fc-toolbar-title {
      font-size: 1.5rem !important;
      font-weight: 700 !important;
      color: #4a5568 !important;
      margin: 0 1rem !important;
      background: linear-gradient(135deg, #4a5568, #2563eb);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    .fc-button {
      background: linear-gradient(135deg, #2563eb, #1d4ed8) !important;
      border: none !important;
      color: white !important;
      border-radius: 0.5rem !important;
      padding: 0.5rem 1rem !important;
      font-weight: 500 !important;
      transition: all 0.2s ease !important;
    }

    .fc-button:hover {
      transform: translateY(-1px) !important;
      box-shadow: 0 4px 12px rgba(37, 99, 235, 0.4) !important;
    }

    .fc-event {
      background: linear-gradient(135deg, #2563eb, #1d4ed8) !important;
      border: none !important;
      border-radius: 0.5rem !important;
      padding: 0.25rem 0.5rem !important;
      font-weight: 500 !important;
      transition: all 0.2s ease !important;
    }

    .fc-event:hover {
      transform: scale(1.02) !important;
      box-shadow: 0 2px 8px rgba(37, 99, 235, 0.3) !important;
    }

    /* Enhanced Calendar Grid - Clean Design */
    .fc-scrollgrid {
      border: none !important;
      border-radius: 1rem !important;
      overflow: hidden !important;
      box-shadow: 0 4px 15px rgba(37, 99, 235, 0.05) !important;
      background: rgba(255, 255, 255, 0.98) !important;
    }

    .fc-col-header {
      background: linear-gradient(135deg, rgba(37, 99, 235, 0.08), rgba(14, 165, 233, 0.08)) !important;
      border-bottom: 1px solid rgba(37, 99, 235, 0.1) !important;
    }

    .fc-col-header-cell {
      padding: 1.25rem 0.75rem !important;
      border-right: none !important;
    }

    /* Add subtle separators only between weeks */
    .fc-daygrid-day[data-date*="-07"],
    .fc-daygrid-day[data-date*="-14"],
    .fc-daygrid-day[data-date*="-21"],
    .fc-daygrid-day[data-date*="-28"] {
      border-bottom: 1px solid rgba(37, 99, 235, 0.05) !important;
    }

    .fc-col-header-cell-cushion {
      color: #2563eb !important;
      font-weight: 700 !important;
      font-size: 0.875rem !important;
      text-transform: uppercase !important;
      letter-spacing: 0.75px !important;
    }

    /* Enhanced Day Cells - Clean Background */
    .fc-daygrid-day {
      background: rgba(255, 255, 255, 0.98) !important;
      border: none !important;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
      position: relative !important;
      min-height: 130px !important;
    }

    /* Remove table borders */
    .fc-scrollgrid-sync-table,
    .fc-col-header-row,
    .fc-daygrid-body,
    .fc-scrollgrid-sync-inner {
      border: none !important;
    }

    /* Clean grid lines */
    .fc-theme-standard td,
    .fc-theme-standard th {
      border: none !important;
    }

    .fc-daygrid-day::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, rgba(37, 99, 235, 0.02), rgba(14, 165, 233, 0.02));
      opacity: 0;
      transition: opacity 0.3s ease;
      pointer-events: none;
    }

    .fc-daygrid-day:hover {
      background: rgba(37, 99, 235, 0.05) !important;
      transform: scale(1.01) !important;
      box-shadow: 0 6px 20px rgba(37, 99, 235, 0.12) !important;
      border-radius: 0.5rem !important;
      z-index: 10 !important;
    }

    .fc-daygrid-day:hover::before {
      opacity: 1;
    }

    /* Day Numbers */
    .fc-daygrid-day-number {
      color: #4a5568 !important;
      font-weight: 700 !important;
      padding: 0.875rem !important;
      font-size: 1.1rem !important;
      transition: all 0.3s ease !important;
      position: relative !important;
      z-index: 2 !important;
    }

    .fc-daygrid-day:hover .fc-daygrid-day-number {
      color: #2563eb !important;
      transform: scale(1.15) !important;
    }

    /* Today Highlighting */
    .fc-day-today {
      background: linear-gradient(135deg, rgba(37, 99, 235, 0.12), rgba(14, 165, 233, 0.12)) !important;
      border: 2px solid rgba(37, 99, 235, 0.4) !important;
      box-shadow: 0 4px 20px rgba(37, 99, 235, 0.15) !important;
    }

    .fc-day-today .fc-daygrid-day-number {
      background: linear-gradient(135deg, #2563eb, #1d4ed8) !important;
      color: white !important;
      border-radius: 0.875rem !important;
      width: 2.75rem !important;
      height: 2.75rem !important;
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
      margin: 0.375rem !important;
      box-shadow: 0 6px 15px rgba(37, 99, 235, 0.4) !important;
      font-weight: 800 !important;
    }

    /* Event Styling */
    .fc-event {
      background: linear-gradient(135deg, #2563eb, #1d4ed8) !important;
      border: none !important;
      border-radius: 0.75rem !important;
      padding: 0.5rem 0.75rem !important;
      font-weight: 600 !important;
      font-size: 0.8rem !important;
      transition: all 0.3s ease !important;
      margin: 0.125rem !important;
      box-shadow: 0 2px 8px rgba(37, 99, 235, 0.2) !important;
    }

    .fc-event:hover {
      transform: scale(1.05) translateY(-1px) !important;
      box-shadow: 0 4px 15px rgba(37, 99, 235, 0.35) !important;
      background: linear-gradient(135deg, #1d4ed8, #1e40af) !important;
    }

    .fc-event-title {
      font-weight: 600 !important;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
    }

    /* More Events Link */
    .fc-more-link {
      background: linear-gradient(135deg, rgba(37, 99, 235, 0.1), rgba(14, 165, 233, 0.1)) !important;
      color: #2563eb !important;
      border: 1px solid rgba(37, 99, 235, 0.2) !important;
      border-radius: 0.5rem !important;
      padding: 0.25rem 0.5rem !important;
      font-size: 0.75rem !important;
      font-weight: 600 !important;
      text-decoration: none !important;
      transition: all 0.2s ease !important;
      margin: 0.125rem !important;
      display: inline-block !important;
    }

    .fc-more-link:hover {
      background: linear-gradient(135deg, rgba(37, 99, 235, 0.15), rgba(14, 165, 233, 0.15)) !important;
      color: #1d4ed8 !important;
      transform: scale(1.05) !important;
      box-shadow: 0 2px 8px rgba(37, 99, 235, 0.2) !important;
    }

    /* Popover for more events */
    .fc-popover {
      background: rgba(255, 255, 255, 0.98) !important;
      border: 1px solid rgba(37, 99, 235, 0.2) !important;
      border-radius: 0.75rem !important;
      box-shadow: 0 8px 25px rgba(37, 99, 235, 0.15) !important;
      backdrop-filter: blur(10px) !important;
    }

    .fc-popover-header {
      background: linear-gradient(135deg, rgba(37, 99, 235, 0.08), rgba(14, 165, 233, 0.08)) !important;
      border-bottom: 1px solid rgba(37, 99, 235, 0.1) !important;
      border-radius: 0.75rem 0.75rem 0 0 !important;
      padding: 0.75rem 1rem !important;
      font-weight: 700 !important;
      color: #2563eb !important;
    }

    /* Responsive Design */
    @media (max-width: 1024px) {
      .content-grid {
        grid-template-columns: 1fr;
      }

      .header-container {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
      }

      .header-right {
        width: 100%;
        justify-content: space-between;
      }
    }

    @media (max-width: 768px) {
      .compact-header {
        padding: 1rem;
      }

      .dashboard-content {
        padding: 1rem;
      }

      .stats-grid {
        grid-template-columns: 1fr;
        gap: 0.75rem;
      }

      .stat-card {
        padding: 1.25rem;
      }

      .header-stats {
        gap: 1rem;
      }

      .page-title {
        font-size: 1.5rem;
      }

      .fc-toolbar {
        flex-direction: column;
        gap: 0.5rem;
      }
    }

    @media (max-width: 640px) {
      .header-container {
        gap: 0.75rem;
      }

      .header-right {
        flex-direction: column;
        gap: 1rem;
        width: 100%;
      }

      .header-actions {
        justify-content: center;
      }

      .action-btn.primary span {
        display: none;
      }
    }
  </style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
  <script src="https://cdn.jsdelivr.net/npm/fullcalendar@5.11.3/main.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
  <script>
    document.addEventListener('DOMContentLoaded', function () {
      // Calendar initialization
      var calendarEl = document.getElementById('calendar');
      if (calendarEl) {
        var calendar = new FullCalendar.Calendar(calendarEl, {
          initialView: 'dayGridMonth',
          height: 'auto',
          selectable: true,
          eventDisplay: 'block',
          headerToolbar: {
            left: 'prev,next today',
            center: 'title',
            right: 'dayGridMonth,timeGridWeek,listWeek'
          },
          dayMaxEvents: 1,
          dayMaxEventRows: 1,
          moreLinkClick: 'popover',
          eventMaxStack: 1,
          <?php if(Route::has('calendar.events')): ?>
          events: "<?php echo e(route('calendar.events'), false); ?>",
          <?php endif; ?>
          eventClassNames: function (arg) {
            const eventType = arg.event.extendedProps.type || '';
            switch (eventType.toLowerCase()) {
              case 'college essay':
                return 'event-college-essay';
              case 'sat prep':
                return 'event-sat-prep';
              case 'act prep':
                return 'event-act-prep';
              default:
                return 'event-default';
            }
          },
          eventClick: function (info) {
            const bookingIds = info.event.extendedProps.booking_ids || [];
            if (bookingIds.length === 0) {
              Swal.fire({
                title: 'No Bookings',
                text: 'No bookings found for this type and date.',
                icon: 'info',
                showCloseButton: true,
                confirmButtonText: 'OK',
                background: 'rgba(255, 255, 255, 0.95)',
                backdrop: 'rgba(0, 0, 0, 0.4)',
                customClass: {
                  popup: 'swal2-modern'
                }
              });
              return;
            }

            <?php if(Route::has('calendar.bookings')): ?>
            fetch('<?php echo e(route('calendar.bookings'), false); ?>?type=' + encodeURIComponent(info.event.extendedProps.type) +
              '&date=' + info.event.start.toDateString() +
              '&booking_ids=' + bookingIds.join(','), {
              headers: {
                'Accept': 'application/json'
              }
            })
              .then(response => response.json())
              .then(data => {
                let html = '<div class="text-start">';
                if (data.length > 0) {
                  html += '<h5 class="mb-3" style="color: #2563eb;">' + info.event.extendedProps.type + ' Bookings</h5>';
                  html += '<ul class="list-unstyled">';
                  data.forEach(booking => {
                    html += `
                      <li class="mb-3 p-3 rounded" style="background: rgba(37, 99, 235, 0.1); border-left: 4px solid #2563eb;">
                        <strong>Booking:</strong> ${booking.name}<br>
                        <strong>Student:</strong> ${booking.student_name}<br>
                        <strong>Sessions:</strong> ${booking.sessions}<br>
                        <strong>Status:</strong> <span class="badge" style="background: ${
                          booking.status === 'New' ? '#3b82f6' :
                          booking.status === 'Confirmed' ? '#10b981' :
                          booking.status === 'Pending' ? '#f59e0b' : '#2563eb'
                        }; color: white; padding: 0.25rem 0.75rem; border-radius: 1rem; font-size: 0.75rem;">${booking.status}</span>
                      </li>
                    `;
                  });
                  html += '</ul>';
                } else {
                  html += '<p style="color: #6b7280;">No bookings found for this type and date.</p>';
                }
                html += '</div>';

                Swal.fire({
                  title: info.event.extendedProps.type + ' on ' + info.event.start.toLocaleDateString('en-US', {
                    weekday: 'long', month: 'long', day: 'numeric'
                  }),
                  html: html,
                  icon: 'info',
                  showCloseButton: true,
                  confirmButtonText: 'OK',
                  background: 'rgba(255, 255, 255, 0.95)',
                  backdrop: 'rgba(0, 0, 0, 0.4)',
                  customClass: {
                    popup: 'swal2-modern'
                  }
                });
              })
              .catch(error => {
                Swal.fire({
                  title: 'Error',
                  text: 'Failed to fetch booking details.',
                  icon: 'error',
                  showCloseButton: true,
                  confirmButtonText: 'OK',
                  background: 'rgba(255, 255, 255, 0.95)',
                  backdrop: 'rgba(0, 0, 0, 0.4)',
                  customClass: {
                    popup: 'swal2-modern'
                  }
                });
              });
            <?php endif; ?>
          },
          selectAllow: function (selectInfo) {
            return selectInfo.start.getDay() === 6;
          },
          dateClick: function (info) {
            if (info.date.getDay() !== 6) {
              Swal.fire({
                title: 'Invalid Selection',
                text: 'Only Saturdays are allowed for selection.',
                icon: 'warning',
                showCloseButton: true,
                confirmButtonText: 'OK',
                background: 'rgba(255, 255, 255, 0.95)',
                backdrop: 'rgba(0, 0, 0, 0.4)',
                customClass: {
                  popup: 'swal2-modern'
                }
              });
            }
          }
        });

        calendar.render();
      }

      // Enhanced header stats animations
      document.querySelectorAll('.mini-stat').forEach(stat => {
        const numberElement = stat.querySelector('.mini-stat-number');
        const target = parseInt(numberElement.textContent) || 0;

        // Animate counter on page load
        setTimeout(() => {
          animateCounter(numberElement, 0, target, 1500);
        }, 300);

        // Enhanced hover effects
        stat.addEventListener('mouseenter', function() {
          const icon = this.querySelector('.mini-stat-icon');
          icon.style.transform = 'scale(1.1) rotate(5deg)';
        });

        stat.addEventListener('mouseleave', function() {
          const icon = this.querySelector('.mini-stat-icon');
          icon.style.transform = 'scale(1) rotate(0deg)';
        });

        // Click effect
        stat.addEventListener('click', function() {
          this.style.transform = 'translateY(0px) scale(0.98)';
          setTimeout(() => {
            this.style.transform = 'translateY(-2px) scale(1.02)';
          }, 150);

          // Re-animate counter
          animateCounter(numberElement, 0, target, 800);
        });
      });

      // Enhanced interactive animations for stat cards
      document.querySelectorAll('.stat-card').forEach(card => {
        // Counter animation
        const numberElement = card.querySelector('.stat-number');
        const target = parseInt(numberElement.getAttribute('data-target')) || 0;

        // Animate counter on page load
        setTimeout(() => {
          animateCounter(numberElement, 0, target, 2000);
        }, 500);

        // Enhanced hover effects
        card.addEventListener('mouseenter', function() {
          this.style.transform = 'translateY(-12px) scale(1.03)';

          // Add subtle shake to icon
          const icon = this.querySelector('.stat-icon');
          icon.style.animation = 'none';
          setTimeout(() => {
            icon.style.animation = 'subtle-pulse 3s ease-in-out infinite, icon-shake 0.5s ease-in-out';
          }, 10);
        });

        card.addEventListener('mouseleave', function() {
          this.style.transform = 'translateY(0) scale(1)';

          // Reset icon animation
          const icon = this.querySelector('.stat-icon');
          icon.style.animation = 'subtle-pulse 3s ease-in-out infinite';
        });

        // Click effect
        card.addEventListener('click', function() {
          this.style.transform = 'translateY(-8px) scale(0.98)';
          setTimeout(() => {
            this.style.transform = 'translateY(-12px) scale(1.03)';
          }, 150);

          // Re-animate counter
          animateCounter(numberElement, 0, target, 1000);
        });
      });

      // Counter animation function
      function animateCounter(element, start, end, duration) {
        const startTime = performance.now();
        const range = end - start;

        function updateCounter(currentTime) {
          const elapsed = currentTime - startTime;
          const progress = Math.min(elapsed / duration, 1);

          // Easing function for smooth animation
          const easeOutQuart = 1 - Math.pow(1 - progress, 4);
          const current = Math.floor(start + (range * easeOutQuart));

          element.textContent = current;

          if (progress < 1) {
            requestAnimationFrame(updateCounter);
          } else {
            element.textContent = end;
          }
        }

        requestAnimationFrame(updateCounter);
      }

      // Smooth scroll animations
      const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
      };

      const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            entry.target.style.opacity = '1';
            entry.target.style.transform = 'translateY(0)';
          }
        });
      }, observerOptions);

      // Enhanced scroll animations
      document.querySelectorAll('.stat-card').forEach((el, index) => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(30px) scale(0.95)';
        el.style.transition = `opacity 0.8s ease ${index * 0.1}s, transform 0.8s ease ${index * 0.1}s`;
        observer.observe(el);
      });

      document.querySelectorAll('.activity-card, .calendar-card').forEach((el, index) => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(40px)';
        el.style.transition = `opacity 0.8s ease ${0.4 + index * 0.2}s, transform 0.8s ease ${0.4 + index * 0.2}s`;
        observer.observe(el);
      });

      // Stagger activity items animation
      document.querySelectorAll('.activity-item').forEach((el, index) => {
        el.style.opacity = '0';
        el.style.transform = 'translateX(-20px)';
        el.style.transition = `opacity 0.6s ease ${index * 0.05}s, transform 0.6s ease ${index * 0.05}s`;

        setTimeout(() => {
          el.style.opacity = '1';
          el.style.transform = 'translateX(0)';
        }, 1000 + index * 50);
      });

      // Add ripple effect to buttons
      document.querySelectorAll('.action-btn, .card-action').forEach(button => {
        button.addEventListener('click', function(e) {
          const ripple = document.createElement('span');
          const rect = this.getBoundingClientRect();
          const size = Math.max(rect.width, rect.height);
          const x = e.clientX - rect.left - size / 2;
          const y = e.clientY - rect.top - size / 2;

          ripple.style.width = ripple.style.height = size + 'px';
          ripple.style.left = x + 'px';
          ripple.style.top = y + 'px';
          ripple.classList.add('ripple');

          this.appendChild(ripple);

          setTimeout(() => {
            ripple.remove();
          }, 600);
        });
      });

      // Add ripple CSS
      const style = document.createElement('style');
      style.textContent = `
        .action-btn, .card-action {
          position: relative;
          overflow: hidden;
        }

        .ripple {
          position: absolute;
          border-radius: 50%;
          background: rgba(255, 255, 255, 0.6);
          transform: scale(0);
          animation: ripple-animation 0.6s linear;
          pointer-events: none;
        }

        @keyframes ripple-animation {
          to {
            transform: scale(4);
            opacity: 0;
          }
        }

        /* Custom Scrollbar */
        .activity-list::-webkit-scrollbar {
          width: 6px;
        }

        .activity-list::-webkit-scrollbar-track {
          background: rgba(37, 99, 235, 0.05);
          border-radius: 3px;
        }

        .activity-list::-webkit-scrollbar-thumb {
          background: linear-gradient(135deg, #2563eb, #0ea5e9);
          border-radius: 3px;
        }

        .activity-list::-webkit-scrollbar-thumb:hover {
          background: linear-gradient(135deg, #1d4ed8, #0284c7);
        }

        /* Enhanced Loading States */
        .stat-card, .activity-card, .calendar-card {
          position: relative;
        }

        .stat-card::after, .activity-card::after, .calendar-card::after {
          content: '';
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
          animation: loading-shimmer 2s infinite;
          pointer-events: none;
          opacity: 0;
        }

        @keyframes loading-shimmer {
          0% { left: -100%; }
          100% { left: 100%; }
        }

        /* Floating Elements */
        .dashboard-content::before {
          content: '';
          position: absolute;
          top: 10%;
          right: 10%;
          width: 100px;
          height: 100px;
          background: radial-gradient(circle, rgba(37, 99, 235, 0.1), transparent);
          border-radius: 50%;
          animation: float 6s ease-in-out infinite;
          pointer-events: none;
        }

        .dashboard-content::after {
          content: '';
          position: absolute;
          bottom: 20%;
          left: 5%;
          width: 60px;
          height: 60px;
          background: radial-gradient(circle, rgba(14, 165, 233, 0.1), transparent);
          border-radius: 50%;
          animation: float 8s ease-in-out infinite reverse;
          pointer-events: none;
        }

        @keyframes float {
          0%, 100% { transform: translateY(0px) rotate(0deg); }
          50% { transform: translateY(-20px) rotate(180deg); }
        }
      `;
      document.head.appendChild(style);
    });
  </script>
<?php $__env->stopPush(); ?>
<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\Zoffness_backend\resources\views/dashboard.blade.php ENDPATH**/ ?>