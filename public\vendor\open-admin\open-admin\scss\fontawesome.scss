$fa-font-path: '../../fontawesome/webfonts';
$fa-css-prefix: 'icon';
@import '../../fontawesome/scss/fontawesome';
@import '../../fontawesome/scss/solid';
@import '../../fontawesome/scss/brands';

[class^='icon-']:before,
[class*=' icon-']:before,
.far [class^='icon-']:before,
.far [class*=' icon-']:before {
    font-family: 'Font Awesome 5 Free';
    font-style: normal;
    font-weight: normal;
    speak: never;

    display: inline-block;
    text-decoration: inherit;
    width: 1em;
    margin-right: 0.2em;
    text-align: center;
    /* opacity: .8; */

    /* For safety - reset parent styles, that can break glyph codes*/
    font-variant: normal;
    text-transform: none;

    /* fix buttons height, for twitter bootstrap */
    line-height: 1em;

    /* Animation center compensation - margins should be symmetric */
    /* remove if not needed */
    margin-left: 0.2em;

    /* you can be more comfortable with increased icons size */
    /* font-size: 120%; */

    /* Font smoothing. That was taken from TWBS */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;

    /* Uncomment for 3D effect */
    /* text-shadow: 1px 1px 1px rgba(127, 127, 127, 0.3); */
}

.fas:before {
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
}

.fab:before {
    font-family: 'Font Awesome 5 Brands';
}

/* icons in dropdown menu's */
.dropdown-item i[class^='icon-']:before,
.dropdown-item i[class*=' icon-']:before {
    margin-right: 8px;
}
