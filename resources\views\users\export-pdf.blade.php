<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>{{ $title }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            color: #333;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #3B82F6;
            padding-bottom: 20px;
        }
        
        .header h1 {
            color: #1e293b;
            margin: 0;
            font-size: 28px;
            font-weight: bold;
        }
        
        .header p {
            color: #64748b;
            margin: 10px 0 0 0;
            font-size: 14px;
        }
        
        .stats {
            background: #f8fafc;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 25px;
            border-left: 4px solid #3B82F6;
        }
        
        .stats p {
            margin: 0;
            font-size: 14px;
            color: #475569;
        }
        
        .stats strong {
            color: #1e293b;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        
        th {
            background: #3B82F6;
            color: white;
            padding: 12px 8px;
            text-align: left;
            font-weight: bold;
            font-size: 12px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        td {
            padding: 10px 8px;
            border-bottom: 1px solid #e2e8f0;
            font-size: 11px;
        }
        
        tr:nth-child(even) {
            background-color: #f8fafc;
        }
        
        tr:hover {
            background-color: #f1f5f9;
        }
        
        .role-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 10px;
            font-weight: bold;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .role-admin {
            background: rgba(239, 68, 68, 0.1);
            color: #dc2626;
            border: 1px solid rgba(239, 68, 68, 0.2);
        }
        
        .role-tutor {
            background: rgba(16, 185, 129, 0.1);
            color: #059669;
            border: 1px solid rgba(16, 185, 129, 0.2);
        }
        
        .role-parent {
            background: rgba(59, 130, 246, 0.1);
            color: #2563eb;
            border: 1px solid rgba(59, 130, 246, 0.2);
        }
        
        .footer {
            margin-top: 30px;
            text-align: center;
            font-size: 10px;
            color: #9ca3af;
            border-top: 1px solid #e2e8f0;
            padding-top: 15px;
        }
        
        .no-data {
            text-align: center;
            padding: 40px;
            color: #6b7280;
            font-style: italic;
        }
        
        .sr-no {
            text-align: center;
            font-weight: bold;
        }
        
        .email {
            color: #3B82F6;
        }
        
        .date {
            color: #6b7280;
            font-size: 10px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>{{ $title }}</h1>
        <p>Generated on {{ $date }}</p>
    </div>
    
    <div class="stats">
        <p><strong>Total Users:</strong> {{ count($users) }}</p>
        <p><strong>Export Date:</strong> {{ date('F j, Y \a\t g:i A') }}</p>
    </div>
    
    @if(count($users) > 0)
        <table>
            <thead>
                <tr>
                    <th style="width: 8%;">Sr No</th>
                    <th style="width: 25%;">Name</th>
                    <th style="width: 35%;">Email</th>
                    <th style="width: 15%;">Role</th>
                    <th style="width: 17%;">Created Date</th>
                </tr>
            </thead>
            <tbody>
                @foreach($users as $index => $user)
                    <tr>
                        <td class="sr-no">{{ $index + 1 }}</td>
                        <td>{{ $user->username }}</td>
                        <td class="email">{{ $user->email }}</td>
                        <td>
                            <span class="role-badge role-{{ $user->role }}">
                                {{ ucfirst($user->role) }}
                            </span>
                        </td>
                        <td class="date">
                            {{ $user->created_at ? $user->created_at->format('M j, Y') : 'N/A' }}
                        </td>
                    </tr>
                @endforeach
            </tbody>
        </table>
    @else
        <div class="no-data">
            <p>No users found to export.</p>
        </div>
    @endif
    
    <div class="footer">
        <p>This report was generated automatically by the User Management System.</p>
        <p>© {{ date('Y') }} Zoffness College Prep - All rights reserved.</p>
    </div>
</body>
</html>
