/*-------------------------------------------------------*/
/* content header */
/*-------------------------------------------------------*/

.content-header {
    h1 {
        font-size: 1.6rem;
        margin: 0;
        float: left;
    }

    small,
    .breadcrumb-item {
        font-size: 0.9rem;
        color: #777;
        a {
            text-decoration: none;
            &:hover {
                text-decoration: underline;
            }
        }
    }

    .breadcrumb-nav {
        float: right;
        padding-right: 3px;
        .icon-home {
            margin-right: 0.5em;
        }
    }
    @media screen and (max-width: 1000px) {
        .breadcrumb-nav {
            clear: both;
            float: left;
            width: 100%;
            margin-top: 0.75rem;
            border-top: 1px solid $border-color;
        }
    }
}

/*-------------------------------------------------------*/
/* card  */
/*-------------------------------------------------------*/

.card {
    border: 0;
    box-shadow: 0 0 1rem -0.5rem rgba(0, 0, 0, 0.2);
    border-radius: 0.3rem;
    margin-bottom: 1.5rem;

    .card-header {
        background: #fff;
        padding: 0.75rem;

        /*
        &:first-child {
            border-radius: inherit;
            border-bottom-left-radius: 0;
            border-bottom-right-radius: 0;

        }
*/
        &.no-border {
            border: 0;
        }

        .card-title {
            float: left;
            font-size: 1.2rem;
            line-height: 2rem;
            margin-bottom: 0;
        }
        .card-tools {
            float: right;
        }

        .btn-box-tool {
            padding: 5px;
            font-size: 0.8rem;
            background: transparent;
            color: $light-elements;
            text-align: center;

            &.collapsed .fa::before {
                content: '\f067';
            }

            .fa {
                margin-right: 0;
            }
        }
    }
}
.card {
    .card {
        margin: 0;
        box-shadow: none;
    }
}

/*---------------------------------------------------------*/
/* breadcrumb */
/*---------------------------------------------------------*/

.breadcrumb {
    padding: 10px 0;
}

.breadcrumb a {
    color: var(--text-color);
}

/*---------------------------------------------------------*/
/* button styles */
/*---------------------------------------------------------*/

@media (max-width: 700px) {
    .hidden-small {
        display: none;
    }
}

.btn .fa {
    margin-right: 5px;
}

/*---------------------------------------------------------*/
/* user menu */
/*---------------------------------------------------------*/

.user-menu {
    border-radius: 0;
    padding: 0;
}

.user-menu .user-header {
    background: var(--menu-bg);
    color: var(--menu-text);
}

/*---------------------------------------------------------*/
/* horizontal forms */
/*---------------------------------------------------------*/

.form-horizontal {
    .row {
        margin-bottom: 0.75rem;
    }

    table {
        .form-label {
            text-align: left;
        }
    }

    .form-label {
        padding-top: 0.3rem;
        margin-bottom: 0;
        text-align: right;
    }
    .form-check {
        padding-top: 0.3rem;
    }
}
@media (max-width: 576px) {
    .form-group {
        padding: 0 1rem;
    }

    .form-horizontal {
        .form-label {
            text-align: left;
        }
    }
}

.alert {
    button.close {
        -webkit-appearance: none;
        padding: 0;
        cursor: pointer;
        background: 0 0;
        border: 0;
        float: right;
        font-size: 21px;
        font-weight: 700;
        line-height: 1;
        color: #000;
        text-shadow: 0 1px 0 #fff;
        opacity: 0.3;
    }
    p:last-child {
        margin-bottom: 0;
    }
}

/*---------------------------------------------------------*/
/* horizontal forms */
/*---------------------------------------------------------*/

#app > .content {
    margin-bottom: 64px;
}

/*---------------------------------------------------------*/
/* context menu */
/*---------------------------------------------------------*/

.context-menu {
    position: absolute;
    display: none;

    ul.dropdown-menu {
        list-style: none;
        display: block;
        min-width: none;
    }
}

/*---------------------------------------------------------*/
/* additional buttons styles */
/*---------------------------------------------------------*/

.btn-group > .dropdown-toggle,
.btn-group > .btn-group.dropdown-toggle {
    border-left: 1px solid rgba(255, 255, 255, 0.2);
    min-width: 2.2em;
}

/*---------------------------------------------------------*/
/* show / display styles */
/*---------------------------------------------------------*/

.show-value {
    padding-top: 0.3rem;
}

.mailbox-arttachment {
    border: 1px solid $card-border-color;
    max-width: 360px;

    .mailbox-attachment-icon {
        padding: 20px;
        font-size: 3rem;
        text-align: center;
    }
    .card-body {
        background-color: $card-border-color;
    }
}

/*---------------------------------------------------------*/
/* modal overwrites */
/*---------------------------------------------------------*/

.modal .form {
    padding-bottom: 0;
}

/*---------------------------------------------------------*/
/* widgets */
/*---------------------------------------------------------*/

.card-tools .box-tool-minimize.collapsed .icon-minus:before {
    content: '';
    font-weight: bold;
}

.collapse-group .card {
    border-radius: 0;
}

.collapse-group .card:first-child {
    border-top-left-radius: 0.3rem;
    border-top-right-radius: 0.3rem;
}

.collapse-group .card:last-child {
    border-bottom-left-radius: 0.3rem;
    border-bottom-right-radius: 0.3rem;
    .card-header.collapsed {
        border-bottom-left-radius: 0.3rem;
        border-bottom-right-radius: 0.3rem;
    }
}

.info-box {
    padding: 0;
    .inner {
        h3 {
            margin: 0;
        }
        text-align: center;
        padding-left: 2rem;
    }
    .icon {
        font-size: 3rem;
    }
}
