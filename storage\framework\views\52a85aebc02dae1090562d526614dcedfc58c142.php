

<?php $__env->startSection('content'); ?>
<div class="modern-users-section">
    <!-- Enhanced Header -->
    <div class="users-header">
        <div class="header-content">
            <div class="header-left">
                <h1 class="section-title">
                    <i class="bx bx-group"></i>
                    User Management
                </h1>
                <p class="section-subtitle">Manage and organize your users efficiently</p>
            </div>
            <div class="header-actions">
                <button class="action-btn secondary" id="refreshUsers" title="Refresh">
                    <i class="bx bx-refresh"></i>
                </button>
                <a href="<?php echo e(route('users.create'), false); ?>" class="action-btn primary">
                    <i class="bx bx-plus"></i>
                    <span>Add User</span>
                </a>
            </div>
        </div>
    </div>

    <!-- Success Alert -->
    <?php if(session('success')): ?>
        <div class="alert alert-success alert-dismissible fade show modern-alert" role="alert">
            <i class="bx bx-check-circle me-2"></i>
            <?php echo e(session('success'), false); ?>

            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <!-- Enhanced Controls -->
    <div class="users-controls">
        <div class="controls-left">
            <div class="search-container">
                <i class="bx bx-search search-icon"></i>
                <input type="text" id="userSearch" class="search-input" placeholder="Search users...">
            </div>
            <div class="filter-container">
                <select id="roleFilter" class="filter-select">
                    <option value="">All Roles</option>
                    <option value="admin">Admin</option>
                    <option value="tutor">Tutor</option>
                    <option value="parent">Parent</option>
                </select>
            </div>
        </div>
        <div class="controls-right">
            <div class="export-buttons">
                <button class="export-btn excel" id="exportExcel">
                    <i class="bx bx-file"></i>
                    Excel
                </button>
                <button class="export-btn pdf" id="exportPDF">
                    <i class="bx bx-file-pdf"></i>
                    PDF
                </button>
            </div>
            <div class="view-toggle">
                <button class="view-btn active" data-view="cards" title="Card View">
                    <i class="bx bx-grid-alt"></i>
                </button>
                <button class="view-btn" data-view="table" title="Table View">
                    <i class="bx bx-list-ul"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- Users Content -->
    <div class="users-content">
        <!-- Card View -->
        <div class="users-grid" id="cardView">
            <?php $__empty_1 = true; $__currentLoopData = $users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                <div class="user-card" data-role="<?php echo e($user->role, false); ?>" data-search="<?php echo e(strtolower($user->username . ' ' . $user->email . ' ' . $user->role), false); ?>">
                    <div class="card-header">
                        <div class="user-avatar">
                            <i class="bx bx-user"></i>
                            <span class="status-dot <?php echo e($user->role, false); ?>"></span>
                        </div>
                        <div class="card-actions">
                            <div class="dropdown">
                                <button class="action-menu-btn" data-bs-toggle="dropdown">
                                    <i class="bx bx-dots-vertical-rounded"></i>
                                </button>
                                <ul class="dropdown-menu dropdown-menu-end">
                                    <li>
                                        <a class="dropdown-item" href="<?php echo e(route('users.show', $user->id), false); ?>">
                                            <i class="bx bx-show me-2"></i>View Details
                                        </a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item" href="<?php echo e(route('users.edit', $user->id), false); ?>">
                                            <i class="bx bx-edit me-2"></i>Edit User
                                        </a>
                                    </li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li>
                                        <button class="dropdown-item text-danger" onclick="deleteUser(<?php echo e($user->id, false); ?>)">
                                            <i class="bx bx-trash me-2"></i>Delete User
                                        </button>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <h3 class="user-name"><?php echo e($user->username, false); ?></h3>
                        <p class="user-email"><?php echo e($user->email, false); ?></p>
                        <div class="user-role">
                            <span class="role-badge <?php echo e($user->role, false); ?>">
                                <i class="bx <?php echo e($user->role == 'admin' ? 'bx-crown' : ($user->role == 'tutor' ? 'bx-chalkboard' : 'bx-user-circle'), false); ?>"></i>
                                <?php echo e(ucfirst($user->role), false); ?>

                            </span>
                        </div>
                    </div>
                    <div class="card-footer">
                        <div class="quick-actions">
                            <a href="<?php echo e(route('users.show', $user->id), false); ?>" class="quick-btn view" title="View">
                                <i class="bx bx-show"></i>
                            </a>
                            <a href="<?php echo e(route('users.edit', $user->id), false); ?>" class="quick-btn edit" title="Edit">
                                <i class="bx bx-edit"></i>
                            </a>
                            <button class="quick-btn delete" onclick="deleteUser(<?php echo e($user->id, false); ?>)" title="Delete">
                                <i class="bx bx-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                <div class="empty-state">
                    <div class="empty-icon">
                        <i class="bx bx-user-x"></i>
                    </div>
                    <h3>No Users Found</h3>
                    <p>Start by creating your first user</p>
                    <a href="<?php echo e(route('users.create'), false); ?>" class="action-btn primary">
                        <i class="bx bx-plus"></i>
                        Create User
                    </a>
                </div>
            <?php endif; ?>
        </div>

        <!-- Table View (Hidden by default) -->
        <div class="table-container" id="tableView" style="display: none;">
            <div class="table-responsive">
                <table id="usersTable" class="table table-striped table-hover datatable">
                    <thead>
                        <tr>
                            <th>Sr No</th>
                            <th>Name</th>
                            <th>Email</th>
                            <th>Role</th>
                            <th class="text-center no-export">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__empty_1 = true; $__currentLoopData = $users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <tr>
                                <td><?php echo e($index + 1, false); ?></td>
                                <td><?php echo e($user->username, false); ?></td>
                                <td><?php echo e($user->email, false); ?></td>
                                <td>
                                    <span class="badge bg-label-<?php echo e($user->role == 'admin' ? 'primary' : ($user->role == 'tutor' ? 'success' : 'info'), false); ?>">
                                        <?php echo e(ucfirst($user->role), false); ?>

                                    </span>
                                </td>
                                <td class="text-center">
                                    <div class="d-inline-flex">
                                        <a href="<?php echo e(route('users.show', $user->id), false); ?>" class="btn btn-sm btn-icon btn-action-view" title="View">
                                            <i class="bx bx-show"></i>
                                        </a>
                                        <a href="<?php echo e(route('users.edit', $user->id), false); ?>" class="btn btn-sm btn-icon btn-action-edit" title="Edit">
                                            <i class="bx bx-edit"></i>
                                        </a>
                                        <button type="button" class="btn btn-sm btn-icon btn-action-delete"
                                                onclick="deleteUser(<?php echo e($user->id, false); ?>)" title="Delete">
                                            <i class="bx bx-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <tr>
                                <td colspan="5" class="text-center py-5">
                                    <div class="text-muted">
                                        <img src="https://cdn-icons-png.flaticon.com/512/4076/4076549.png" alt="No Data" width="80" class="mb-3 opacity-50">
                                        <p class="mb-0">No users found.</p>
                                    </div>
                                </td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<script>
  function deleteUser(userID) {
    Swal.fire({
        title: 'Are you sure?',
        text: 'Do you want to delete this user?',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: 'Yes, delete it!',
        cancelButtonText: 'No, cancel!',
        reverseButtons: true
    }).then((result) => {
        if (result.isConfirmed) {
            $.ajax({
                url: '<?php echo e(route('users.delete', ''), false); ?>/' + userID,
                type: 'DELETE',
                headers: {
                    'X-CSRF-TOKEN': '<?php echo e(csrf_token(), false); ?>'
                },
                success: function(response) {
                    if (response.success) {
                        Swal.fire(
                            'Deleted!',
                            response.message || 'User deleted successfully.',
                            'success'
                        ).then(() => {
                            location.reload();
                        });
                    } else {
                        Swal.fire(
                            'Error!',
                            response.message || 'Failed to delete user.',
                            'error'
                        );
                    }
                },
                error: function(xhr) {
                    Swal.fire(
                        'Error!',
                        xhr.responseJSON?.message || 'An unexpected error occurred.',
                        'error'
                    );
                }
            });
        }
    });
}
</script>

<!-- Modern Users Section Styles -->
<style>
/* Simple Hover Effects Only - No Animations */

/* Modern Users Section */
.modern-users-section {
    padding: 1.5rem;
    background: #f8fafc;
    min-height: 100vh;
}

/* Enhanced Header */
.users-header {
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 1rem;
    padding: 2rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.users-header:hover {
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08);
    border-color: #cbd5e1;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-left {
    flex: 1;
}

.section-title {
    font-size: 1.75rem;
    font-weight: 700;
    color: #1e293b;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.section-title i {
    color: #3b82f6;
    font-size: 2rem;
    transition: all 0.3s ease;
}

.section-title i:hover {
    color: #1d4ed8;
}

.section-subtitle {
    color: #64748b;
    margin: 0.5rem 0 0 0;
    font-size: 0.95rem;
}

.header-actions {
    display: flex;
    gap: 0.75rem;
    align-items: center;
}

.action-btn {
    padding: 0.75rem 1.25rem;
    border-radius: 0.75rem;
    border: none;
    font-weight: 500;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
}

.action-btn.primary {
    background: #3b82f6;
    color: white;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.2);
}

.action-btn.primary:hover {
    background: #1d4ed8;
    box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3);
    color: white;
}

.action-btn.secondary {
    background: #f8fafc;
    color: #64748b;
    border: 1px solid #e2e8f0;
    padding: 0.75rem;
}

.action-btn.secondary:hover {
    background: #f1f5f9;
    color: #475569;
    border-color: #cbd5e1;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

/* Modern Alert */
.modern-alert {
    background: rgba(34, 197, 94, 0.1);
    border: 1px solid rgba(34, 197, 94, 0.2);
    border-radius: 0.75rem;
    color: #166534;
    margin-bottom: 1.5rem;
    transition: all 0.3s ease;
}

.modern-alert:hover {
    background: rgba(34, 197, 94, 0.15);
    border-color: rgba(34, 197, 94, 0.3);
}

/* Enhanced Controls */
.users-controls {
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 1rem;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 1rem;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.users-controls:hover {
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08);
    border-color: #cbd5e1;
}

.controls-left {
    display: flex;
    gap: 1rem;
    align-items: center;
    flex: 1;
}

.controls-right {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.search-container {
    position: relative;
    flex: 1;
    max-width: 300px;
}

.search-icon {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #9ca3af;
    font-size: 1.125rem;
}

.search-input {
    width: 100%;
    padding: 0.75rem 1rem 0.75rem 2.75rem;
    border: 1px solid rgba(226, 232, 240, 0.8);
    border-radius: 0.75rem;
    background: rgba(248, 250, 252, 0.8);
    font-size: 0.875rem;
    transition: all 0.2s ease;
}

.search-input:focus {
    outline: none;
    border-color: #3b82f6;
    background: white;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.filter-select {
    padding: 0.75rem 1rem;
    border: 1px solid rgba(226, 232, 240, 0.8);
    border-radius: 0.75rem;
    background: rgba(248, 250, 252, 0.8);
    font-size: 0.875rem;
    color: #64748b;
    cursor: pointer;
    transition: all 0.2s ease;
}

.filter-select:focus {
    outline: none;
    border-color: #3b82f6;
    background: white;
}

.export-buttons {
    display: flex;
    gap: 0.5rem;
}

.export-btn {
    padding: 0.75rem 1rem;
    border: 1px solid;
    border-radius: 0.75rem;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: white;
}

.export-btn.excel {
    color: #059669;
    border-color: rgba(5, 150, 105, 0.3);
}

.export-btn.excel:hover {
    background: rgba(5, 150, 105, 0.1);
    transform: translateY(-1px);
}

.export-btn.pdf {
    color: #dc2626;
    border-color: rgba(220, 38, 38, 0.3);
}

.export-btn.pdf:hover {
    background: rgba(220, 38, 38, 0.1);
    transform: translateY(-1px);
}

.view-toggle {
    display: flex;
    background: rgba(248, 250, 252, 0.8);
    border-radius: 0.75rem;
    padding: 0.25rem;
    border: 1px solid rgba(226, 232, 240, 0.8);
}

.view-btn {
    padding: 0.5rem 0.75rem;
    border: none;
    background: transparent;
    border-radius: 0.5rem;
    color: #64748b;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 1rem;
}

.view-btn.active {
    background: #3b82f6;
    color: white;
    box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
}

.view-btn:hover:not(.active) {
    background: rgba(59, 130, 246, 0.1);
    color: #3b82f6;
}

/* Users Grid */
.users-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 1.5rem;
    padding: 0.5rem;
}

.user-card {
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 1rem;
    overflow: hidden;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.user-card:hover {
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
    border-color: #3b82f6;
    transform: translateY(-2px);
}

.user-card .card-header {
    padding: 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #f8fafc;
    border-bottom: 1px solid #e2e8f0;
}

.user-avatar {
    width: 3.5rem;
    height: 3.5rem;
    background: #3b82f6;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.2);
}

.user-avatar:hover {
    background: #1d4ed8;
    box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3);
}

.user-avatar i {
    color: white;
    font-size: 1.5rem;
}

.status-dot {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 1rem;
    height: 1rem;
    border-radius: 50%;
    border: 2px solid white;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

.status-dot.admin {
    background: #8b5cf6;
}

.status-dot.tutor {
    background: #10b981;
}

.status-dot.parent {
    background: #f59e0b;
}

.action-menu-btn {
    width: 2rem;
    height: 2rem;
    border: none;
    background: rgba(107, 114, 128, 0.1);
    border-radius: 0.5rem;
    color: #6b7280;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.action-menu-btn:hover {
    background: rgba(107, 114, 128, 0.2);
    color: #374151;
}

.user-card .card-body {
    padding: 1.5rem;
}

.user-name {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1e293b;
    margin: 0 0 0.5rem 0;
    transition: color 0.3s ease;
}

.user-card:hover .user-name {
    color: #3b82f6;
}

.user-email {
    color: #64748b;
    font-size: 0.875rem;
    margin: 0 0 1rem 0;
    transition: color 0.3s ease;
}

.user-card:hover .user-email {
    color: #475569;
}

.role-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 0.75rem;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.3s ease;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
}

.role-badge:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.role-badge.admin {
    background: rgba(139, 92, 246, 0.1);
    color: #7c3aed;
}

.role-badge.tutor {
    background: rgba(16, 185, 129, 0.1);
    color: #059669;
}

.role-badge.parent {
    background: rgba(245, 158, 11, 0.1);
    color: #d97706;
}

.user-card .card-footer {
    padding: 1.25rem 1.5rem;
    background: #f8fafc;
    border-top: 1px solid #e2e8f0;
}

.quick-actions {
    display: flex;
    gap: 0.75rem;
    justify-content: center;
}

.quick-btn {
    width: 2.25rem;
    height: 2.25rem;
    border: none;
    border-radius: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    font-size: 1rem;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
}

.quick-btn.view {
    background: rgba(59, 130, 246, 0.1);
    color: #3b82f6;
}

.quick-btn.view:hover {
    background: rgba(59, 130, 246, 0.2);
    color: #1d4ed8;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.2);
}

.quick-btn.edit {
    background: rgba(245, 158, 11, 0.1);
    color: #d97706;
}

.quick-btn.edit:hover {
    background: rgba(245, 158, 11, 0.2);
    color: #b45309;
    box-shadow: 0 2px 8px rgba(245, 158, 11, 0.2);
}

.quick-btn.delete {
    background: rgba(239, 68, 68, 0.1);
    color: #ef4444;
}

.quick-btn.delete:hover {
    background: rgba(239, 68, 68, 0.2);
    color: #dc2626;
    box-shadow: 0 2px 8px rgba(239, 68, 68, 0.2);
}

/* Empty State */
.empty-state {
    grid-column: 1 / -1;
    text-align: center;
    padding: 4rem;
    color: #64748b;
}

.empty-icon {
    font-size: 4rem;
    color: #cbd5e1;
    margin-bottom: 1.5rem;
}

.empty-state h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #475569;
    margin-bottom: 0.5rem;
}

.empty-state p {
    margin-bottom: 2rem;
}

/* Table Container */
.table-container {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(226, 232, 240, 0.8);
    border-radius: 1rem;
    padding: 1.5rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

/* Responsive Design */
@media (max-width: 768px) {
    .modern-users-section {
        padding: 1rem;
    }

    .header-content {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }

    .users-controls {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }

    .controls-left {
        flex-direction: column;
        gap: 1rem;
    }

    .controls-right {
        justify-content: space-between;
    }

    .search-container {
        max-width: none;
    }

    .users-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
}

@media (max-width: 480px) {
    .export-buttons {
        flex-direction: column;
        width: 100%;
    }

    .export-btn {
        justify-content: center;
    }

    .action-btn span {
        display: none;
    }
}
</style>

<?php $__env->startPush('scripts'); ?>
<script>
    $(document).ready(function() {
        // Initialize DataTable for table view
        let dataTable = null;

        function initializeDataTable() {
            if (dataTable) {
                dataTable.destroy();
            }
            dataTable = initDataTable('usersTable', {
                order: [[1, 'asc']],
                columnDefs: [
                    { orderable: false, targets: [0, 4] },
                    { className: 'text-center', targets: [0, 3, 4] }
                ]
            });
        }

        // Simple loading overlay
        if (!$('.loading-overlay').length) {
            $('body').append(`
                <div class="loading-overlay">
                    <div class="loading-spinner">Loading...</div>
                </div>
            `);
        }

        // Simple View Toggle Functionality
        $('.view-btn').on('click', function() {
            const view = $(this).data('view');

            $('.view-btn').removeClass('active');
            $(this).addClass('active');

            if (view === 'cards') {
                $('#tableView').hide();
                $('#cardView').show();
            } else {
                $('#cardView').hide();
                $('#tableView').show();
                setTimeout(initializeDataTable, 100);
            }
        });

        // Simple Search Functionality for Card View
        $('#userSearch').on('input', function() {
            const searchTerm = $(this).val().toLowerCase();

            $('.user-card').each(function() {
                const searchData = $(this).data('search');
                if (searchData.includes(searchTerm)) {
                    $(this).show();
                } else {
                    $(this).hide();
                }
            });
        });

        // Role Filter Functionality for Card View
        $('#roleFilter').on('change', function() {
            const selectedRole = $(this).val();

            $('.user-card').each(function() {
                const cardRole = $(this).data('role');
                if (selectedRole === '' || cardRole === selectedRole) {
                    $(this).show();
                } else {
                    $(this).hide();
                }
            });
        });

        // Export Functionality
        $('#exportExcel').on('click', function() {
            if (dataTable) {
                dataTable.button('.buttons-excel').trigger();
            } else {
                // For card view, create temporary table and export
                exportCardData('excel');
            }
        });

        $('#exportPDF').on('click', function() {
            if (dataTable) {
                dataTable.button('.buttons-pdf').trigger();
            } else {
                exportCardData('pdf');
            }
        });

        function exportCardData(format) {
            // Create temporary table for export
            const tempTable = $('<table>').addClass('d-none');
            const thead = $('<thead>').append(
                $('<tr>').append(
                    '<th>Sr No</th><th>Name</th><th>Email</th><th>Role</th>'
                )
            );
            const tbody = $('<tbody>');

            $('.user-card:visible').each(function(index) {
                const name = $(this).find('.user-name').text();
                const email = $(this).find('.user-email').text();
                const role = $(this).find('.role-badge').text().trim();

                tbody.append(
                    $('<tr>').append(
                        `<td>${index + 1}</td><td>${name}</td><td>${email}</td><td>${role}</td>`
                    )
                );
            });

            tempTable.append(thead).append(tbody);
            $('body').append(tempTable);

            // Initialize temporary DataTable for export
            const tempDT = tempTable.DataTable({
                dom: 'Bfrtip',
                buttons: [format]
            });

            tempDT.button(`.buttons-${format}`).trigger();

            // Clean up
            setTimeout(() => {
                tempDT.destroy();
                tempTable.remove();
            }, 1000);
        }

        // Refresh Users
        $('#refreshUsers').on('click', function() {
            location.reload();
        });

        // Simple Delete Function
        window.deleteUser = function(userID) {
            Swal.fire({
                title: 'Are you sure?',
                text: 'Do you want to delete this user?',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: 'Yes, delete it!',
                cancelButtonText: 'No, cancel!',
                reverseButtons: true
            }).then((result) => {
                if (result.isConfirmed) {
                    $('.loading-overlay').addClass('show');

                    $.ajax({
                        url: '<?php echo e(route('users.delete', ''), false); ?>/' + userID,
                        type: 'DELETE',
                        headers: {
                            'X-CSRF-TOKEN': '<?php echo e(csrf_token(), false); ?>'
                        },
                        success: function(response) {
                            $('.loading-overlay').removeClass('show');

                            if (response.success) {
                                Swal.fire({
                                    title: 'Deleted!',
                                    text: response.message || 'User deleted successfully.',
                                    icon: 'success'
                                }).then(() => {
                                    location.reload();
                                });
                            } else {
                                Swal.fire({
                                    title: 'Error!',
                                    text: response.message || 'Failed to delete user.',
                                    icon: 'error'
                                });
                            }
                        },
                        error: function(xhr) {
                            $('.loading-overlay').removeClass('show');
                            Swal.fire({
                                title: 'Error!',
                                text: xhr.responseJSON?.message || 'An unexpected error occurred.',
                                icon: 'error'
                            });
                        }
                    });
                }
            });
        };

        // Initialize with card view
        $('#cardView').show();
        $('#tableView').hide();
    });

    // Simple CSS for loading overlay
    const simpleCSS = `
        <style>
        /* Loading Overlay */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.9);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .loading-overlay.show {
            opacity: 1;
            visibility: visible;
        }

        .loading-spinner {
            color: #3b82f6;
            font-weight: 600;
            font-size: 1rem;
        }
        </style>
    `;

    $('head').append(simpleCSS);
</script>
<?php $__env->stopPush(); ?>

<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\Zoffness_backend\resources\views/users/list.blade.php ENDPATH**/ ?>