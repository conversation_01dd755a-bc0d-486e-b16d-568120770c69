

<?php $__env->startSection('content'); ?>
<div class="modern-users-section">
    <!-- Enhanced Header -->
    <div class="users-header">
        <div class="header-content">
            <div class="header-left">
                <h1 class="section-title">
                    <i class="bx bx-group"></i>
                    User Management
                </h1>
                <p class="section-subtitle">Manage and organize your users efficiently</p>
            </div>
            <div class="header-actions">
                <button class="action-btn secondary" id="refreshUsers" title="Refresh">
                    <i class="bx bx-refresh"></i>
                </button>
                <a href="<?php echo e(route('users.create'), false); ?>" class="action-btn primary">
                    <i class="bx bx-plus"></i>
                    <span>Add User</span>
                </a>
            </div>
        </div>
    </div>

    <!-- Success Alert -->
    <?php if(session('success')): ?>
        <div class="alert alert-success alert-dismissible fade show modern-alert" role="alert">
            <i class="bx bx-check-circle me-2"></i>
            <?php echo e(session('success'), false); ?>

            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <!-- Enhanced Controls -->
    <div class="users-controls">
        <div class="controls-left">
            <div class="search-container">
                <i class="bx bx-search search-icon"></i>
                <input type="text" id="userSearch" class="search-input" placeholder="Search users...">
            </div>
            <div class="filter-container">
                <select id="roleFilter" class="filter-select">
                    <option value="">All Roles</option>
                    <option value="admin">Admin</option>
                    <option value="tutor">Tutor</option>
                    <option value="parent">Parent</option>
                </select>
            </div>
        </div>
        <div class="controls-right">
            <div class="export-buttons">
                <button class="export-btn excel" id="exportExcel">
                    <i class="bx bx-file"></i>
                    Excel
                </button>
                <button class="export-btn pdf" id="exportPDF">
                    <i class="bx bx-file-pdf"></i>
                    PDF
                </button>
            </div>
            <div class="view-toggle">
                <button class="view-btn active" data-view="cards" title="Card View">
                    <i class="bx bx-grid-alt"></i>
                </button>
                <button class="view-btn" data-view="table" title="Table View">
                    <i class="bx bx-list-ul"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- Users Content -->
    <div class="users-content">
        <!-- Card View -->
        <div class="users-grid" id="cardView">
            <?php $__empty_1 = true; $__currentLoopData = $users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                <div class="user-card" data-role="<?php echo e($user->role, false); ?>" data-search="<?php echo e(strtolower($user->username . ' ' . $user->email . ' ' . $user->role), false); ?>">
                    <div class="card-header">
                        <div class="user-avatar">
                            <i class="bx bx-user"></i>
                            <span class="status-dot <?php echo e($user->role, false); ?>"></span>
                        </div>
                        <div class="card-actions">
                            <div class="dropdown">
                                <button class="action-menu-btn" data-bs-toggle="dropdown">
                                    <i class="bx bx-dots-vertical-rounded"></i>
                                </button>
                                <ul class="dropdown-menu dropdown-menu-end">
                                    <li>
                                        <a class="dropdown-item" href="<?php echo e(route('users.show', $user->id), false); ?>">
                                            <i class="bx bx-show me-2"></i>View Details
                                        </a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item" href="<?php echo e(route('users.edit', $user->id), false); ?>">
                                            <i class="bx bx-edit me-2"></i>Edit User
                                        </a>
                                    </li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li>
                                        <button class="dropdown-item text-danger" onclick="deleteUser(<?php echo e($user->id, false); ?>)">
                                            <i class="bx bx-trash me-2"></i>Delete User
                                        </button>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <h3 class="user-name"><?php echo e($user->username, false); ?></h3>
                        <p class="user-email"><?php echo e($user->email, false); ?></p>
                        <div class="user-role">
                            <span class="role-badge <?php echo e($user->role, false); ?>">
                                <i class="bx <?php echo e($user->role == 'admin' ? 'bx-crown' : ($user->role == 'tutor' ? 'bx-chalkboard' : 'bx-user-circle'), false); ?>"></i>
                                <?php echo e(ucfirst($user->role), false); ?>

                            </span>
                        </div>
                    </div>
                    <div class="card-footer">
                        <div class="quick-actions">
                            <a href="<?php echo e(route('users.show', $user->id), false); ?>" class="quick-btn view" title="View">
                                <i class="bx bx-show"></i>
                            </a>
                            <a href="<?php echo e(route('users.edit', $user->id), false); ?>" class="quick-btn edit" title="Edit">
                                <i class="bx bx-edit"></i>
                            </a>
                            <button class="quick-btn delete" onclick="deleteUser(<?php echo e($user->id, false); ?>)" title="Delete">
                                <i class="bx bx-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                <div class="empty-state">
                    <div class="empty-icon">
                        <i class="bx bx-user-x"></i>
                    </div>
                    <h3>No Users Found</h3>
                    <p>Start by creating your first user</p>
                    <a href="<?php echo e(route('users.create'), false); ?>" class="action-btn primary">
                        <i class="bx bx-plus"></i>
                        Create User
                    </a>
                </div>
            <?php endif; ?>
        </div>

        <!-- Table View (Hidden by default) -->
        <div class="table-container" id="tableView" style="display: none;">
            <div class="table-responsive">
                <table id="usersTable" class="table table-striped table-hover datatable">
                    <thead>
                        <tr>
                            <th>Sr No</th>
                            <th>Name</th>
                            <th>Email</th>
                            <th>Role</th>
                            <th class="text-center no-export">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__empty_1 = true; $__currentLoopData = $users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <tr>
                                <td><?php echo e($index + 1, false); ?></td>
                                <td><?php echo e($user->username, false); ?></td>
                                <td><?php echo e($user->email, false); ?></td>
                                <td>
                                    <span class="badge bg-label-<?php echo e($user->role == 'admin' ? 'primary' : ($user->role == 'tutor' ? 'success' : 'info'), false); ?>">
                                        <?php echo e(ucfirst($user->role), false); ?>

                                    </span>
                                </td>
                                <td class="text-center">
                                    <div class="d-inline-flex">
                                        <a href="<?php echo e(route('users.show', $user->id), false); ?>" class="btn btn-sm btn-icon btn-action-view" title="View">
                                            <i class="bx bx-show"></i>
                                        </a>
                                        <a href="<?php echo e(route('users.edit', $user->id), false); ?>" class="btn btn-sm btn-icon btn-action-edit" title="Edit">
                                            <i class="bx bx-edit"></i>
                                        </a>
                                        <button type="button" class="btn btn-sm btn-icon btn-action-delete"
                                                onclick="deleteUser(<?php echo e($user->id, false); ?>)" title="Delete">
                                            <i class="bx bx-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <tr>
                                <td colspan="5" class="text-center py-5">
                                    <div class="text-muted">
                                        <img src="https://cdn-icons-png.flaticon.com/512/4076/4076549.png" alt="No Data" width="80" class="mb-3 opacity-50">
                                        <p class="mb-0">No users found.</p>
                                    </div>
                                </td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<script>
  function deleteUser(userID) {
    Swal.fire({
        title: 'Are you sure?',
        text: 'Do you want to delete this user?',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: 'Yes, delete it!',
        cancelButtonText: 'No, cancel!',
        reverseButtons: true
    }).then((result) => {
        if (result.isConfirmed) {
            $.ajax({
                url: '<?php echo e(route('users.delete', ''), false); ?>/' + userID,
                type: 'DELETE',
                headers: {
                    'X-CSRF-TOKEN': '<?php echo e(csrf_token(), false); ?>'
                },
                success: function(response) {
                    if (response.success) {
                        Swal.fire(
                            'Deleted!',
                            response.message || 'User deleted successfully.',
                            'success'
                        ).then(() => {
                            location.reload();
                        });
                    } else {
                        Swal.fire(
                            'Error!',
                            response.message || 'Failed to delete user.',
                            'error'
                        );
                    }
                },
                error: function(xhr) {
                    Swal.fire(
                        'Error!',
                        xhr.responseJSON?.message || 'An unexpected error occurred.',
                        'error'
                    );
                }
            });
        }
    });
}
</script>

<!-- Modern Users Section Styles -->
<style>
/* Keyframe Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.95);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

@keyframes shimmer {
    0% {
        background-position: -200px 0;
    }
    100% {
        background-position: calc(200px + 100%) 0;
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-8px);
    }
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes rotateIn {
    from {
        opacity: 0;
        transform: rotate(-180deg) scale(0.5);
    }
    to {
        opacity: 1;
        transform: rotate(0deg) scale(1);
    }
}

/* Modern Users Section */
.modern-users-section {
    padding: 1.5rem;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    min-height: 100vh;
    animation: fadeInUp 0.6s ease-out;
}

/* Enhanced Header */
.users-header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(226, 232, 240, 0.8);
    border-radius: 1.5rem;
    padding: 2rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    animation: slideInDown 0.8s ease-out;
    position: relative;
    overflow: hidden;
}

.users-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: shimmer 3s infinite;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 1;
}

.header-left {
    flex: 1;
    animation: slideInLeft 0.8s ease-out 0.2s both;
}

.section-title {
    font-size: 1.75rem;
    font-weight: 700;
    color: #1e293b;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    animation: fadeInUp 0.8s ease-out 0.4s both;
}

.section-title i {
    color: #3b82f6;
    font-size: 2rem;
    animation: rotateIn 1s ease-out 0.6s both;
    transition: all 0.3s ease;
}

.section-title i:hover {
    transform: rotate(360deg) scale(1.1);
    color: #1d4ed8;
}

.section-subtitle {
    color: #64748b;
    margin: 0.5rem 0 0 0;
    font-size: 0.95rem;
    animation: fadeInUp 0.8s ease-out 0.6s both;
}

.header-actions {
    display: flex;
    gap: 0.75rem;
    align-items: center;
    animation: slideInRight 0.8s ease-out 0.4s both;
}

.action-btn {
    padding: 0.75rem 1.25rem;
    border-radius: 0.75rem;
    border: none;
    font-weight: 500;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
    position: relative;
    overflow: hidden;
    animation: bounceIn 0.8s ease-out 0.8s both;
}

.action-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.action-btn:hover::before {
    left: 100%;
}

.action-btn.primary {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.action-btn.primary:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 10px 25px rgba(59, 130, 246, 0.4);
    color: white;
}

.action-btn.secondary {
    background: rgba(248, 250, 252, 0.8);
    color: #64748b;
    border: 1px solid rgba(226, 232, 240, 0.8);
    padding: 0.75rem;
}

.action-btn.secondary:hover {
    background: rgba(241, 245, 249, 0.9);
    color: #475569;
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.action-btn i {
    transition: all 0.3s ease;
}

.action-btn:hover i {
    transform: scale(1.2) rotate(5deg);
}

/* Modern Alert */
.modern-alert {
    background: rgba(34, 197, 94, 0.1);
    border: 1px solid rgba(34, 197, 94, 0.2);
    border-radius: 1rem;
    color: #166534;
    margin-bottom: 1.5rem;
    animation: bounceIn 0.8s ease-out;
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
}

.modern-alert::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(34, 197, 94, 0.1), transparent);
    animation: shimmer 2s infinite;
}

/* Enhanced Controls */
.users-controls {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(226, 232, 240, 0.8);
    border-radius: 1.5rem;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 1rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
    animation: scaleIn 0.8s ease-out 0.2s both;
    position: relative;
    overflow: hidden;
}

.users-controls::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: shimmer 4s infinite;
}

.controls-left {
    display: flex;
    gap: 1rem;
    align-items: center;
    flex: 1;
    position: relative;
    z-index: 1;
    animation: slideInLeft 0.8s ease-out 0.4s both;
}

.controls-right {
    display: flex;
    gap: 1rem;
    align-items: center;
    position: relative;
    z-index: 1;
    animation: slideInRight 0.8s ease-out 0.6s both;
}

.search-container {
    position: relative;
    flex: 1;
    max-width: 300px;
}

.search-icon {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #9ca3af;
    font-size: 1.125rem;
}

.search-input {
    width: 100%;
    padding: 0.75rem 1rem 0.75rem 2.75rem;
    border: 1px solid rgba(226, 232, 240, 0.8);
    border-radius: 0.75rem;
    background: rgba(248, 250, 252, 0.8);
    font-size: 0.875rem;
    transition: all 0.2s ease;
}

.search-input:focus {
    outline: none;
    border-color: #3b82f6;
    background: white;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.filter-select {
    padding: 0.75rem 1rem;
    border: 1px solid rgba(226, 232, 240, 0.8);
    border-radius: 0.75rem;
    background: rgba(248, 250, 252, 0.8);
    font-size: 0.875rem;
    color: #64748b;
    cursor: pointer;
    transition: all 0.2s ease;
}

.filter-select:focus {
    outline: none;
    border-color: #3b82f6;
    background: white;
}

.export-buttons {
    display: flex;
    gap: 0.5rem;
}

.export-btn {
    padding: 0.75rem 1rem;
    border: 1px solid;
    border-radius: 0.75rem;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: white;
}

.export-btn.excel {
    color: #059669;
    border-color: rgba(5, 150, 105, 0.3);
}

.export-btn.excel:hover {
    background: rgba(5, 150, 105, 0.1);
    transform: translateY(-1px);
}

.export-btn.pdf {
    color: #dc2626;
    border-color: rgba(220, 38, 38, 0.3);
}

.export-btn.pdf:hover {
    background: rgba(220, 38, 38, 0.1);
    transform: translateY(-1px);
}

.view-toggle {
    display: flex;
    background: rgba(248, 250, 252, 0.8);
    border-radius: 0.75rem;
    padding: 0.25rem;
    border: 1px solid rgba(226, 232, 240, 0.8);
}

.view-btn {
    padding: 0.5rem 0.75rem;
    border: none;
    background: transparent;
    border-radius: 0.5rem;
    color: #64748b;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 1rem;
}

.view-btn.active {
    background: #3b82f6;
    color: white;
    box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
}

.view-btn:hover:not(.active) {
    background: rgba(59, 130, 246, 0.1);
    color: #3b82f6;
}

/* Users Grid */
.users-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 1.5rem;
    padding: 0.5rem;
}

.user-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(226, 232, 240, 0.8);
    border-radius: 1.5rem;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    animation: fadeInUp 0.8s ease-out both;
    position: relative;
}

.user-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: shimmer 3s infinite;
    z-index: 0;
}

.user-card:nth-child(1) { animation-delay: 0.1s; }
.user-card:nth-child(2) { animation-delay: 0.2s; }
.user-card:nth-child(3) { animation-delay: 0.3s; }
.user-card:nth-child(4) { animation-delay: 0.4s; }
.user-card:nth-child(5) { animation-delay: 0.5s; }
.user-card:nth-child(6) { animation-delay: 0.6s; }

.user-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    border-color: rgba(59, 130, 246, 0.4);
}

.user-card:hover::before {
    animation-duration: 1s;
}

.user-card .card-header {
    padding: 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgba(248, 250, 252, 0.6);
    border-bottom: 1px solid rgba(226, 232, 240, 0.5);
    position: relative;
    z-index: 1;
    animation: slideInDown 0.6s ease-out 0.2s both;
}

.user-avatar {
    width: 3.5rem;
    height: 3.5rem;
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    transition: all 0.3s ease;
    animation: bounceIn 0.8s ease-out 0.4s both;
    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

.user-avatar:hover {
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
}

.user-avatar i {
    color: white;
    font-size: 1.5rem;
    transition: all 0.3s ease;
}

.user-avatar:hover i {
    transform: scale(1.2);
}

.status-dot {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 1rem;
    height: 1rem;
    border-radius: 50%;
    border: 2px solid white;
    animation: pulse 2s infinite;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.status-dot.admin {
    background: #8b5cf6;
}

.status-dot.tutor {
    background: #10b981;
}

.status-dot.parent {
    background: #f59e0b;
}

.action-menu-btn {
    width: 2rem;
    height: 2rem;
    border: none;
    background: rgba(107, 114, 128, 0.1);
    border-radius: 0.5rem;
    color: #6b7280;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.action-menu-btn:hover {
    background: rgba(107, 114, 128, 0.2);
    color: #374151;
}

.user-card .card-body {
    padding: 1.5rem;
    position: relative;
    z-index: 1;
    animation: fadeInUp 0.6s ease-out 0.4s both;
}

.user-name {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1e293b;
    margin: 0 0 0.5rem 0;
    transition: all 0.3s ease;
    animation: slideInLeft 0.6s ease-out 0.6s both;
}

.user-card:hover .user-name {
    color: #3b82f6;
    transform: translateX(5px);
}

.user-email {
    color: #64748b;
    font-size: 0.875rem;
    margin: 0 0 1rem 0;
    transition: all 0.3s ease;
    animation: slideInLeft 0.6s ease-out 0.8s both;
}

.user-card:hover .user-email {
    color: #475569;
    transform: translateX(5px);
}

.role-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.6rem 1rem;
    border-radius: 0.75rem;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.3s ease;
    animation: bounceIn 0.8s ease-out 1s both;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.role-badge:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
}

.role-badge i {
    transition: all 0.3s ease;
}

.role-badge:hover i {
    transform: rotate(10deg) scale(1.1);
}

.role-badge.admin {
    background: rgba(139, 92, 246, 0.1);
    color: #7c3aed;
}

.role-badge.tutor {
    background: rgba(16, 185, 129, 0.1);
    color: #059669;
}

.role-badge.parent {
    background: rgba(245, 158, 11, 0.1);
    color: #d97706;
}

.user-card .card-footer {
    padding: 1.25rem 1.5rem;
    background: rgba(248, 250, 252, 0.4);
    border-top: 1px solid rgba(226, 232, 240, 0.5);
    position: relative;
    z-index: 1;
    animation: slideInUp 0.6s ease-out 0.8s both;
}

.quick-actions {
    display: flex;
    gap: 0.75rem;
    justify-content: center;
}

.quick-btn {
    width: 2.5rem;
    height: 2.5rem;
    border: none;
    border-radius: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    text-decoration: none;
    font-size: 1rem;
    position: relative;
    overflow: hidden;
    animation: scaleIn 0.6s ease-out both;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.quick-btn:nth-child(1) { animation-delay: 1s; }
.quick-btn:nth-child(2) { animation-delay: 1.1s; }
.quick-btn:nth-child(3) { animation-delay: 1.2s; }

.quick-btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transition: all 0.3s ease;
    transform: translate(-50%, -50%);
}

.quick-btn:hover::before {
    width: 100%;
    height: 100%;
}

.quick-btn.view {
    background: rgba(59, 130, 246, 0.1);
    color: #3b82f6;
}

.quick-btn.view:hover {
    background: rgba(59, 130, 246, 0.2);
    color: #1d4ed8;
    transform: scale(1.15) rotate(5deg);
    box-shadow: 0 6px 20px rgba(59, 130, 246, 0.3);
}

.quick-btn.edit {
    background: rgba(245, 158, 11, 0.1);
    color: #d97706;
}

.quick-btn.edit:hover {
    background: rgba(245, 158, 11, 0.2);
    color: #b45309;
    transform: scale(1.15) rotate(-5deg);
    box-shadow: 0 6px 20px rgba(245, 158, 11, 0.3);
}

.quick-btn.delete {
    background: rgba(239, 68, 68, 0.1);
    color: #ef4444;
}

.quick-btn.delete:hover {
    background: rgba(239, 68, 68, 0.2);
    color: #dc2626;
    transform: scale(1.15) rotate(5deg);
    box-shadow: 0 6px 20px rgba(239, 68, 68, 0.3);
}

/* Empty State */
.empty-state {
    grid-column: 1 / -1;
    text-align: center;
    padding: 4rem;
    color: #64748b;
    animation: fadeInUp 0.8s ease-out;
}

.empty-icon {
    font-size: 5rem;
    color: #cbd5e1;
    margin-bottom: 1.5rem;
    animation: float 3s ease-in-out infinite;
}

.empty-state h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #475569;
    margin-bottom: 0.5rem;
    animation: fadeInUp 0.8s ease-out 0.2s both;
}

.empty-state p {
    margin-bottom: 2rem;
    animation: fadeInUp 0.8s ease-out 0.4s both;
}

.empty-state .action-btn {
    animation: bounceIn 0.8s ease-out 0.6s both;
}

/* Table Container */
.table-container {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(226, 232, 240, 0.8);
    border-radius: 1rem;
    padding: 1.5rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

/* Responsive Design */
@media (max-width: 768px) {
    .modern-users-section {
        padding: 1rem;
    }

    .header-content {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }

    .users-controls {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }

    .controls-left {
        flex-direction: column;
        gap: 1rem;
    }

    .controls-right {
        justify-content: space-between;
    }

    .search-container {
        max-width: none;
    }

    .users-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
}

@media (max-width: 480px) {
    .export-buttons {
        flex-direction: column;
        width: 100%;
    }

    .export-btn {
        justify-content: center;
    }

    .action-btn span {
        display: none;
    }
}
</style>

<?php $__env->startPush('scripts'); ?>
<script>
    $(document).ready(function() {
        // Initialize DataTable for table view
        let dataTable = null;

        function initializeDataTable() {
            if (dataTable) {
                dataTable.destroy();
            }
            dataTable = initDataTable('usersTable', {
                order: [[1, 'asc']],
                columnDefs: [
                    { orderable: false, targets: [0, 4] },
                    { className: 'text-center', targets: [0, 3, 4] }
                ]
            });
        }

        // Enhanced Animation Functions
        function addRippleEffect(element, event) {
            const ripple = $('<span class="ripple-effect"></span>');
            const rect = element.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = event.clientX - rect.left - size / 2;
            const y = event.clientY - rect.top - size / 2;

            ripple.css({
                width: size + 'px',
                height: size + 'px',
                left: x + 'px',
                top: y + 'px'
            });

            $(element).append(ripple);

            setTimeout(() => {
                ripple.remove();
            }, 600);
        }

        // Add loading overlay
        if (!$('.loading-overlay').length) {
            $('body').append(`
                <div class="loading-overlay">
                    <div class="loading-spinner">
                        <div class="spinner-ring"></div>
                        <div class="spinner-text">Loading...</div>
                    </div>
                </div>
            `);
        }

        // Enhanced View Toggle Functionality
        $('.view-btn').on('click', function(e) {
            const view = $(this).data('view');
            addRippleEffect(this, e);

            $('.view-btn').removeClass('active');
            $(this).addClass('active');

            if (view === 'cards') {
                $('#tableView').fadeOut(300, function() {
                    $('#cardView').fadeIn(400);
                    // Re-trigger card animations
                    $('.user-card').each(function(index) {
                        $(this).css('animation-delay', (index * 0.1) + 's');
                        $(this).removeClass('fadeInUp').addClass('fadeInUp');
                    });
                });
            } else {
                $('#cardView').fadeOut(300, function() {
                    $('#tableView').fadeIn(400);
                    setTimeout(initializeDataTable, 100);
                    // Animate table rows
                    $('.table tbody tr').each(function(index) {
                        $(this).css({
                            'opacity': '0',
                            'transform': 'translateY(20px)'
                        }).delay(index * 50).animate({
                            'opacity': '1'
                        }, 300).css('transform', 'translateY(0)');
                    });
                });
            }
        });

        // Enhanced Search Functionality for Card View
        $('#userSearch').on('input', function() {
            const searchTerm = $(this).val().toLowerCase();

            $('.user-card').each(function(index) {
                const searchData = $(this).data('search');
                const $card = $(this);

                if (searchData.includes(searchTerm)) {
                    if ($card.is(':hidden')) {
                        $card.css({
                            'opacity': '0',
                            'transform': 'scale(0.8) translateY(20px)'
                        }).show().delay(index * 50).animate({
                            'opacity': '1'
                        }, 300, function() {
                            $card.css('transform', 'scale(1) translateY(0)');
                        });
                    }
                } else {
                    $card.animate({
                        'opacity': '0'
                    }, 200, function() {
                        $card.hide().css({
                            'opacity': '1',
                            'transform': 'scale(1) translateY(0)'
                        });
                    });
                }
            });
        });

        // Role Filter Functionality for Card View
        $('#roleFilter').on('change', function() {
            const selectedRole = $(this).val();

            $('.user-card').each(function() {
                const cardRole = $(this).data('role');
                if (selectedRole === '' || cardRole === selectedRole) {
                    $(this).show();
                } else {
                    $(this).hide();
                }
            });
        });

        // Export Functionality
        $('#exportExcel').on('click', function() {
            if (dataTable) {
                dataTable.button('.buttons-excel').trigger();
            } else {
                // For card view, create temporary table and export
                exportCardData('excel');
            }
        });

        $('#exportPDF').on('click', function() {
            if (dataTable) {
                dataTable.button('.buttons-pdf').trigger();
            } else {
                exportCardData('pdf');
            }
        });

        function exportCardData(format) {
            // Create temporary table for export
            const tempTable = $('<table>').addClass('d-none');
            const thead = $('<thead>').append(
                $('<tr>').append(
                    '<th>Sr No</th><th>Name</th><th>Email</th><th>Role</th>'
                )
            );
            const tbody = $('<tbody>');

            $('.user-card:visible').each(function(index) {
                const name = $(this).find('.user-name').text();
                const email = $(this).find('.user-email').text();
                const role = $(this).find('.role-badge').text().trim();

                tbody.append(
                    $('<tr>').append(
                        `<td>${index + 1}</td><td>${name}</td><td>${email}</td><td>${role}</td>`
                    )
                );
            });

            tempTable.append(thead).append(tbody);
            $('body').append(tempTable);

            // Initialize temporary DataTable for export
            const tempDT = tempTable.DataTable({
                dom: 'Bfrtip',
                buttons: [format]
            });

            tempDT.button(`.buttons-${format}`).trigger();

            // Clean up
            setTimeout(() => {
                tempDT.destroy();
                tempTable.remove();
            }, 1000);
        }

        // Refresh Users
        $('#refreshUsers').on('click', function() {
            location.reload();
        });

        // Enhanced Button Interactions
        $('.action-btn, .quick-btn, .export-btn').on('mousedown', function(e) {
            addRippleEffect(this, e);
        });

        // Enhanced Card Hover Effects
        $('.user-card').hover(
            function() {
                $(this).addClass('card-hover-active');
            },
            function() {
                $(this).removeClass('card-hover-active');
            }
        );

        // Enhanced Delete Function with Animations
        window.deleteUser = function(userID) {
            Swal.fire({
                title: 'Are you sure?',
                text: 'Do you want to delete this user?',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: 'Yes, delete it!',
                cancelButtonText: 'No, cancel!',
                reverseButtons: true,
                customClass: {
                    popup: 'animated-swal',
                    confirmButton: 'btn-danger-animated',
                    cancelButton: 'btn-secondary-animated'
                },
                showClass: {
                    popup: 'animate__animated animate__zoomIn animate__faster'
                },
                hideClass: {
                    popup: 'animate__animated animate__zoomOut animate__faster'
                }
            }).then((result) => {
                if (result.isConfirmed) {
                    $('.loading-overlay').addClass('show');

                    $.ajax({
                        url: '<?php echo e(route('users.delete', ''), false); ?>/' + userID,
                        type: 'DELETE',
                        headers: {
                            'X-CSRF-TOKEN': '<?php echo e(csrf_token(), false); ?>'
                        },
                        success: function(response) {
                            $('.loading-overlay').removeClass('show');

                            if (response.success) {
                                // Animate out the deleted card
                                const $userCard = $(`.user-card:has(button[onclick*="${userID}"])`);
                                $userCard.addClass('card-delete-animation');

                                Swal.fire({
                                    title: 'Deleted!',
                                    text: response.message || 'User deleted successfully.',
                                    icon: 'success',
                                    customClass: {
                                        popup: 'success-swal'
                                    },
                                    showClass: {
                                        popup: 'animate__animated animate__bounceIn'
                                    }
                                }).then(() => {
                                    setTimeout(() => {
                                        location.reload();
                                    }, 500);
                                });
                            } else {
                                Swal.fire({
                                    title: 'Error!',
                                    text: response.message || 'Failed to delete user.',
                                    icon: 'error',
                                    customClass: {
                                        popup: 'error-swal'
                                    }
                                });
                            }
                        },
                        error: function(xhr) {
                            $('.loading-overlay').removeClass('show');
                            Swal.fire({
                                title: 'Error!',
                                text: xhr.responseJSON?.message || 'An unexpected error occurred.',
                                icon: 'error',
                                customClass: {
                                    popup: 'error-swal'
                                }
                            });
                        }
                    });
                }
            });
        };

        // Initialize with card view
        $('#cardView').show();
        $('#tableView').hide();

        // Add stagger animation to cards on load
        $('.user-card').each(function(index) {
            $(this).css('animation-delay', (index * 0.1) + 's');
        });
    });

    // Additional CSS for enhanced animations
    const enhancedCSS = `
        <style>
        /* Ripple Effect */
        .ripple-effect {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.6);
            transform: scale(0);
            animation: ripple-animation 0.6s linear;
            pointer-events: none;
        }

        @keyframes ripple-animation {
            to {
                transform: scale(4);
                opacity: 0;
            }
        }

        /* Loading Overlay */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .loading-overlay.show {
            opacity: 1;
            visibility: visible;
        }

        .loading-spinner {
            text-align: center;
        }

        .spinner-ring {
            width: 60px;
            height: 60px;
            border: 4px solid rgba(59, 130, 246, 0.3);
            border-top: 4px solid #3b82f6;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem;
        }

        .spinner-text {
            color: #3b82f6;
            font-weight: 600;
            font-size: 0.875rem;
        }

        /* Card Hover Active State */
        .card-hover-active {
            transform: translateY(-8px) scale(1.02) !important;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2) !important;
        }

        /* Card Delete Animation */
        .card-delete-animation {
            animation: cardDeleteOut 0.5s ease-out forwards;
        }

        @keyframes cardDeleteOut {
            0% {
                transform: scale(1);
                opacity: 1;
            }
            50% {
                transform: scale(1.1);
                opacity: 0.7;
            }
            100% {
                transform: scale(0.8) translateX(100%);
                opacity: 0;
            }
        }

        /* Enhanced SweetAlert Styles */
        .animated-swal {
            border-radius: 1.5rem !important;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15) !important;
        }

        .btn-danger-animated {
            background: linear-gradient(135deg, #ef4444, #dc2626) !important;
            border: none !important;
            border-radius: 0.75rem !important;
            padding: 0.75rem 1.5rem !important;
            font-weight: 600 !important;
        }

        .btn-secondary-animated {
            background: rgba(107, 114, 128, 0.1) !important;
            color: #6b7280 !important;
            border: 1px solid rgba(107, 114, 128, 0.3) !important;
            border-radius: 0.75rem !important;
            padding: 0.75rem 1.5rem !important;
            font-weight: 600 !important;
        }

        .success-swal {
            border: 3px solid #10b981 !important;
        }

        .error-swal {
            border: 3px solid #ef4444 !important;
        }

        /* Enhanced Focus States */
        .search-input:focus,
        .filter-select:focus {
            transform: scale(1.02);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15) !important;
        }

        /* Smooth Transitions for All Interactive Elements */
        .action-btn,
        .quick-btn,
        .export-btn,
        .view-btn,
        .user-card,
        .search-input,
        .filter-select {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
        }

        /* Floating Animation for Icons */
        .section-title i,
        .empty-icon i {
            animation: float 3s ease-in-out infinite;
        }

        /* Stagger Animation for Cards */
        .user-card:nth-child(odd) {
            animation-delay: 0.1s;
        }

        .user-card:nth-child(even) {
            animation-delay: 0.2s;
        }
        </style>
    `;

    $('head').append(enhancedCSS);
</script>
<?php $__env->stopPush(); ?>

<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\Zoffness_backend\resources\views/users/list.blade.php ENDPATH**/ ?>