

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <!-- Enhanced Header -->
    <div class="modern-header">
        <div class="header-container">
            <div class="header-left">
                <h1 class="page-title">
                    <i class="bx bx-users"></i>
                    User Management
                </h1>
                <p class="page-subtitle">
                    Manage system users and their permissions
                    <span class="user-count-badge"><?php echo e(count($users), false); ?> users</span>
                </p>
            </div>
            <div class="header-actions">
                <div class="export-dropdown">
                    <button class="action-btn secondary" id="export-btn" title="Export Data">
                        <i class="bx bx-download"></i>
                        <span>Export</span>
                        <i class="bx bx-chevron-down"></i>
                    </button>
                    <div class="export-menu" id="export-menu">
                        <a href="#" class="export-option" data-format="excel">
                            <i class="bx bxs-file-export"></i>
                            <span>Export to Excel</span>
                        </a>
                        <a href="#" class="export-option" data-format="pdf">
                            <i class="bx bxs-file-pdf"></i>
                            <span>Export to PDF</span>
                        </a>
                    </div>
                </div>
                <button class="action-btn secondary" id="filter-btn" title="Filter Users">
                    <i class="bx bx-filter"></i>
                    <span>Filter</span>
                </button>
                <a href="<?php echo e(route('users.create'), false); ?>" class="action-btn primary">
                    <i class="bx bx-plus"></i>
                    <span>Add User</span>
                </a>
            </div>
        </div>
    </div>
    <!-- Success Alert -->
    <?php if(session('success')): ?>
        <div class="modern-alert success">
            <div class="alert-content">
                <i class="bx bx-check-circle"></i>
                <span><?php echo e(session('success'), false); ?></span>
                <button class="alert-close" onclick="this.parentElement.parentElement.remove()">
                    <i class="bx bx-x"></i>
                </button>
            </div>
        </div>
    <?php endif; ?>

    <!-- Enhanced User Cards Grid -->
    <div class="users-container">
        <div class="users-grid">
            <?php $__empty_1 = true; $__currentLoopData = $users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                <div class="user-card" data-user-id="<?php echo e($user->id, false); ?>">
                    <div class="user-card-header">
                        <div class="user-avatar">
                            <i class="bx bx-user"></i>
                            <span class="user-status <?php echo e($user->role, false); ?>"></span>
                        </div>
                        <div class="user-role-badge">
                            <span class="role-badge <?php echo e($user->role, false); ?>">
                                <?php echo e(ucfirst($user->role), false); ?>

                            </span>
                        </div>
                    </div>

                    <div class="user-card-body">
                        <h3 class="user-name"><?php echo e($user->username, false); ?></h3>
                        <p class="user-email"><?php echo e($user->email, false); ?></p>
                        <div class="user-meta">
                            <span class="meta-item">
                                <i class="bx bx-calendar"></i>
                                Joined <?php echo e($user->created_at ? $user->created_at->format('M Y') : 'N/A', false); ?>

                            </span>
                        </div>
                    </div>

                    <div class="user-card-actions">
                        <a href="<?php echo e(route('users.show', $user->id), false); ?>" class="card-action-btn view" title="View User">
                            <i class="bx bx-show"></i>
                        </a>
                        <a href="<?php echo e(route('users.edit', $user->id), false); ?>" class="card-action-btn edit" title="Edit User">
                            <i class="bx bx-edit"></i>
                        </a>
                        <button type="button" class="card-action-btn delete" onclick="deleteUser(<?php echo e($user->id, false); ?>)" title="Delete User">
                            <i class="bx bx-trash"></i>
                        </button>
                    </div>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                <div class="empty-state">
                    <div class="empty-icon">
                        <i class="bx bx-user-x"></i>
                    </div>
                    <h3>No Users Found</h3>
                    <p>Get started by creating your first user account.</p>
                    <a href="<?php echo e(route('users.create'), false); ?>" class="empty-action-btn">
                        <i class="bx bx-plus"></i>
                        Create First User
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<script>
function deleteUser(userID) {
    Swal.fire({
        title: 'Are you sure?',
        text: 'Do you want to delete this user?',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: 'Yes, delete it!',
        cancelButtonText: 'No, cancel!',
        reverseButtons: true
    }).then((result) => {
        if (result.isConfirmed) {
            $.ajax({
                url: '<?php echo e(route('users.delete', ''), false); ?>/' + userID,
                type: 'DELETE',
                headers: {
                    'X-CSRF-TOKEN': '<?php echo e(csrf_token(), false); ?>'
                },
                success: function(response) {
                    if (response.success) {
                        Swal.fire(
                            'Deleted!',
                            response.message || 'User deleted successfully.',
                            'success'
                        ).then(() => {
                            location.reload();
                        });
                    } else {
                        Swal.fire(
                            'Error!',
                            response.message || 'Failed to delete user.',
                            'error'
                        );
                    }
                },
                error: function(xhr) {
                    Swal.fire(
                        'Error!',
                        xhr.responseJSON?.message || 'An unexpected error occurred.',
                        'error'
                    );
                }
            });
        }
    });
}
</script>

<?php $__env->startPush('styles'); ?>
<style>
/* Export Dropdown Styles */
.export-dropdown {
    position: relative;
    display: inline-block;
}

.export-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(226, 232, 240, 0.8);
    border-radius: 0.75rem;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    min-width: 200px;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.2s ease;
    margin-top: 0.5rem;
}

.export-menu.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.export-option {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    color: #4a5568;
    text-decoration: none;
    border-radius: 0.5rem;
    margin: 0.25rem;
    transition: all 0.2s ease;
    font-size: 0.875rem;
    font-weight: 500;
}

.export-option:hover {
    background: rgba(59, 130, 246, 0.08);
    color: #2563eb;
    transform: translateX(2px);
}

.export-option i {
    font-size: 1.125rem;
    color: #6b7280;
    transition: color 0.2s ease;
}

.export-option:hover i {
    color: #2563eb;
}

/* Modern Header Styles */
.modern-header {
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(226, 232, 240, 0.8);
    border-radius: 1rem;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.header-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 1.5rem;
}

.header-left {
    flex: 1;
}

.page-title {
    font-size: 1.75rem;
    font-weight: 700;
    color: #1e293b;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.page-title i {
    color: #3b82f6;
    font-size: 2rem;
}

.page-subtitle {
    color: #64748b;
    margin: 0.5rem 0 0 0;
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.user-count-badge {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 600;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.action-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.25rem;
    border: none;
    border-radius: 0.75rem;
    font-size: 0.875rem;
    font-weight: 500;
    text-decoration: none;
    transition: all 0.2s ease;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.action-btn.primary {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.action-btn.primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(59, 130, 246, 0.4);
    color: white;
}

.action-btn.secondary {
    background: rgba(248, 250, 252, 0.8);
    color: #4a5568;
    border: 1px solid rgba(226, 232, 240, 0.8);
}

.action-btn.secondary:hover {
    background: rgba(59, 130, 246, 0.08);
    color: #2563eb;
    border-color: rgba(59, 130, 246, 0.2);
    transform: translateY(-1px);
}

/* Modern Alert Styles */
.modern-alert {
    border-radius: 0.75rem;
    padding: 1rem;
    margin-bottom: 1.5rem;
    border: none;
    position: relative;
    overflow: hidden;
}

.modern-alert.success {
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(5, 150, 105, 0.05));
    border-left: 4px solid #10b981;
}

.alert-content {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.alert-content i {
    color: #10b981;
    font-size: 1.25rem;
}

.alert-close {
    background: none;
    border: none;
    color: #6b7280;
    cursor: pointer;
    margin-left: auto;
    padding: 0.25rem;
    border-radius: 0.375rem;
    transition: all 0.2s ease;
}

.alert-close:hover {
    background: rgba(107, 114, 128, 0.1);
    color: #374151;
}

/* Users Container Styles */
.users-container {
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(226, 232, 240, 0.8);
    border-radius: 1rem;
    padding: 1.5rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.users-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 1.5rem;
}

/* User Card Styles */
.user-card {
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(226, 232, 240, 0.6);
    border-radius: 1rem;
    padding: 1.5rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.user-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 25px rgba(0, 0, 0, 0.1);
    border-color: rgba(59, 130, 246, 0.3);
}

.user-card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
}

.user-avatar {
    width: 3.5rem;
    height: 3.5rem;
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.user-avatar i {
    color: white;
    font-size: 1.5rem;
}

.user-status {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 1rem;
    height: 1rem;
    border-radius: 50%;
    border: 2px solid white;
}

.user-status.admin {
    background: #ef4444;
}

.user-status.tutor {
    background: #10b981;
}

.user-status.student {
    background: #3b82f6;
}

.role-badge {
    padding: 0.375rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

.role-badge.admin {
    background: rgba(239, 68, 68, 0.1);
    color: #dc2626;
}

.role-badge.tutor {
    background: rgba(16, 185, 129, 0.1);
    color: #059669;
}

.role-badge.student {
    background: rgba(59, 130, 246, 0.1);
    color: #2563eb;
}

.user-card-body {
    margin-bottom: 1.5rem;
}

.user-name {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1e293b;
    margin: 0 0 0.5rem 0;
}

.user-email {
    color: #64748b;
    margin: 0 0 1rem 0;
    font-size: 0.875rem;
}

.user-meta {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 0.375rem;
    color: #6b7280;
    font-size: 0.75rem;
}

.meta-item i {
    color: #9ca3af;
}

.user-card-actions {
    display: flex;
    gap: 0.5rem;
}

.card-action-btn {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0.75rem;
    border: none;
    border-radius: 0.5rem;
    font-size: 1rem;
    transition: all 0.2s ease;
    cursor: pointer;
    text-decoration: none;
}

.card-action-btn.view {
    background: rgba(59, 130, 246, 0.1);
    color: #2563eb;
}

.card-action-btn.view:hover {
    background: rgba(59, 130, 246, 0.2);
    color: #1d4ed8;
}

.card-action-btn.edit {
    background: rgba(245, 158, 11, 0.1);
    color: #d97706;
}

.card-action-btn.edit:hover {
    background: rgba(245, 158, 11, 0.2);
    color: #b45309;
}

.card-action-btn.delete {
    background: rgba(239, 68, 68, 0.1);
    color: #dc2626;
}

.card-action-btn.delete:hover {
    background: rgba(239, 68, 68, 0.2);
    color: #b91c1c;
}

/* Empty State Styles */
.empty-state {
    text-align: center;
    padding: 3rem;
    grid-column: 1 / -1;
}

.empty-icon {
    width: 4rem;
    height: 4rem;
    background: rgba(156, 163, 175, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
}

.empty-icon i {
    font-size: 2rem;
    color: #9ca3af;
}

.empty-state h3 {
    color: #374151;
    margin-bottom: 0.5rem;
}

.empty-state p {
    color: #6b7280;
    margin-bottom: 1.5rem;
}

.empty-action-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
    text-decoration: none;
    border-radius: 0.75rem;
    font-weight: 500;
    transition: all 0.2s ease;
}

.empty-action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(59, 130, 246, 0.4);
    color: white;
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-container {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
    }

    .header-actions {
        justify-content: center;
        flex-wrap: wrap;
    }

    .users-grid {
        grid-template-columns: 1fr;
    }

    .action-btn span {
        display: none;
    }

    .action-btn {
        padding: 0.75rem;
    }
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
    $(document).ready(function() {
        // Export dropdown functionality
        $('#export-btn').click(function(e) {
            e.preventDefault();
            e.stopPropagation();
            $('#export-menu').toggleClass('show');
        });

        // Close dropdown when clicking outside
        $(document).click(function(e) {
            if (!$(e.target).closest('.export-dropdown').length) {
                $('#export-menu').removeClass('show');
            }
        });

        // Export functionality
        $('.export-option').click(function(e) {
            e.preventDefault();
            const format = $(this).data('format');
            exportUsers(format);
            $('#export-menu').removeClass('show');
        });

        // Filter functionality
        $('#filter-btn').click(function() {
            // Add filter logic here
            console.log('Filter clicked');
        });
    });

    // Export function
    function exportUsers(format) {
        // Show loading state
        Swal.fire({
            title: 'Exporting...',
            text: `Preparing ${format.toUpperCase()} file`,
            allowOutsideClick: false,
            didOpen: () => {
                Swal.showLoading();
            }
        });

        // Make AJAX request to export
        $.ajax({
            url: '<?php echo e(route("users.export"), false); ?>',
            type: 'GET',
            data: { format: format },
            xhrFields: {
                responseType: 'blob'
            },
            success: function(data, status, xhr) {
                // Close loading dialog
                Swal.close();

                // Get filename from response headers or create default
                const disposition = xhr.getResponseHeader('Content-Disposition');
                let filename = `users_${new Date().toISOString().slice(0, 10)}.${format === 'excel' ? 'csv' : 'html'}`;

                if (disposition && disposition.indexOf('attachment') !== -1) {
                    const matches = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/.exec(disposition);
                    if (matches != null && matches[1]) {
                        filename = matches[1].replace(/['"]/g, '');
                    }
                }

                // Create download link
                const blob = new Blob([data], {
                    type: format === 'excel' ? 'text/csv' : 'text/html'
                });
                const url = window.URL.createObjectURL(blob);
                const link = document.createElement('a');
                link.href = url;
                link.download = filename;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                window.URL.revokeObjectURL(url);

                // Show success message
                Swal.fire({
                    icon: 'success',
                    title: 'Export Successful!',
                    text: `${format.toUpperCase()} file has been downloaded.`,
                    timer: 3000,
                    showConfirmButton: false
                });
            },
            error: function(xhr, status, error) {
                Swal.close();

                let errorMessage = 'An error occurred while exporting the file.';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                } else if (xhr.responseText) {
                    try {
                        const response = JSON.parse(xhr.responseText);
                        errorMessage = response.message || errorMessage;
                    } catch (e) {
                        // If not JSON, use default message
                    }
                }

                Swal.fire({
                    icon: 'error',
                    title: 'Export Failed',
                    text: errorMessage,
                    confirmButtonText: 'OK'
                });
            }
        });
    }
</script>
<?php $__env->stopPush(); ?>

<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\Zoffness_backend\resources\views/users/list.blade.php ENDPATH**/ ?>