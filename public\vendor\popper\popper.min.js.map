{"version": 3, "file": "popper.min.js", "sources": ["../src/utils/isBrowser.js", "../src/utils/debounce.js", "../src/utils/isFunction.js", "../src/utils/getStyleComputedProperty.js", "../src/utils/getParentNode.js", "../src/utils/getScrollParent.js", "../src/utils/getReferenceNode.js", "../src/utils/isIE.js", "../src/utils/getOffsetParent.js", "../src/utils/isOffsetContainer.js", "../src/utils/getRoot.js", "../src/utils/findCommonOffsetParent.js", "../src/utils/getScroll.js", "../src/utils/includeScroll.js", "../src/utils/getBordersSize.js", "../src/utils/getWindowSizes.js", "../src/utils/getClientRect.js", "../src/utils/getBoundingClientRect.js", "../src/utils/getOffsetRectRelativeToArbitraryNode.js", "../src/utils/getViewportOffsetRectRelativeToArtbitraryNode.js", "../src/utils/isFixed.js", "../src/utils/getFixedPositionOffsetParent.js", "../src/utils/getBoundaries.js", "../src/utils/computeAutoPlacement.js", "../src/utils/getReferenceOffsets.js", "../src/utils/getOuterSizes.js", "../src/utils/getOppositePlacement.js", "../src/utils/getPopperOffsets.js", "../src/utils/find.js", "../src/utils/findIndex.js", "../src/utils/runModifiers.js", "../src/methods/update.js", "../src/utils/isModifierEnabled.js", "../src/utils/getSupportedPropertyName.js", "../src/methods/destroy.js", "../src/utils/getWindow.js", "../src/utils/setupEventListeners.js", "../src/methods/enableEventListeners.js", "../src/utils/removeEventListeners.js", "../src/methods/disableEventListeners.js", "../src/utils/isNumeric.js", "../src/utils/setStyles.js", "../src/utils/setAttributes.js", "../src/modifiers/applyStyle.js", "../src/utils/getRoundedOffsets.js", "../src/modifiers/computeStyle.js", "../src/utils/isModifierRequired.js", "../src/modifiers/arrow.js", "../src/utils/getOppositeVariation.js", "../src/methods/placements.js", "../src/utils/clockwise.js", "../src/modifiers/flip.js", "../src/modifiers/keepTogether.js", "../src/modifiers/offset.js", "../src/modifiers/preventOverflow.js", "../src/modifiers/shift.js", "../src/modifiers/hide.js", "../src/modifiers/inner.js", "../src/modifiers/index.js", "../src/methods/defaults.js", "../src/index.js"], "sourcesContent": ["export default typeof window !== 'undefined' && typeof document !== 'undefined' && typeof navigator !== 'undefined';\n", "import isBrowser from './isBrowser';\n\nconst timeoutDuration = (function(){\n  const longerTimeoutBrowsers = ['Edge', 'Trident', 'Firefox'];\n  for (let i = 0; i < longerTimeoutBrowsers.length; i += 1) {\n    if (isBrowser && navigator.userAgent.indexOf(longerTimeoutBrowsers[i]) >= 0) {\n      return 1;\n    }\n  }\n  return 0;\n}());\n\nexport function microtaskDebounce(fn) {\n  let called = false\n  return () => {\n    if (called) {\n      return\n    }\n    called = true\n    window.Promise.resolve().then(() => {\n      called = false\n      fn()\n    })\n  }\n}\n\nexport function taskDebounce(fn) {\n  let scheduled = false;\n  return () => {\n    if (!scheduled) {\n      scheduled = true;\n      setTimeout(() => {\n        scheduled = false;\n        fn();\n      }, timeoutDuration);\n    }\n  };\n}\n\nconst supportsMicroTasks = isBrowser && window.Promise\n\n\n/**\n* Create a debounced version of a method, that's asynchronously deferred\n* but called in the minimum time possible.\n*\n* @method\n* @memberof Popper.Utils\n* @argument {Function} fn\n* @returns {Function}\n*/\nexport default (supportsMicroTasks\n  ? microtaskDebounce\n  : taskDebounce);\n", "/**\n * Check if the given variable is a function\n * @method\n * @memberof Popper.Utils\n * @argument {Any} functionToCheck - variable to check\n * @returns {Boolean} answer to: is a function?\n */\nexport default function isFunction(functionToCheck) {\n  const getType = {};\n  return (\n    functionToCheck &&\n    getType.toString.call(functionToCheck) === '[object Function]'\n  );\n}\n", "/**\n * Get CSS computed property of the given element\n * @method\n * @memberof Popper.Utils\n * @argument {Eement} element\n * @argument {String} property\n */\nexport default function getStyleComputedProperty(element, property) {\n  if (element.nodeType !== 1) {\n    return [];\n  }\n  // NOTE: 1 DOM access here\n  const window = element.ownerDocument.defaultView;\n  const css = window.getComputedStyle(element, null);\n  return property ? css[property] : css;\n}\n", "/**\n * Returns the parentNode or the host of the element\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Element} parent\n */\nexport default function getParentNode(element) {\n  if (element.nodeName === 'HTML') {\n    return element;\n  }\n  return element.parentNode || element.host;\n}\n", "import getStyleComputedProperty from './getStyleComputedProperty';\nimport getParentNode from './getParentNode';\n\n/**\n * Returns the scrolling parent of the given element\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Element} scroll parent\n */\nexport default function getScrollParent(element) {\n  // Return body, `getScroll` will take care to get the correct `scrollTop` from it\n  if (!element) {\n    return document.body\n  }\n\n  switch (element.nodeName) {\n    case 'HTML':\n    case 'BODY':\n      return element.ownerDocument.body\n    case '#document':\n      return element.body\n  }\n\n  // Firefox want us to check `-x` and `-y` variations as well\n  const { overflow, overflowX, overflowY } = getStyleComputedProperty(element);\n  if (/(auto|scroll|overlay)/.test(overflow + overflowY + overflowX)) {\n    return element;\n  }\n\n  return getScrollParent(getParentNode(element));\n}\n", "/**\n * Returns the reference node of the reference object, or the reference object itself.\n * @method\n * @memberof Popper.Utils\n * @param {Element|Object} reference - the reference element (the popper will be relative to this)\n * @returns {Element} parent\n */\nexport default function getReferenceNode(reference) {\n  return reference && reference.referenceNode ? reference.referenceNode : reference;\n}\n", "import isBrowser from './isBrowser';\n\nconst isIE11 = isBrowser && !!(window.MSInputMethodContext && document.documentMode);\nconst isIE10 = isBrowser && /MSIE 10/.test(navigator.userAgent);\n\n/**\n * Determines if the browser is Internet Explorer\n * @method\n * @memberof Popper.Utils\n * @param {Number} version to check\n * @returns {Boolean} isIE\n */\nexport default function isIE(version) {\n  if (version === 11) {\n    return isIE11;\n  }\n  if (version === 10) {\n    return isIE10;\n  }\n  return isIE11 || isIE10;\n}\n", "import getStyleComputedProperty from './getStyleComputedProperty';\nimport isIE from './isIE';\n/**\n * Returns the offset parent of the given element\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Element} offset parent\n */\nexport default function getOffsetParent(element) {\n  if (!element) {\n    return document.documentElement;\n  }\n\n  const noOffsetParent = isIE(10) ? document.body : null;\n\n  // NOTE: 1 DOM access here\n  let offsetParent = element.offsetParent || null;\n  // Skip hidden elements which don't have an offsetParent\n  while (offsetParent === noOffsetParent && element.nextElementSibling) {\n    offsetParent = (element = element.nextElementSibling).offsetParent;\n  }\n\n  const nodeName = offsetParent && offsetParent.nodeName;\n\n  if (!nodeName || nodeName === 'BODY' || nodeName === 'HTML') {\n    return element ? element.ownerDocument.documentElement : document.documentElement;\n  }\n\n  // .offsetParent will return the closest TH, TD or TABLE in case\n  // no offsetParent is present, I hate this job...\n  if (\n    ['TH', 'TD', 'TABLE'].indexOf(offsetParent.nodeName) !== -1 &&\n    getStyleComputedProperty(offsetParent, 'position') === 'static'\n  ) {\n    return getOffsetParent(offsetParent);\n  }\n\n  return offsetParent;\n}\n", "import getOffsetParent from './getOffsetParent';\n\nexport default function isOffsetContainer(element) {\n  const { nodeName } = element;\n  if (nodeName === 'BODY') {\n    return false;\n  }\n  return (\n    nodeName === 'HTML' || getOffsetParent(element.firstElementChild) === element\n  );\n}\n", "/**\n * Finds the root node (document, shadowDOM root) of the given element\n * @method\n * @memberof Popper.Utils\n * @argument {Element} node\n * @returns {Element} root node\n */\nexport default function getRoot(node) {\n  if (node.parentNode !== null) {\n    return getRoot(node.parentNode);\n  }\n\n  return node;\n}\n", "import isOffsetContainer from './isOffsetContainer';\nimport getRoot from './getRoot';\nimport getOffsetParent from './getOffsetParent';\n\n/**\n * Finds the offset parent common to the two provided nodes\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element1\n * @argument {Element} element2\n * @returns {Element} common offset parent\n */\nexport default function findCommonOffsetParent(element1, element2) {\n  // This check is needed to avoid errors in case one of the elements isn't defined for any reason\n  if (!element1 || !element1.nodeType || !element2 || !element2.nodeType) {\n    return document.documentElement;\n  }\n\n  // Here we make sure to give as \"start\" the element that comes first in the DOM\n  const order =\n    element1.compareDocumentPosition(element2) &\n    Node.DOCUMENT_POSITION_FOLLOWING;\n  const start = order ? element1 : element2;\n  const end = order ? element2 : element1;\n\n  // Get common ancestor container\n  const range = document.createRange();\n  range.setStart(start, 0);\n  range.setEnd(end, 0);\n  const { commonAncestorContainer } = range;\n\n  // Both nodes are inside #document\n  if (\n    (element1 !== commonAncestorContainer &&\n      element2 !== commonAncestorContainer) ||\n    start.contains(end)\n  ) {\n    if (isOffsetContainer(commonAncestorContainer)) {\n      return commonAncestorContainer;\n    }\n\n    return getOffsetParent(commonAncestorContainer);\n  }\n\n  // one of the nodes is inside shadowDOM, find which one\n  const element1root = getRoot(element1);\n  if (element1root.host) {\n    return findCommonOffsetParent(element1root.host, element2);\n  } else {\n    return findCommonOffsetParent(element1, getRoot(element2).host);\n  }\n}\n", "/**\n * Gets the scroll value of the given element in the given side (top and left)\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @argument {String} side `top` or `left`\n * @returns {number} amount of scrolled pixels\n */\nexport default function getScroll(element, side = 'top') {\n  const upperSide = side === 'top' ? 'scrollTop' : 'scrollLeft';\n  const nodeName = element.nodeName;\n\n  if (nodeName === 'BODY' || nodeName === 'HTML') {\n    const html = element.ownerDocument.documentElement;\n    const scrollingElement = element.ownerDocument.scrollingElement || html;\n    return scrollingElement[upperSide];\n  }\n\n  return element[upperSide];\n}\n", "import getScroll from './getScroll';\n\n/*\n * Sum or subtract the element scroll values (left and top) from a given rect object\n * @method\n * @memberof Popper.Utils\n * @param {Object} rect - Rect object you want to change\n * @param {HTMLElement} element - The element from the function reads the scroll values\n * @param {Boolean} subtract - set to true if you want to subtract the scroll values\n * @return {Object} rect - The modifier rect object\n */\nexport default function includeScroll(rect, element, subtract = false) {\n  const scrollTop = getScroll(element, 'top');\n  const scrollLeft = getScroll(element, 'left');\n  const modifier = subtract ? -1 : 1;\n  rect.top += scrollTop * modifier;\n  rect.bottom += scrollTop * modifier;\n  rect.left += scrollLeft * modifier;\n  rect.right += scrollLeft * modifier;\n  return rect;\n}\n", "/*\n * Helper to detect borders of a given element\n * @method\n * @memberof Popper.Utils\n * @param {CSSStyleDeclaration} styles\n * Result of `getStyleComputedProperty` on the given element\n * @param {String} axis - `x` or `y`\n * @return {number} borders - The borders size of the given axis\n */\n\nexport default function getBordersSize(styles, axis) {\n  const sideA = axis === 'x' ? 'Left' : 'Top';\n  const sideB = sideA === 'Left' ? 'Right' : 'Bottom';\n\n  return (\n    parseFloat(styles[`border${sideA}Width`]) +\n    parseFloat(styles[`border${sideB}Width`])\n  );\n}\n", "import isIE from './isIE';\n\nfunction getSize(axis, body, html, computedStyle) {\n  return Math.max(\n    body[`offset${axis}`],\n    body[`scroll${axis}`],\n    html[`client${axis}`],\n    html[`offset${axis}`],\n    html[`scroll${axis}`],\n    isIE(10)\n      ? (parseInt(html[`offset${axis}`]) + \n      parseInt(computedStyle[`margin${axis === 'Height' ? 'Top' : 'Left'}`]) + \n      parseInt(computedStyle[`margin${axis === 'Height' ? 'Bottom' : 'Right'}`]))\n    : 0 \n  );\n}\n\nexport default function getWindowSizes(document) {\n  const body = document.body;\n  const html = document.documentElement;\n  const computedStyle = isIE(10) && getComputedStyle(html);\n\n  return {\n    height: getSize('Height', body, html, computedStyle),\n    width: getSize('Width', body, html, computedStyle),\n  };\n}\n", "/**\n * Given element offsets, generate an output similar to getBoundingClientRect\n * @method\n * @memberof Popper.Utils\n * @argument {Object} offsets\n * @returns {Object} ClientRect like output\n */\nexport default function getClientRect(offsets) {\n  return {\n    ...offsets,\n    right: offsets.left + offsets.width,\n    bottom: offsets.top + offsets.height,\n  };\n}\n", "import getStyleComputedProperty from './getStyleComputedProperty';\nimport getBordersSize from './getBordersSize';\nimport getWindowSizes from './getWindowSizes';\nimport getScroll from './getScroll';\nimport getClientRect from './getClientRect';\nimport isIE from './isIE';\n\n/**\n * Get bounding client rect of given element\n * @method\n * @memberof Popper.Utils\n * @param {HTMLElement} element\n * @return {Object} client rect\n */\nexport default function getBoundingClientRect(element) {\n  let rect = {};\n\n  // IE10 10 FIX: Please, don't ask, the element isn't\n  // considered in DOM in some circumstances...\n  // This isn't reproducible in IE10 compatibility mode of IE11\n  try {\n    if (isIE(10)) {\n      rect = element.getBoundingClientRect();\n      const scrollTop = getScroll(element, 'top');\n      const scrollLeft = getScroll(element, 'left');\n      rect.top += scrollTop;\n      rect.left += scrollLeft;\n      rect.bottom += scrollTop;\n      rect.right += scrollLeft;\n    }\n    else {\n      rect = element.getBoundingClientRect();\n    }\n  }\n  catch(e){}\n\n  const result = {\n    left: rect.left,\n    top: rect.top,\n    width: rect.right - rect.left,\n    height: rect.bottom - rect.top,\n  };\n\n  // subtract scrollbar size from sizes\n  const sizes = element.nodeName === 'HTML' ? getWindowSizes(element.ownerDocument) : {};\n  const width =\n    sizes.width || element.clientWidth || result.width;\n  const height =\n    sizes.height || element.clientHeight || result.height;\n\n  let horizScrollbar = element.offsetWidth - width;\n  let vertScrollbar = element.offsetHeight - height;\n\n  // if an hypothetical scrollbar is detected, we must be sure it's not a `border`\n  // we make this check conditional for performance reasons\n  if (horizScrollbar || vertScrollbar) {\n    const styles = getStyleComputedProperty(element);\n    horizScrollbar -= getBordersSize(styles, 'x');\n    vertScrollbar -= getBordersSize(styles, 'y');\n\n    result.width -= horizScrollbar;\n    result.height -= vertScrollbar;\n  }\n\n  return getClientRect(result);\n}\n", "import getStyleComputedProperty from './getStyleComputedProperty';\nimport includeScroll from './includeScroll';\nimport getScrollParent from './getScrollParent';\nimport getBoundingClientRect from './getBoundingClientRect';\nimport runIsIE from './isIE';\nimport getClientRect from './getClientRect';\n\nexport default function getOffsetRectRelativeToArbitraryNode(children, parent, fixedPosition = false) {\n  const isIE10 = runIsIE(10);\n  const isHTML = parent.nodeName === 'HTML';\n  const childrenRect = getBoundingClientRect(children);\n  const parentRect = getBoundingClientRect(parent);\n  const scrollParent = getScrollParent(children);\n\n  const styles = getStyleComputedProperty(parent);\n  const borderTopWidth = parseFloat(styles.borderTopWidth);\n  const borderLeftWidth = parseFloat(styles.borderLeftWidth);\n\n  // In cases where the parent is fixed, we must ignore negative scroll in offset calc\n  if(fixedPosition && isHTML) {\n    parentRect.top = Math.max(parentRect.top, 0);\n    parentRect.left = Math.max(parentRect.left, 0);\n  }\n  let offsets = getClientRect({\n    top: childrenRect.top - parentRect.top - borderTopWidth,\n    left: childrenRect.left - parentRect.left - borderLeftWidth,\n    width: childrenRect.width,\n    height: childrenRect.height,\n  });\n  offsets.marginTop = 0;\n  offsets.marginLeft = 0;\n\n  // Subtract margins of documentElement in case it's being used as parent\n  // we do this only on HTML because it's the only element that behaves\n  // differently when margins are applied to it. The margins are included in\n  // the box of the documentElement, in the other cases not.\n  if (!isIE10 && isHTML) {\n    const marginTop = parseFloat(styles.marginTop);\n    const marginLeft = parseFloat(styles.marginLeft);\n\n    offsets.top -= borderTopWidth - marginTop;\n    offsets.bottom -= borderTopWidth - marginTop;\n    offsets.left -= borderLeftWidth - marginLeft;\n    offsets.right -= borderLeftWidth - marginLeft;\n\n    // Attach marginTop and marginLeft because in some circumstances we may need them\n    offsets.marginTop = marginTop;\n    offsets.marginLeft = marginLeft;\n  }\n\n  if (\n    isIE10 && !fixedPosition\n      ? parent.contains(scrollParent)\n      : parent === scrollParent && scrollParent.nodeName !== 'BODY'\n  ) {\n    offsets = includeScroll(offsets, parent);\n  }\n\n  return offsets;\n}\n", "import getOffsetRectRelativeToArbitraryNode from './getOffsetRectRelativeToArbitraryNode';\nimport getScroll from './getScroll';\nimport getClientRect from './getClientRect';\n\nexport default function getViewportOffsetRectRelativeToArtbitraryNode(element, excludeScroll = false) {\n  const html = element.ownerDocument.documentElement;\n  const relativeOffset = getOffsetRectRelativeToArbitraryNode(element, html);\n  const width = Math.max(html.clientWidth, window.innerWidth || 0);\n  const height = Math.max(html.clientHeight, window.innerHeight || 0);\n\n  const scrollTop = !excludeScroll ? getScroll(html) : 0;\n  const scrollLeft = !excludeScroll ? getScroll(html, 'left') : 0;\n\n  const offset = {\n    top: scrollTop - relativeOffset.top + relativeOffset.marginTop,\n    left: scrollLeft - relativeOffset.left + relativeOffset.marginLeft,\n    width,\n    height,\n  };\n\n  return getClientRect(offset);\n}\n", "import getStyleComputedProperty from './getStyleComputedProperty';\nimport getParentNode from './getParentNode';\n\n/**\n * Check if the given element is fixed or is inside a fixed parent\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @argument {Element} customContainer\n * @returns {<PERSON><PERSON><PERSON>} answer to \"isFixed?\"\n */\nexport default function isFixed(element) {\n  const nodeName = element.nodeName;\n  if (nodeName === 'BODY' || nodeName === 'HTML') {\n    return false;\n  }\n  if (getStyleComputedProperty(element, 'position') === 'fixed') {\n    return true;\n  }\n  const parentNode = getParentNode(element);\n  if (!parentNode) {\n    return false;\n  }\n  return isFixed(parentNode);\n}\n", "import getStyleComputedProperty from './getStyleComputedProperty';\nimport isIE from './isIE';\n/**\n * Finds the first parent of an element that has a transformed property defined\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Element} first transformed parent or documentElement\n */\n\nexport default function getFixedPositionOffsetParent(element) {\n  // This check is needed to avoid errors in case one of the elements isn't defined for any reason\n   if (!element || !element.parentElement || isIE()) {\n    return document.documentElement;\n  }\n  let el = element.parentElement;\n  while (el && getStyleComputedProperty(el, 'transform') === 'none') {\n    el = el.parentElement;\n  }\n  return el || document.documentElement;\n\n}\n", "import getScrollParent from './getScrollParent';\nimport getParentNode from './getParentNode';\nimport getReferenceNode from './getReferenceNode';\nimport findCommonOffsetParent from './findCommonOffsetParent';\nimport getOffsetRectRelativeToArbitraryNode from './getOffsetRectRelativeToArbitraryNode';\nimport getViewportOffsetRectRelativeToArtbitraryNode from './getViewportOffsetRectRelativeToArtbitraryNode';\nimport getWindowSizes from './getWindowSizes';\nimport isFixed from './isFixed';\nimport getFixedPositionOffsetParent from './getFixedPositionOffsetParent';\n\n/**\n * Computed the boundaries limits and return them\n * @method\n * @memberof Popper.Utils\n * @param {HTMLElement} popper\n * @param {HTMLElement} reference\n * @param {number} padding\n * @param {HTMLElement} boundariesElement - Element used to define the boundaries\n * @param {Boolean} fixedPosition - Is in fixed position mode\n * @returns {Object} Coordinates of the boundaries\n */\nexport default function getBoundaries(\n  popper,\n  reference,\n  padding,\n  boundariesElement,\n  fixedPosition = false\n) {\n  // NOTE: 1 DOM access here\n\n  let boundaries = { top: 0, left: 0 };\n  const offsetParent = fixedPosition ? getFixedPositionOffsetParent(popper) : findCommonOffsetParent(popper, getReferenceNode(reference));\n\n  // Handle viewport case\n  if (boundariesElement === 'viewport' ) {\n    boundaries = getViewportOffsetRectRelativeToArtbitraryNode(offsetParent, fixedPosition);\n  }\n\n  else {\n    // Handle other cases based on DOM element used as boundaries\n    let boundariesNode;\n    if (boundariesElement === 'scrollParent') {\n      boundariesNode = getScrollParent(getParentNode(reference));\n      if (boundariesNode.nodeName === 'BODY') {\n        boundariesNode = popper.ownerDocument.documentElement;\n      }\n    } else if (boundariesElement === 'window') {\n      boundariesNode = popper.ownerDocument.documentElement;\n    } else {\n      boundariesNode = boundariesElement;\n    }\n\n    const offsets = getOffsetRectRelativeToArbitraryNode(\n      boundariesNode,\n      offsetParent,\n      fixedPosition\n    );\n\n    // In case of HTML, we need a different computation\n    if (boundariesNode.nodeName === 'HTML' && !isFixed(offsetParent)) {\n      const { height, width } = getWindowSizes(popper.ownerDocument);\n      boundaries.top += offsets.top - offsets.marginTop;\n      boundaries.bottom = height + offsets.top;\n      boundaries.left += offsets.left - offsets.marginLeft;\n      boundaries.right = width + offsets.left;\n    } else {\n      // for all the other DOM elements, this one is good\n      boundaries = offsets;\n    }\n  }\n\n  // Add paddings\n  padding = padding || 0;\n  const isPaddingNumber = typeof padding === 'number';\n  boundaries.left += isPaddingNumber ? padding : padding.left || 0; \n  boundaries.top += isPaddingNumber ? padding : padding.top || 0; \n  boundaries.right -= isPaddingNumber ? padding : padding.right || 0; \n  boundaries.bottom -= isPaddingNumber ? padding : padding.bottom || 0; \n\n  return boundaries;\n}\n", "import getBoundaries from '../utils/getBoundaries';\n\nfunction getArea({ width, height }) {\n  return width * height;\n}\n\n/**\n * Utility used to transform the `auto` placement to the placement with more\n * available space.\n * @method\n * @memberof Popper.Utils\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nexport default function computeAutoPlacement(\n  placement,\n  refRect,\n  popper,\n  reference,\n  boundariesElement,\n  padding = 0\n) {\n  if (placement.indexOf('auto') === -1) {\n    return placement;\n  }\n\n  const boundaries = getBoundaries(\n    popper,\n    reference,\n    padding,\n    boundariesElement\n  );\n\n  const rects = {\n    top: {\n      width: boundaries.width,\n      height: refRect.top - boundaries.top,\n    },\n    right: {\n      width: boundaries.right - refRect.right,\n      height: boundaries.height,\n    },\n    bottom: {\n      width: boundaries.width,\n      height: boundaries.bottom - refRect.bottom,\n    },\n    left: {\n      width: refRect.left - boundaries.left,\n      height: boundaries.height,\n    },\n  };\n\n  const sortedAreas = Object.keys(rects)\n    .map(key => ({\n      key,\n      ...rects[key],\n      area: getArea(rects[key]),\n    }))\n    .sort((a, b) => b.area - a.area);\n\n  const filteredAreas = sortedAreas.filter(\n    ({ width, height }) =>\n      width >= popper.clientWidth && height >= popper.clientHeight\n  );\n\n  const computedPlacement = filteredAreas.length > 0\n    ? filteredAreas[0].key\n    : sortedAreas[0].key;\n\n  const variation = placement.split('-')[1];\n\n  return computedPlacement + (variation ? `-${variation}` : '');\n}\n", "import findCommonOffsetParent from './findCommonOffsetParent';\nimport getOffsetRectRelativeToArbitraryNode from './getOffsetRectRelativeToArbitraryNode';\nimport getFixedPositionOffsetParent from './getFixedPositionOffsetParent';\nimport getReferenceNode from './getReferenceNode';\n\n/**\n * Get offsets to the reference element\n * @method\n * @memberof Popper.Utils\n * @param {Object} state\n * @param {Element} popper - the popper element\n * @param {Element} reference - the reference element (the popper will be relative to this)\n * @param {Element} fixedPosition - is in fixed position mode\n * @returns {Object} An object containing the offsets which will be applied to the popper\n */\nexport default function getReferenceOffsets(state, popper, reference, fixedPosition = null) {\n  const commonOffsetParent = fixedPosition ? getFixedPositionOffsetParent(popper) : findCommonOffsetParent(popper, getReferenceNode(reference));\n  return getOffsetRectRelativeToArbitraryNode(reference, commonOffsetParent, fixedPosition);\n}\n", "/**\n * Get the outer sizes of the given element (offset size + margins)\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Object} object containing width and height properties\n */\nexport default function getOuterSizes(element) {\n  const window = element.ownerDocument.defaultView;\n  const styles = window.getComputedStyle(element);\n  const x = parseFloat(styles.marginTop || 0) + parseFloat(styles.marginBottom || 0);\n  const y = parseFloat(styles.marginLeft || 0) + parseFloat(styles.marginRight || 0);\n  const result = {\n    width: element.offsetWidth + y,\n    height: element.offsetHeight + x,\n  };\n  return result;\n}\n", "/**\n * Get the opposite placement of the given one\n * @method\n * @memberof Popper.Utils\n * @argument {String} placement\n * @returns {String} flipped placement\n */\nexport default function getOppositePlacement(placement) {\n  const hash = { left: 'right', right: 'left', bottom: 'top', top: 'bottom' };\n  return placement.replace(/left|right|bottom|top/g, matched => hash[matched]);\n}\n", "import getOuterSizes from './getOuterSizes';\nimport getOppositePlacement from './getOppositePlacement';\n\n/**\n * Get offsets to the popper\n * @method\n * @memberof Popper.Utils\n * @param {Object} position - CSS position the Popper will get applied\n * @param {HTMLElement} popper - the popper element\n * @param {Object} referenceOffsets - the reference offsets (the popper will be relative to this)\n * @param {String} placement - one of the valid placement options\n * @returns {Object} popperOffsets - An object containing the offsets which will be applied to the popper\n */\nexport default function getPopperOffsets(popper, referenceOffsets, placement) {\n  placement = placement.split('-')[0];\n\n  // Get popper node sizes\n  const popperRect = getOuterSizes(popper);\n\n  // Add position, width and height to our offsets object\n  const popperOffsets = {\n    width: popperRect.width,\n    height: popperRect.height,\n  };\n\n  // depending by the popper placement we have to compute its offsets slightly differently\n  const isHoriz = ['right', 'left'].indexOf(placement) !== -1;\n  const mainSide = isHoriz ? 'top' : 'left';\n  const secondarySide = isHoriz ? 'left' : 'top';\n  const measurement = isHoriz ? 'height' : 'width';\n  const secondaryMeasurement = !isHoriz ? 'height' : 'width';\n\n  popperOffsets[mainSide] =\n    referenceOffsets[mainSide] +\n    referenceOffsets[measurement] / 2 -\n    popperRect[measurement] / 2;\n  if (placement === secondarySide) {\n    popperOffsets[secondarySide] =\n      referenceOffsets[secondarySide] - popperRect[secondaryMeasurement];\n  } else {\n    popperOffsets[secondarySide] =\n      referenceOffsets[getOppositePlacement(secondarySide)];\n  }\n\n  return popperOffsets;\n}\n", "/**\n * Mimics the `find` method of Array\n * @method\n * @memberof Popper.Utils\n * @argument {Array} arr\n * @argument prop\n * @argument value\n * @returns index or -1\n */\nexport default function find(arr, check) {\n  // use native find if supported\n  if (Array.prototype.find) {\n    return arr.find(check);\n  }\n\n  // use `filter` to obtain the same behavior of `find`\n  return arr.filter(check)[0];\n}\n", "import find from './find';\n\n/**\n * Return the index of the matching object\n * @method\n * @memberof Popper.Utils\n * @argument {Array} arr\n * @argument prop\n * @argument value\n * @returns index or -1\n */\nexport default function findIndex(arr, prop, value) {\n  // use native findIndex if supported\n  if (Array.prototype.findIndex) {\n    return arr.findIndex(cur => cur[prop] === value);\n  }\n\n  // use `find` + `indexOf` if `findIndex` isn't supported\n  const match = find(arr, obj => obj[prop] === value);\n  return arr.indexOf(match);\n}\n", "import isFunction from './isFunction';\nimport findIndex from './findIndex';\nimport getClientRect from '../utils/getClientRect';\n\n/**\n * Loop trough the list of modifiers and run them in order,\n * each of them will then edit the data object.\n * @method\n * @memberof Popper.Utils\n * @param {dataObject} data\n * @param {Array} modifiers\n * @param {String} ends - Optional modifier name used as stopper\n * @returns {dataObject}\n */\nexport default function runModifiers(modifiers, data, ends) {\n  const modifiersToRun = ends === undefined\n    ? modifiers\n    : modifiers.slice(0, findIndex(modifiers, 'name', ends));\n\n  modifiersToRun.forEach(modifier => {\n    if (modifier['function']) { // eslint-disable-line dot-notation\n      console.warn('`modifier.function` is deprecated, use `modifier.fn`!');\n    }\n    const fn = modifier['function'] || modifier.fn; // eslint-disable-line dot-notation\n    if (modifier.enabled && isFunction(fn)) {\n      // Add properties to offsets to make them a complete clientRect object\n      // we do this before each modifier to make sure the previous one doesn't\n      // mess with these values\n      data.offsets.popper = getClientRect(data.offsets.popper);\n      data.offsets.reference = getClientRect(data.offsets.reference);\n\n      data = fn(data, modifier);\n    }\n  });\n\n  return data;\n}\n", "import computeAutoPlacement from '../utils/computeAutoPlacement';\nimport getReferenceOffsets from '../utils/getReferenceOffsets';\nimport getPopperOffsets from '../utils/getPopperOffsets';\nimport runModifiers from '../utils/runModifiers';\n\n/**\n * Updates the position of the popper, computing the new offsets and applying\n * the new style.<br />\n * Prefer `scheduleUpdate` over `update` because of performance reasons.\n * @method\n * @memberof Popper\n */\nexport default function update() {\n  // if popper is destroyed, don't perform any further update\n  if (this.state.isDestroyed) {\n    return;\n  }\n\n  let data = {\n    instance: this,\n    styles: {},\n    arrowStyles: {},\n    attributes: {},\n    flipped: false,\n    offsets: {},\n  };\n\n  // compute reference element offsets\n  data.offsets.reference = getReferenceOffsets(\n    this.state,\n    this.popper,\n    this.reference,\n    this.options.positionFixed\n  );\n\n  // compute auto placement, store placement inside the data object,\n  // modifiers will be able to edit `placement` if needed\n  // and refer to originalPlacement to know the original value\n  data.placement = computeAutoPlacement(\n    this.options.placement,\n    data.offsets.reference,\n    this.popper,\n    this.reference,\n    this.options.modifiers.flip.boundariesElement,\n    this.options.modifiers.flip.padding\n  );\n\n  // store the computed placement inside `originalPlacement`\n  data.originalPlacement = data.placement;\n\n  data.positionFixed = this.options.positionFixed;\n\n  // compute the popper offsets\n  data.offsets.popper = getPopperOffsets(\n    this.popper,\n    data.offsets.reference,\n    data.placement\n  );\n\n  data.offsets.popper.position = this.options.positionFixed\n    ? 'fixed'\n    : 'absolute';\n\n  // run the modifiers\n  data = runModifiers(this.modifiers, data);\n\n  // the first `update` will call `onCreate` callback\n  // the other ones will call `onUpdate` callback\n  if (!this.state.isCreated) {\n    this.state.isCreated = true;\n    this.options.onCreate(data);\n  } else {\n    this.options.onUpdate(data);\n  }\n}\n", "/**\n * Helper used to know if the given modifier is enabled.\n * @method\n * @memberof Popper.Utils\n * @returns {Boolean}\n */\nexport default function isModifierEnabled(modifiers, modifierName) {\n  return modifiers.some(\n    ({ name, enabled }) => enabled && name === modifierName\n  );\n}\n", "/**\n * Get the prefixed supported property name\n * @method\n * @memberof Popper.Utils\n * @argument {String} property (camelCase)\n * @returns {String} prefixed property (camelCase or PascalCase, depending on the vendor prefix)\n */\nexport default function getSupportedPropertyName(property) {\n  const prefixes = [false, 'ms', 'Webkit', 'Moz', 'O'];\n  const upperProp = property.charAt(0).toUpperCase() + property.slice(1);\n\n  for (let i = 0; i < prefixes.length; i++) {\n    const prefix = prefixes[i];\n    const toCheck = prefix ? `${prefix}${upperProp}` : property;\n    if (typeof document.body.style[toCheck] !== 'undefined') {\n      return toCheck;\n    }\n  }\n  return null;\n}\n", "import isModifierEnabled from '../utils/isModifierEnabled';\nimport getSupportedPropertyName from '../utils/getSupportedPropertyName';\n\n/**\n * Destroys the popper.\n * @method\n * @memberof Popper\n */\nexport default function destroy() {\n  this.state.isDestroyed = true;\n\n  // touch DOM only if `applyStyle` modifier is enabled\n  if (isModifierEnabled(this.modifiers, 'applyStyle')) {\n    this.popper.removeAttribute('x-placement');\n    this.popper.style.position = '';\n    this.popper.style.top = '';\n    this.popper.style.left = '';\n    this.popper.style.right = '';\n    this.popper.style.bottom = '';\n    this.popper.style.willChange = '';\n    this.popper.style[getSupportedPropertyName('transform')] = '';\n  }\n\n  this.disableEventListeners();\n\n  // remove the popper if user explicitly asked for the deletion on destroy\n  // do not use `remove` because IE11 doesn't support it\n  if (this.options.removeOnDestroy) {\n    this.popper.parentNode.removeChild(this.popper);\n  }\n  return this;\n}\n", "/**\n * Get the window associated with the element\n * @argument {Element} element\n * @returns {Window}\n */\nexport default function getWindow(element) {\n  const ownerDocument = element.ownerDocument;\n  return ownerDocument ? ownerDocument.defaultView : window;\n}\n", "import getScrollParent from './getScrollParent';\nimport getWindow from './getWindow';\n\nfunction attachToScrollParents(scrollParent, event, callback, scrollParents) {\n  const isBody = scrollParent.nodeName === 'BODY';\n  const target = isBody ? scrollParent.ownerDocument.defaultView : scrollParent;\n  target.addEventListener(event, callback, { passive: true });\n\n  if (!isBody) {\n    attachToScrollParents(\n      getScrollParent(target.parentNode),\n      event,\n      callback,\n      scrollParents\n    );\n  }\n  scrollParents.push(target);\n}\n\n/**\n * Setup needed event listeners used to update the popper position\n * @method\n * @memberof Popper.Utils\n * @private\n */\nexport default function setupEventListeners(\n  reference,\n  options,\n  state,\n  updateBound\n) {\n  // Resize event listener on window\n  state.updateBound = updateBound;\n  getWindow(reference).addEventListener('resize', state.updateBound, { passive: true });\n\n  // Scroll event listener on scroll parents\n  const scrollElement = getScrollParent(reference);\n  attachToScrollParents(\n    scrollElement,\n    'scroll',\n    state.updateBound,\n    state.scrollParents\n  );\n  state.scrollElement = scrollElement;\n  state.eventsEnabled = true;\n\n  return state;\n}\n", "import setupEventListeners from '../utils/setupEventListeners';\n\n/**\n * It will add resize/scroll events and start recalculating\n * position of the popper element when they are triggered.\n * @method\n * @memberof Popper\n */\nexport default function enableEventListeners() {\n  if (!this.state.eventsEnabled) {\n    this.state = setupEventListeners(\n      this.reference,\n      this.options,\n      this.state,\n      this.scheduleUpdate\n    );\n  }\n}\n", "import getWindow from './getWindow';\n\n/**\n * Remove event listeners used to update the popper position\n * @method\n * @memberof Popper.Utils\n * @private\n */\nexport default function removeEventListeners(reference, state) {\n  // Remove resize event listener on window\n  getWindow(reference).removeEventListener('resize', state.updateBound);\n\n  // Remove scroll event listener on scroll parents\n  state.scrollParents.forEach(target => {\n    target.removeEventListener('scroll', state.updateBound);\n  });\n\n  // Reset state\n  state.updateBound = null;\n  state.scrollParents = [];\n  state.scrollElement = null;\n  state.eventsEnabled = false;\n  return state;\n}\n", "import removeEventListeners from '../utils/removeEventListeners';\n\n/**\n * It will remove resize/scroll events and won't recalculate popper position\n * when they are triggered. It also won't trigger `onUpdate` callback anymore,\n * unless you call `update` method manually.\n * @method\n * @memberof Popper\n */\nexport default function disableEventListeners() {\n  if (this.state.eventsEnabled) {\n    cancelAnimationFrame(this.scheduleUpdate);\n    this.state = removeEventListeners(this.reference, this.state);\n  }\n}\n", "/**\n * Tells if a given input is a number\n * @method\n * @memberof Popper.Utils\n * @param {*} input to check\n * @return {Boolean}\n */\nexport default function isNumeric(n) {\n  return n !== '' && !isNaN(parseFloat(n)) && isFinite(n);\n}\n", "import isNumeric from './isNumeric';\n\n/**\n * Set the style to the given popper\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element - Element to apply the style to\n * @argument {Object} styles\n * Object with a list of properties and values which will be applied to the element\n */\nexport default function setStyles(element, styles) {\n  Object.keys(styles).forEach(prop => {\n    let unit = '';\n    // add unit if the value is numeric and is one of the following\n    if (\n      ['width', 'height', 'top', 'right', 'bottom', 'left'].indexOf(prop) !==\n        -1 &&\n      isNumeric(styles[prop])\n    ) {\n      unit = 'px';\n    }\n    element.style[prop] = styles[prop] + unit;\n  });\n}\n", "/**\n * Set the attributes to the given popper\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element - Element to apply the attributes to\n * @argument {Object} styles\n * Object with a list of properties and values which will be applied to the element\n */\nexport default function setAttributes(element, attributes) {\n  Object.keys(attributes).forEach(function(prop) {\n    const value = attributes[prop];\n    if (value !== false) {\n      element.setAttribute(prop, attributes[prop]);\n    } else {\n      element.removeAttribute(prop);\n    }\n  });\n}\n", "import setStyles from '../utils/setStyles';\nimport setAttributes from '../utils/setAttributes';\nimport getReferenceOffsets from '../utils/getReferenceOffsets';\nimport computeAutoPlacement from '../utils/computeAutoPlacement';\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Object} data.styles - List of style properties - values to apply to popper element\n * @argument {Object} data.attributes - List of attribute properties - values to apply to popper element\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The same data object\n */\nexport default function applyStyle(data) {\n  // any property present in `data.styles` will be applied to the popper,\n  // in this way we can make the 3rd party modifiers add custom styles to it\n  // Be aware, modifiers could override the properties defined in the previous\n  // lines of this modifier!\n  setStyles(data.instance.popper, data.styles);\n\n  // any property present in `data.attributes` will be applied to the popper,\n  // they will be set as HTML attributes of the element\n  setAttributes(data.instance.popper, data.attributes);\n\n  // if arrowElement is defined and arrowStyles has some properties\n  if (data.arrowElement && Object.keys(data.arrowStyles).length) {\n    setStyles(data.arrowElement, data.arrowStyles);\n  }\n\n  return data;\n}\n\n/**\n * Set the x-placement attribute before everything else because it could be used\n * to add margins to the popper margins needs to be calculated to get the\n * correct popper offsets.\n * @method\n * @memberof Popper.modifiers\n * @param {HTMLElement} reference - The reference element used to position the popper\n * @param {HTMLElement} popper - The HTML element used as popper\n * @param {Object} options - Popper.js options\n */\nexport function applyStyleOnLoad(\n  reference,\n  popper,\n  options,\n  modifierOptions,\n  state\n) {\n  // compute reference element offsets\n  const referenceOffsets = getReferenceOffsets(state, popper, reference, options.positionFixed);\n\n  // compute auto placement, store placement inside the data object,\n  // modifiers will be able to edit `placement` if needed\n  // and refer to originalPlacement to know the original value\n  const placement = computeAutoPlacement(\n    options.placement,\n    referenceOffsets,\n    popper,\n    reference,\n    options.modifiers.flip.boundariesElement,\n    options.modifiers.flip.padding\n  );\n\n  popper.setAttribute('x-placement', placement);\n\n  // Apply `position` to popper before anything else because\n  // without the position applied we can't guarantee correct computations\n  setStyles(popper, { position: options.positionFixed ? 'fixed' : 'absolute' });\n\n  return options;\n}\n", "/**\n * @function\n * @memberof Popper.Utils\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Boolean} shouldRound - If the offsets should be rounded at all\n * @returns {Object} The popper's position offsets rounded\n *\n * The tale of pixel-perfect positioning. It's still not 100% perfect, but as\n * good as it can be within reason.\n * Discussion here: https://github.com/FezVrasta/popper.js/pull/715\n *\n * Low DPI screens cause a popper to be blurry if not using full pixels (Safari\n * as well on High DPI screens).\n *\n * Firefox prefers no rounding for positioning and does not have blurriness on\n * high DPI screens.\n *\n * Only horizontal placement and left/right values need to be considered.\n */\nexport default function getRoundedOffsets(data, shouldRound) {\n  const { popper, reference } = data.offsets;\n  const { round, floor } = Math;\n  const noRound = v => v;\n  \n  const referenceWidth = round(reference.width);\n  const popperWidth = round(popper.width);\n  \n  const isVertical = ['left', 'right'].indexOf(data.placement) !== -1;\n  const isVariation = data.placement.indexOf('-') !== -1;\n  const sameWidthParity = referenceWidth % 2 === popperWidth % 2;\n  const bothOddWidth = referenceWidth % 2 === 1 && popperWidth % 2 === 1;\n\n  const horizontalToInteger = !shouldRound\n    ? noRound\n    : isVertical || isVariation || sameWidthParity\n    ? round\n    : floor;\n  const verticalToInteger = !shouldRound ? noRound : round;\n\n  return {\n    left: horizontalToInteger(\n      bothOddWidth && !isVariation && shouldRound\n        ? popper.left - 1\n        : popper.left\n    ),\n    top: verticalToInteger(popper.top),\n    bottom: verticalToInteger(popper.bottom),\n    right: horizontalToInteger(popper.right),\n  };\n}\n", "import getSupportedPropertyName from '../utils/getSupportedPropertyName';\nimport find from '../utils/find';\nimport getOffsetParent from '../utils/getOffsetParent';\nimport getBoundingClientRect from '../utils/getBoundingClientRect';\nimport getRoundedOffsets from '../utils/getRoundedOffsets';\nimport isBrowser from '../utils/isBrowser';\n\nconst isFirefox = isBrowser && /Firefox/i.test(navigator.userAgent);\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nexport default function computeStyle(data, options) {\n  const { x, y } = options;\n  const { popper } = data.offsets;\n\n  // Remove this legacy support in Popper.js v2\n  const legacyGpuAccelerationOption = find(\n    data.instance.modifiers,\n    modifier => modifier.name === 'applyStyle'\n  ).gpuAcceleration;\n  if (legacyGpuAccelerationOption !== undefined) {\n    console.warn(\n      'WARNING: `gpuAcceleration` option moved to `computeStyle` modifier and will not be supported in future versions of Popper.js!'\n    );\n  }\n  const gpuAcceleration =\n    legacyGpuAccelerationOption !== undefined\n      ? legacyGpuAccelerationOption\n      : options.gpuAcceleration;\n\n  const offsetParent = getOffsetParent(data.instance.popper);\n  const offsetParentRect = getBoundingClientRect(offsetParent);\n\n  // Styles\n  const styles = {\n    position: popper.position,\n  };\n\n  const offsets = getRoundedOffsets(\n    data,\n    window.devicePixelRatio < 2 || !isFirefox\n  );\n\n  const sideA = x === 'bottom' ? 'top' : 'bottom';\n  const sideB = y === 'right' ? 'left' : 'right';\n\n  // if gpuAcceleration is set to `true` and transform is supported,\n  //  we use `translate3d` to apply the position to the popper we\n  // automatically use the supported prefixed version if needed\n  const prefixedProperty = getSupportedPropertyName('transform');\n\n  // now, let's make a step back and look at this code closely (wtf?)\n  // If the content of the popper grows once it's been positioned, it\n  // may happen that the popper gets misplaced because of the new content\n  // overflowing its reference element\n  // To avoid this problem, we provide two options (x and y), which allow\n  // the consumer to define the offset origin.\n  // If we position a popper on top of a reference element, we can set\n  // `x` to `top` to make the popper grow towards its top instead of\n  // its bottom.\n  let left, top;\n  if (sideA === 'bottom') {\n    // when offsetParent is <html> the positioning is relative to the bottom of the screen (excluding the scrollbar)\n    // and not the bottom of the html element\n    if (offsetParent.nodeName === 'HTML') {\n      top = -offsetParent.clientHeight + offsets.bottom;\n    } else {\n      top = -offsetParentRect.height + offsets.bottom;\n    }\n  } else {\n    top = offsets.top;\n  }\n  if (sideB === 'right') {\n    if (offsetParent.nodeName === 'HTML') {\n      left = -offsetParent.clientWidth + offsets.right;\n    } else {\n      left = -offsetParentRect.width + offsets.right;\n    }\n  } else {\n    left = offsets.left;\n  }\n  if (gpuAcceleration && prefixedProperty) {\n    styles[prefixedProperty] = `translate3d(${left}px, ${top}px, 0)`;\n    styles[sideA] = 0;\n    styles[sideB] = 0;\n    styles.willChange = 'transform';\n  } else {\n    // othwerise, we use the standard `top`, `left`, `bottom` and `right` properties\n    const invertTop = sideA === 'bottom' ? -1 : 1;\n    const invertLeft = sideB === 'right' ? -1 : 1;\n    styles[sideA] = top * invertTop;\n    styles[sideB] = left * invertLeft;\n    styles.willChange = `${sideA}, ${sideB}`;\n  }\n\n  // Attributes\n  const attributes = {\n    'x-placement': data.placement,\n  };\n\n  // Update `data` attributes, styles and arrowStyles\n  data.attributes = { ...attributes, ...data.attributes };\n  data.styles = { ...styles, ...data.styles };\n  data.arrowStyles = { ...data.offsets.arrow, ...data.arrowStyles };\n\n  return data;\n}\n", "import find from './find';\n\n/**\n * Helper used to know if the given modifier depends from another one.<br />\n * It checks if the needed modifier is listed and enabled.\n * @method\n * @memberof Popper.Utils\n * @param {Array} modifiers - list of modifiers\n * @param {String} requestingName - name of requesting modifier\n * @param {String} requestedName - name of requested modifier\n * @returns {Boolean}\n */\nexport default function isModifierRequired(\n  modifiers,\n  requestingName,\n  requestedName\n) {\n  const requesting = find(modifiers, ({ name }) => name === requestingName);\n\n  const isRequired =\n    !!requesting &&\n    modifiers.some(modifier => {\n      return (\n        modifier.name === requestedName &&\n        modifier.enabled &&\n        modifier.order < requesting.order\n      );\n    });\n\n  if (!isRequired) {\n    const requesting = `\\`${requestingName}\\``;\n    const requested = `\\`${requestedName}\\``;\n    console.warn(\n      `${requested} modifier is required by ${requesting} modifier in order to work, be sure to include it before ${requesting}!`\n    );\n  }\n  return isRequired;\n}\n", "import getClientRect from '../utils/getClientRect';\nimport getOuterSizes from '../utils/getOuterSizes';\nimport isModifierRequired from '../utils/isModifierRequired';\nimport getStyleComputedProperty from '../utils/getStyleComputedProperty';\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nexport default function arrow(data, options) {\n  // arrow depends on keepTogether in order to work\n  if (!isModifierRequired(data.instance.modifiers, 'arrow', 'keepTogether')) {\n    return data;\n  }\n\n  let arrowElement = options.element;\n\n  // if arrowElement is a string, suppose it's a CSS selector\n  if (typeof arrowElement === 'string') {\n    arrowElement = data.instance.popper.querySelector(arrowElement);\n\n    // if arrowElement is not found, don't run the modifier\n    if (!arrowElement) {\n      return data;\n    }\n  } else {\n    // if the arrowElement isn't a query selector we must check that the\n    // provided DOM node is child of its popper node\n    if (!data.instance.popper.contains(arrowElement)) {\n      console.warn(\n        'WARNING: `arrow.element` must be child of its popper element!'\n      );\n      return data;\n    }\n  }\n\n  const placement = data.placement.split('-')[0];\n  const { popper, reference } = data.offsets;\n  const isVertical = ['left', 'right'].indexOf(placement) !== -1;\n\n  const len = isVertical ? 'height' : 'width';\n  const sideCapitalized = isVertical ? 'Top' : 'Left';\n  const side = sideCapitalized.toLowerCase();\n  const altSide = isVertical ? 'left' : 'top';\n  const opSide = isVertical ? 'bottom' : 'right';\n  const arrowElementSize = getOuterSizes(arrowElement)[len];\n\n  //\n  // extends keepTogether behavior making sure the popper and its\n  // reference have enough pixels in conjunction\n  //\n\n  // top/left side\n  if (reference[opSide] - arrowElementSize < popper[side]) {\n    data.offsets.popper[side] -=\n      popper[side] - (reference[opSide] - arrowElementSize);\n  }\n  // bottom/right side\n  if (reference[side] + arrowElementSize > popper[opSide]) {\n    data.offsets.popper[side] +=\n      reference[side] + arrowElementSize - popper[opSide];\n  }\n  data.offsets.popper = getClientRect(data.offsets.popper);\n\n  // compute center of the popper\n  const center = reference[side] + reference[len] / 2 - arrowElementSize / 2;\n\n  // Compute the sideValue using the updated popper offsets\n  // take popper margin in account because we don't have this info available\n  const css = getStyleComputedProperty(data.instance.popper);\n  const popperMarginSide = parseFloat(css[`margin${sideCapitalized}`]);\n  const popperBorderSide = parseFloat(css[`border${sideCapitalized}Width`]);\n  let sideValue =\n    center - data.offsets.popper[side] - popperMarginSide - popperBorderSide;\n\n  // prevent arrowElement from being placed not contiguously to its popper\n  sideValue = Math.max(Math.min(popper[len] - arrowElementSize, sideValue), 0);\n\n  data.arrowElement = arrowElement;\n  data.offsets.arrow = {\n    [side]: Math.round(sideValue),\n    [altSide]: '', // make sure to unset any eventual altSide value from the DOM node\n  };\n\n  return data;\n}\n", "/**\n * Get the opposite placement variation of the given one\n * @method\n * @memberof Popper.Utils\n * @argument {String} placement variation\n * @returns {String} flipped placement variation\n */\nexport default function getOppositeVariation(variation) {\n  if (variation === 'end') {\n    return 'start';\n  } else if (variation === 'start') {\n    return 'end';\n  }\n  return variation;\n}\n", "/**\n * List of accepted placements to use as values of the `placement` option.<br />\n * Valid placements are:\n * - `auto`\n * - `top`\n * - `right`\n * - `bottom`\n * - `left`\n *\n * Each placement can have a variation from this list:\n * - `-start`\n * - `-end`\n *\n * Variations are interpreted easily if you think of them as the left to right\n * written languages. Horizontally (`top` and `bottom`), `start` is left and `end`\n * is right.<br />\n * Vertically (`left` and `right`), `start` is top and `end` is bottom.\n *\n * Some valid examples are:\n * - `top-end` (on top of reference, right aligned)\n * - `right-start` (on right of reference, top aligned)\n * - `bottom` (on bottom, centered)\n * - `auto-end` (on the side with more space available, alignment depends by placement)\n *\n * @static\n * @type {Array}\n * @enum {String}\n * @readonly\n * @method placements\n * @memberof Popper\n */\nexport default [\n  'auto-start',\n  'auto',\n  'auto-end',\n  'top-start',\n  'top',\n  'top-end',\n  'right-start',\n  'right',\n  'right-end',\n  'bottom-end',\n  'bottom',\n  'bottom-start',\n  'left-end',\n  'left',\n  'left-start',\n];\n", "import placements from '../methods/placements';\n\n// Get rid of `auto` `auto-start` and `auto-end`\nconst validPlacements = placements.slice(3);\n\n/**\n * Given an initial placement, returns all the subsequent placements\n * clockwise (or counter-clockwise).\n *\n * @method\n * @memberof Popper.Utils\n * @argument {String} placement - A valid placement (it accepts variations)\n * @argument {Boolean} counter - Set to true to walk the placements counterclockwise\n * @returns {Array} placements including their variations\n */\nexport default function clockwise(placement, counter = false) {\n  const index = validPlacements.indexOf(placement);\n  const arr = validPlacements\n    .slice(index + 1)\n    .concat(validPlacements.slice(0, index));\n  return counter ? arr.reverse() : arr;\n}\n", "import getOppositePlacement from '../utils/getOppositePlacement';\nimport getOppositeVariation from '../utils/getOppositeVariation';\nimport getPopperOffsets from '../utils/getPopperOffsets';\nimport runModifiers from '../utils/runModifiers';\nimport getBoundaries from '../utils/getBoundaries';\nimport isModifierEnabled from '../utils/isModifierEnabled';\nimport clockwise from '../utils/clockwise';\n\nconst BEHAVIORS = {\n  FLIP: 'flip',\n  CLOCKWISE: 'clockwise',\n  COUNTERCLOCKWISE: 'counterclockwise',\n};\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nexport default function flip(data, options) {\n  // if `inner` modifier is enabled, we can't use the `flip` modifier\n  if (isModifierEnabled(data.instance.modifiers, 'inner')) {\n    return data;\n  }\n\n  if (data.flipped && data.placement === data.originalPlacement) {\n    // seems like flip is trying to loop, probably there's not enough space on any of the flippable sides\n    return data;\n  }\n\n  const boundaries = getBoundaries(\n    data.instance.popper,\n    data.instance.reference,\n    options.padding,\n    options.boundariesElement,\n    data.positionFixed\n  );\n\n  let placement = data.placement.split('-')[0];\n  let placementOpposite = getOppositePlacement(placement);\n  let variation = data.placement.split('-')[1] || '';\n\n  let flipOrder = [];\n\n  switch (options.behavior) {\n    case BEHAVIORS.FLIP:\n      flipOrder = [placement, placementOpposite];\n      break;\n    case BEHAVIORS.CLOCKWISE:\n      flipOrder = clockwise(placement);\n      break;\n    case BEHAVIORS.COUNTERCLOCKWISE:\n      flipOrder = clockwise(placement, true);\n      break;\n    default:\n      flipOrder = options.behavior;\n  }\n\n  flipOrder.forEach((step, index) => {\n    if (placement !== step || flipOrder.length === index + 1) {\n      return data;\n    }\n\n    placement = data.placement.split('-')[0];\n    placementOpposite = getOppositePlacement(placement);\n\n    const popperOffsets = data.offsets.popper;\n    const refOffsets = data.offsets.reference;\n\n    // using floor because the reference offsets may contain decimals we are not going to consider here\n    const floor = Math.floor;\n    const overlapsRef =\n      (placement === 'left' &&\n        floor(popperOffsets.right) > floor(refOffsets.left)) ||\n      (placement === 'right' &&\n        floor(popperOffsets.left) < floor(refOffsets.right)) ||\n      (placement === 'top' &&\n        floor(popperOffsets.bottom) > floor(refOffsets.top)) ||\n      (placement === 'bottom' &&\n        floor(popperOffsets.top) < floor(refOffsets.bottom));\n\n    const overflowsLeft = floor(popperOffsets.left) < floor(boundaries.left);\n    const overflowsRight = floor(popperOffsets.right) > floor(boundaries.right);\n    const overflowsTop = floor(popperOffsets.top) < floor(boundaries.top);\n    const overflowsBottom =\n      floor(popperOffsets.bottom) > floor(boundaries.bottom);\n\n    const overflowsBoundaries =\n      (placement === 'left' && overflowsLeft) ||\n      (placement === 'right' && overflowsRight) ||\n      (placement === 'top' && overflowsTop) ||\n      (placement === 'bottom' && overflowsBottom);\n\n    // flip the variation if required\n    const isVertical = ['top', 'bottom'].indexOf(placement) !== -1;\n\n    // flips variation if reference element overflows boundaries\n    const flippedVariationByRef =\n      !!options.flipVariations &&\n      ((isVertical && variation === 'start' && overflowsLeft) ||\n        (isVertical && variation === 'end' && overflowsRight) ||\n        (!isVertical && variation === 'start' && overflowsTop) ||\n        (!isVertical && variation === 'end' && overflowsBottom));\n\n    // flips variation if popper content overflows boundaries\n    const flippedVariationByContent =\n      !!options.flipVariationsByContent &&\n      ((isVertical && variation === 'start' && overflowsRight) ||\n        (isVertical && variation === 'end' && overflowsLeft) ||\n        (!isVertical && variation === 'start' && overflowsBottom) ||\n        (!isVertical && variation === 'end' && overflowsTop));\n\n    const flippedVariation = flippedVariationByRef || flippedVariationByContent;\n\n    if (overlapsRef || overflowsBoundaries || flippedVariation) {\n      // this boolean to detect any flip loop\n      data.flipped = true;\n\n      if (overlapsRef || overflowsBoundaries) {\n        placement = flipOrder[index + 1];\n      }\n\n      if (flippedVariation) {\n        variation = getOppositeVariation(variation);\n      }\n\n      data.placement = placement + (variation ? '-' + variation : '');\n\n      // this object contains `position`, we want to preserve it along with\n      // any additional property we may add in the future\n      data.offsets.popper = {\n        ...data.offsets.popper,\n        ...getPopperOffsets(\n          data.instance.popper,\n          data.offsets.reference,\n          data.placement\n        ),\n      };\n\n      data = runModifiers(data.instance.modifiers, data, 'flip');\n    }\n  });\n  return data;\n}\n", "/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nexport default function keepTogether(data) {\n  const { popper, reference } = data.offsets;\n  const placement = data.placement.split('-')[0];\n  const floor = Math.floor;\n  const isVertical = ['top', 'bottom'].indexOf(placement) !== -1;\n  const side = isVertical ? 'right' : 'bottom';\n  const opSide = isVertical ? 'left' : 'top';\n  const measurement = isVertical ? 'width' : 'height';\n\n  if (popper[side] < floor(reference[opSide])) {\n    data.offsets.popper[opSide] =\n      floor(reference[opSide]) - popper[measurement];\n  }\n  if (popper[opSide] > floor(reference[side])) {\n    data.offsets.popper[opSide] = floor(reference[side]);\n  }\n\n  return data;\n}\n", "import isNumeric from '../utils/isNumeric';\nimport getClientRect from '../utils/getClientRect';\nimport find from '../utils/find';\n\n/**\n * Converts a string containing value + unit into a px value number\n * @function\n * @memberof {modifiers~offset}\n * @private\n * @argument {String} str - Value + unit string\n * @argument {String} measurement - `height` or `width`\n * @argument {Object} popperOffsets\n * @argument {Object} referenceOffsets\n * @returns {Number|String}\n * Value in pixels, or original string if no values were extracted\n */\nexport function toValue(str, measurement, popperOffsets, referenceOffsets) {\n  // separate value from unit\n  const split = str.match(/((?:\\-|\\+)?\\d*\\.?\\d*)(.*)/);\n  const value = +split[1];\n  const unit = split[2];\n\n  // If it's not a number it's an operator, I guess\n  if (!value) {\n    return str;\n  }\n\n  if (unit.indexOf('%') === 0) {\n    let element;\n    switch (unit) {\n      case '%p':\n        element = popperOffsets;\n        break;\n      case '%':\n      case '%r':\n      default:\n        element = referenceOffsets;\n    }\n\n    const rect = getClientRect(element);\n    return rect[measurement] / 100 * value;\n  } else if (unit === 'vh' || unit === 'vw') {\n    // if is a vh or vw, we calculate the size based on the viewport\n    let size;\n    if (unit === 'vh') {\n      size = Math.max(\n        document.documentElement.clientHeight,\n        window.innerHeight || 0\n      );\n    } else {\n      size = Math.max(\n        document.documentElement.clientWidth,\n        window.innerWidth || 0\n      );\n    }\n    return size / 100 * value;\n  } else {\n    // if is an explicit pixel unit, we get rid of the unit and keep the value\n    // if is an implicit unit, it's px, and we return just the value\n    return value;\n  }\n}\n\n/**\n * Parse an `offset` string to extrapolate `x` and `y` numeric offsets.\n * @function\n * @memberof {modifiers~offset}\n * @private\n * @argument {String} offset\n * @argument {Object} popperOffsets\n * @argument {Object} referenceOffsets\n * @argument {String} basePlacement\n * @returns {Array} a two cells array with x and y offsets in numbers\n */\nexport function parseOffset(\n  offset,\n  popperOffsets,\n  referenceOffsets,\n  basePlacement\n) {\n  const offsets = [0, 0];\n\n  // Use height if placement is left or right and index is 0 otherwise use width\n  // in this way the first offset will use an axis and the second one\n  // will use the other one\n  const useHeight = ['right', 'left'].indexOf(basePlacement) !== -1;\n\n  // Split the offset string to obtain a list of values and operands\n  // The regex addresses values with the plus or minus sign in front (+10, -20, etc)\n  const fragments = offset.split(/(\\+|\\-)/).map(frag => frag.trim());\n\n  // Detect if the offset string contains a pair of values or a single one\n  // they could be separated by comma or space\n  const divider = fragments.indexOf(\n    find(fragments, frag => frag.search(/,|\\s/) !== -1)\n  );\n\n  if (fragments[divider] && fragments[divider].indexOf(',') === -1) {\n    console.warn(\n      'Offsets separated by white space(s) are deprecated, use a comma (,) instead.'\n    );\n  }\n\n  // If divider is found, we divide the list of values and operands to divide\n  // them by ofset X and Y.\n  const splitRegex = /\\s*,\\s*|\\s+/;\n  let ops = divider !== -1\n    ? [\n        fragments\n          .slice(0, divider)\n          .concat([fragments[divider].split(splitRegex)[0]]),\n        [fragments[divider].split(splitRegex)[1]].concat(\n          fragments.slice(divider + 1)\n        ),\n      ]\n    : [fragments];\n\n  // Convert the values with units to absolute pixels to allow our computations\n  ops = ops.map((op, index) => {\n    // Most of the units rely on the orientation of the popper\n    const measurement = (index === 1 ? !useHeight : useHeight)\n      ? 'height'\n      : 'width';\n    let mergeWithPrevious = false;\n    return (\n      op\n        // This aggregates any `+` or `-` sign that aren't considered operators\n        // e.g.: 10 + +5 => [10, +, +5]\n        .reduce((a, b) => {\n          if (a[a.length - 1] === '' && ['+', '-'].indexOf(b) !== -1) {\n            a[a.length - 1] = b;\n            mergeWithPrevious = true;\n            return a;\n          } else if (mergeWithPrevious) {\n            a[a.length - 1] += b;\n            mergeWithPrevious = false;\n            return a;\n          } else {\n            return a.concat(b);\n          }\n        }, [])\n        // Here we convert the string values into number values (in px)\n        .map(str => toValue(str, measurement, popperOffsets, referenceOffsets))\n    );\n  });\n\n  // Loop trough the offsets arrays and execute the operations\n  ops.forEach((op, index) => {\n    op.forEach((frag, index2) => {\n      if (isNumeric(frag)) {\n        offsets[index] += frag * (op[index2 - 1] === '-' ? -1 : 1);\n      }\n    });\n  });\n  return offsets;\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @argument {Number|String} options.offset=0\n * The offset value as described in the modifier description\n * @returns {Object} The data object, properly modified\n */\nexport default function offset(data, { offset }) {\n  const { placement, offsets: { popper, reference } } = data;\n  const basePlacement = placement.split('-')[0];\n\n  let offsets;\n  if (isNumeric(+offset)) {\n    offsets = [+offset, 0];\n  } else {\n    offsets = parseOffset(offset, popper, reference, basePlacement);\n  }\n\n  if (basePlacement === 'left') {\n    popper.top += offsets[0];\n    popper.left -= offsets[1];\n  } else if (basePlacement === 'right') {\n    popper.top += offsets[0];\n    popper.left += offsets[1];\n  } else if (basePlacement === 'top') {\n    popper.left += offsets[0];\n    popper.top -= offsets[1];\n  } else if (basePlacement === 'bottom') {\n    popper.left += offsets[0];\n    popper.top += offsets[1];\n  }\n\n  data.popper = popper;\n  return data;\n}\n", "import getOffsetParent from '../utils/getOffsetParent';\nimport getBoundaries from '../utils/getBoundaries';\nimport getSupportedPropertyName from '../utils/getSupportedPropertyName';\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nexport default function preventOverflow(data, options) {\n  let boundariesElement =\n    options.boundariesElement || getOffsetParent(data.instance.popper);\n\n  // If offsetParent is the reference element, we really want to\n  // go one step up and use the next offsetParent as reference to\n  // avoid to make this modifier completely useless and look like broken\n  if (data.instance.reference === boundariesElement) {\n    boundariesElement = getOffsetParent(boundariesElement);\n  }\n\n  // NOTE: DOM access here\n  // resets the popper's position so that the document size can be calculated excluding\n  // the size of the popper element itself\n  const transformProp = getSupportedPropertyName('transform');\n  const popperStyles = data.instance.popper.style; // assignment to help minification\n  const { top, left, [transformProp]: transform } = popperStyles;\n  popperStyles.top = '';\n  popperStyles.left = '';\n  popperStyles[transformProp] = '';\n\n  const boundaries = getBoundaries(\n    data.instance.popper,\n    data.instance.reference,\n    options.padding,\n    boundariesElement,\n    data.positionFixed\n  );\n\n  // NOTE: DOM access here\n  // restores the original style properties after the offsets have been computed\n  popperStyles.top = top;\n  popperStyles.left = left;\n  popperStyles[transformProp] = transform;\n\n  options.boundaries = boundaries;\n\n  const order = options.priority;\n  let popper = data.offsets.popper;\n\n  const check = {\n    primary(placement) {\n      let value = popper[placement];\n      if (\n        popper[placement] < boundaries[placement] &&\n        !options.escapeWithReference\n      ) {\n        value = Math.max(popper[placement], boundaries[placement]);\n      }\n      return { [placement]: value };\n    },\n    secondary(placement) {\n      const mainSide = placement === 'right' ? 'left' : 'top';\n      let value = popper[mainSide];\n      if (\n        popper[placement] > boundaries[placement] &&\n        !options.escapeWithReference\n      ) {\n        value = Math.min(\n          popper[mainSide],\n          boundaries[placement] -\n            (placement === 'right' ? popper.width : popper.height)\n        );\n      }\n      return { [mainSide]: value };\n    },\n  };\n\n  order.forEach(placement => {\n    const side =\n      ['left', 'top'].indexOf(placement) !== -1 ? 'primary' : 'secondary';\n    popper = { ...popper, ...check[side](placement) };\n  });\n\n  data.offsets.popper = popper;\n\n  return data;\n}\n", "/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nexport default function shift(data) {\n  const placement = data.placement;\n  const basePlacement = placement.split('-')[0];\n  const shiftvariation = placement.split('-')[1];\n\n  // if shift shiftvariation is specified, run the modifier\n  if (shiftvariation) {\n    const { reference, popper } = data.offsets;\n    const isVertical = ['bottom', 'top'].indexOf(basePlacement) !== -1;\n    const side = isVertical ? 'left' : 'top';\n    const measurement = isVertical ? 'width' : 'height';\n\n    const shiftOffsets = {\n      start: { [side]: reference[side] },\n      end: {\n        [side]: reference[side] + reference[measurement] - popper[measurement],\n      },\n    };\n\n    data.offsets.popper = { ...popper, ...shiftOffsets[shiftvariation] };\n  }\n\n  return data;\n}\n", "import isModifierRequired from '../utils/isModifierRequired';\nimport find from '../utils/find';\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nexport default function hide(data) {\n  if (!isModifierRequired(data.instance.modifiers, 'hide', 'preventOverflow')) {\n    return data;\n  }\n\n  const refRect = data.offsets.reference;\n  const bound = find(\n    data.instance.modifiers,\n    modifier => modifier.name === 'preventOverflow'\n  ).boundaries;\n\n  if (\n    refRect.bottom < bound.top ||\n    refRect.left > bound.right ||\n    refRect.top > bound.bottom ||\n    refRect.right < bound.left\n  ) {\n    // Avoid unnecessary DOM access if visibility hasn't changed\n    if (data.hide === true) {\n      return data;\n    }\n\n    data.hide = true;\n    data.attributes['x-out-of-boundaries'] = '';\n  } else {\n    // Avoid unnecessary DOM access if visibility hasn't changed\n    if (data.hide === false) {\n      return data;\n    }\n\n    data.hide = false;\n    data.attributes['x-out-of-boundaries'] = false;\n  }\n\n  return data;\n}\n", "import getClientRect from '../utils/getClientRect';\nimport getOppositePlacement from '../utils/getOppositePlacement';\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nexport default function inner(data) {\n  const placement = data.placement;\n  const basePlacement = placement.split('-')[0];\n  const { popper, reference } = data.offsets;\n  const isHoriz = ['left', 'right'].indexOf(basePlacement) !== -1;\n\n  const subtractLength = ['top', 'left'].indexOf(basePlacement) === -1;\n\n  popper[isHoriz ? 'left' : 'top'] =\n    reference[basePlacement] -\n    (subtractLength ? popper[isHoriz ? 'width' : 'height'] : 0);\n\n  data.placement = getOppositePlacement(placement);\n  data.offsets.popper = getClientRect(popper);\n\n  return data;\n}\n", "import applyStyle, { applyStyleOnLoad } from './applyStyle';\nimport computeStyle from './computeStyle';\nimport arrow from './arrow';\nimport flip from './flip';\nimport keepTogether from './keepTogether';\nimport offset from './offset';\nimport preventOverflow from './preventOverflow';\nimport shift from './shift';\nimport hide from './hide';\nimport inner from './inner';\n\n/**\n * Modifier function, each modifier can have a function of this type assigned\n * to its `fn` property.<br />\n * These functions will be called on each update, this means that you must\n * make sure they are performant enough to avoid performance bottlenecks.\n *\n * @function ModifierFn\n * @argument {dataObject} data - The data object generated by `update` method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {dataObject} The data object, properly modified\n */\n\n/**\n * Modifiers are plugins used to alter the behavior of your poppers.<br />\n * Popper.js uses a set of 9 modifiers to provide all the basic functionalities\n * needed by the library.\n *\n * Usually you don't want to override the `order`, `fn` and `onLoad` props.\n * All the other properties are configurations that could be tweaked.\n * @namespace modifiers\n */\nexport default {\n  /**\n   * Modifier used to shift the popper on the start or end of its reference\n   * element.<br />\n   * It will read the variation of the `placement` property.<br />\n   * It can be one either `-end` or `-start`.\n   * @memberof modifiers\n   * @inner\n   */\n  shift: {\n    /** @prop {number} order=100 - Index used to define the order of execution */\n    order: 100,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: shift,\n  },\n\n  /**\n   * The `offset` modifier can shift your popper on both its axis.\n   *\n   * It accepts the following units:\n   * - `px` or unit-less, interpreted as pixels\n   * - `%` or `%r`, percentage relative to the length of the reference element\n   * - `%p`, percentage relative to the length of the popper element\n   * - `vw`, CSS viewport width unit\n   * - `vh`, CSS viewport height unit\n   *\n   * For length is intended the main axis relative to the placement of the popper.<br />\n   * This means that if the placement is `top` or `bottom`, the length will be the\n   * `width`. In case of `left` or `right`, it will be the `height`.\n   *\n   * You can provide a single value (as `Number` or `String`), or a pair of values\n   * as `String` divided by a comma or one (or more) white spaces.<br />\n   * The latter is a deprecated method because it leads to confusion and will be\n   * removed in v2.<br />\n   * Additionally, it accepts additions and subtractions between different units.\n   * Note that multiplications and divisions aren't supported.\n   *\n   * Valid examples are:\n   * ```\n   * 10\n   * '10%'\n   * '10, 10'\n   * '10%, 10'\n   * '10 + 10%'\n   * '10 - 5vh + 3%'\n   * '-10px + 5vh, 5px - 6%'\n   * ```\n   * > **NB**: If you desire to apply offsets to your poppers in a way that may make them overlap\n   * > with their reference element, unfortunately, you will have to disable the `flip` modifier.\n   * > You can read more on this at this [issue](https://github.com/FezVrasta/popper.js/issues/373).\n   *\n   * @memberof modifiers\n   * @inner\n   */\n  offset: {\n    /** @prop {number} order=200 - Index used to define the order of execution */\n    order: 200,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: offset,\n    /** @prop {Number|String} offset=0\n     * The offset value as described in the modifier description\n     */\n    offset: 0,\n  },\n\n  /**\n   * Modifier used to prevent the popper from being positioned outside the boundary.\n   *\n   * A scenario exists where the reference itself is not within the boundaries.<br />\n   * We can say it has \"escaped the boundaries\" — or just \"escaped\".<br />\n   * In this case we need to decide whether the popper should either:\n   *\n   * - detach from the reference and remain \"trapped\" in the boundaries, or\n   * - if it should ignore the boundary and \"escape with its reference\"\n   *\n   * When `escapeWithReference` is set to`true` and reference is completely\n   * outside its boundaries, the popper will overflow (or completely leave)\n   * the boundaries in order to remain attached to the edge of the reference.\n   *\n   * @memberof modifiers\n   * @inner\n   */\n  preventOverflow: {\n    /** @prop {number} order=300 - Index used to define the order of execution */\n    order: 300,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: preventOverflow,\n    /**\n     * @prop {Array} [priority=['left','right','top','bottom']]\n     * Popper will try to prevent overflow following these priorities by default,\n     * then, it could overflow on the left and on top of the `boundariesElement`\n     */\n    priority: ['left', 'right', 'top', 'bottom'],\n    /**\n     * @prop {number} padding=5\n     * Amount of pixel used to define a minimum distance between the boundaries\n     * and the popper. This makes sure the popper always has a little padding\n     * between the edges of its container\n     */\n    padding: 5,\n    /**\n     * @prop {String|HTMLElement} boundariesElement='scrollParent'\n     * Boundaries used by the modifier. Can be `scrollParent`, `window`,\n     * `viewport` or any DOM element.\n     */\n    boundariesElement: 'scrollParent',\n  },\n\n  /**\n   * Modifier used to make sure the reference and its popper stay near each other\n   * without leaving any gap between the two. Especially useful when the arrow is\n   * enabled and you want to ensure that it points to its reference element.\n   * It cares only about the first axis. You can still have poppers with margin\n   * between the popper and its reference element.\n   * @memberof modifiers\n   * @inner\n   */\n  keepTogether: {\n    /** @prop {number} order=400 - Index used to define the order of execution */\n    order: 400,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: keepTogether,\n  },\n\n  /**\n   * This modifier is used to move the `arrowElement` of the popper to make\n   * sure it is positioned between the reference element and its popper element.\n   * It will read the outer size of the `arrowElement` node to detect how many\n   * pixels of conjunction are needed.\n   *\n   * It has no effect if no `arrowElement` is provided.\n   * @memberof modifiers\n   * @inner\n   */\n  arrow: {\n    /** @prop {number} order=500 - Index used to define the order of execution */\n    order: 500,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: arrow,\n    /** @prop {String|HTMLElement} element='[x-arrow]' - Selector or node used as arrow */\n    element: '[x-arrow]',\n  },\n\n  /**\n   * Modifier used to flip the popper's placement when it starts to overlap its\n   * reference element.\n   *\n   * Requires the `preventOverflow` modifier before it in order to work.\n   *\n   * **NOTE:** this modifier will interrupt the current update cycle and will\n   * restart it if it detects the need to flip the placement.\n   * @memberof modifiers\n   * @inner\n   */\n  flip: {\n    /** @prop {number} order=600 - Index used to define the order of execution */\n    order: 600,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: flip,\n    /**\n     * @prop {String|Array} behavior='flip'\n     * The behavior used to change the popper's placement. It can be one of\n     * `flip`, `clockwise`, `counterclockwise` or an array with a list of valid\n     * placements (with optional variations)\n     */\n    behavior: 'flip',\n    /**\n     * @prop {number} padding=5\n     * The popper will flip if it hits the edges of the `boundariesElement`\n     */\n    padding: 5,\n    /**\n     * @prop {String|HTMLElement} boundariesElement='viewport'\n     * The element which will define the boundaries of the popper position.\n     * The popper will never be placed outside of the defined boundaries\n     * (except if `keepTogether` is enabled)\n     */\n    boundariesElement: 'viewport',\n    /**\n     * @prop {Boolean} flipVariations=false\n     * The popper will switch placement variation between `-start` and `-end` when\n     * the reference element overlaps its boundaries.\n     *\n     * The original placement should have a set variation.\n     */\n    flipVariations: false,\n    /**\n     * @prop {Boolean} flipVariationsByContent=false\n     * The popper will switch placement variation between `-start` and `-end` when\n     * the popper element overlaps its reference boundaries.\n     *\n     * The original placement should have a set variation.\n     */\n    flipVariationsByContent: false,\n  },\n\n  /**\n   * Modifier used to make the popper flow toward the inner of the reference element.\n   * By default, when this modifier is disabled, the popper will be placed outside\n   * the reference element.\n   * @memberof modifiers\n   * @inner\n   */\n  inner: {\n    /** @prop {number} order=700 - Index used to define the order of execution */\n    order: 700,\n    /** @prop {Boolean} enabled=false - Whether the modifier is enabled or not */\n    enabled: false,\n    /** @prop {ModifierFn} */\n    fn: inner,\n  },\n\n  /**\n   * Modifier used to hide the popper when its reference element is outside of the\n   * popper boundaries. It will set a `x-out-of-boundaries` attribute which can\n   * be used to hide with a CSS selector the popper when its reference is\n   * out of boundaries.\n   *\n   * Requires the `preventOverflow` modifier before it in order to work.\n   * @memberof modifiers\n   * @inner\n   */\n  hide: {\n    /** @prop {number} order=800 - Index used to define the order of execution */\n    order: 800,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: hide,\n  },\n\n  /**\n   * Computes the style that will be applied to the popper element to gets\n   * properly positioned.\n   *\n   * Note that this modifier will not touch the DOM, it just prepares the styles\n   * so that `applyStyle` modifier can apply it. This separation is useful\n   * in case you need to replace `applyStyle` with a custom implementation.\n   *\n   * This modifier has `850` as `order` value to maintain backward compatibility\n   * with previous versions of Popper.js. Expect the modifiers ordering method\n   * to change in future major versions of the library.\n   *\n   * @memberof modifiers\n   * @inner\n   */\n  computeStyle: {\n    /** @prop {number} order=850 - Index used to define the order of execution */\n    order: 850,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: computeStyle,\n    /**\n     * @prop {Boolean} gpuAcceleration=true\n     * If true, it uses the CSS 3D transformation to position the popper.\n     * Otherwise, it will use the `top` and `left` properties\n     */\n    gpuAcceleration: true,\n    /**\n     * @prop {string} [x='bottom']\n     * Where to anchor the X axis (`bottom` or `top`). AKA X offset origin.\n     * Change this if your popper should grow in a direction different from `bottom`\n     */\n    x: 'bottom',\n    /**\n     * @prop {string} [x='left']\n     * Where to anchor the Y axis (`left` or `right`). AKA Y offset origin.\n     * Change this if your popper should grow in a direction different from `right`\n     */\n    y: 'right',\n  },\n\n  /**\n   * Applies the computed styles to the popper element.\n   *\n   * All the DOM manipulations are limited to this modifier. This is useful in case\n   * you want to integrate Popper.js inside a framework or view library and you\n   * want to delegate all the DOM manipulations to it.\n   *\n   * Note that if you disable this modifier, you must make sure the popper element\n   * has its position set to `absolute` before Popper.js can do its work!\n   *\n   * Just disable this modifier and define your own to achieve the desired effect.\n   *\n   * @memberof modifiers\n   * @inner\n   */\n  applyStyle: {\n    /** @prop {number} order=900 - Index used to define the order of execution */\n    order: 900,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: applyStyle,\n    /** @prop {Function} */\n    onLoad: applyStyleOnLoad,\n    /**\n     * @deprecated since version 1.10.0, the property moved to `computeStyle` modifier\n     * @prop {Boolean} gpuAcceleration=true\n     * If true, it uses the CSS 3D transformation to position the popper.\n     * Otherwise, it will use the `top` and `left` properties\n     */\n    gpuAcceleration: undefined,\n  },\n};\n\n/**\n * The `dataObject` is an object containing all the information used by Popper.js.\n * This object is passed to modifiers and to the `onCreate` and `onUpdate` callbacks.\n * @name dataObject\n * @property {Object} data.instance The Popper.js instance\n * @property {String} data.placement Placement applied to popper\n * @property {String} data.originalPlacement Placement originally defined on init\n * @property {Boolean} data.flipped True if popper has been flipped by flip modifier\n * @property {Boolean} data.hide True if the reference element is out of boundaries, useful to know when to hide the popper\n * @property {HTMLElement} data.arrowElement Node used as arrow by arrow modifier\n * @property {Object} data.styles Any CSS property defined here will be applied to the popper. It expects the JavaScript nomenclature (eg. `marginBottom`)\n * @property {Object} data.arrowStyles Any CSS property defined here will be applied to the popper arrow. It expects the JavaScript nomenclature (eg. `marginBottom`)\n * @property {Object} data.boundaries Offsets of the popper boundaries\n * @property {Object} data.offsets The measurements of popper, reference and arrow elements\n * @property {Object} data.offsets.popper `top`, `left`, `width`, `height` values\n * @property {Object} data.offsets.reference `top`, `left`, `width`, `height` values\n * @property {Object} data.offsets.arrow] `top` and `left` offsets, only one of them will be different from 0\n */\n", "import modifiers from '../modifiers/index';\n\n/**\n * Default options provided to Popper.js constructor.<br />\n * These can be overridden using the `options` argument of Popper.js.<br />\n * To override an option, simply pass an object with the same\n * structure of the `options` object, as the 3rd argument. For example:\n * ```\n * new Popper(ref, pop, {\n *   modifiers: {\n *     preventOverflow: { enabled: false }\n *   }\n * })\n * ```\n * @type {Object}\n * @static\n * @memberof Popper\n */\nexport default {\n  /**\n   * Popper's placement.\n   * @prop {Popper.placements} placement='bottom'\n   */\n  placement: 'bottom',\n\n  /**\n   * Set this to true if you want popper to position it self in 'fixed' mode\n   * @prop {Boolean} positionFixed=false\n   */\n  positionFixed: false,\n\n  /**\n   * Whether events (resize, scroll) are initially enabled.\n   * @prop {Boolean} eventsEnabled=true\n   */\n  eventsEnabled: true,\n\n  /**\n   * Set to true if you want to automatically remove the popper when\n   * you call the `destroy` method.\n   * @prop {Boolean} removeOnDestroy=false\n   */\n  removeOnDestroy: false,\n\n  /**\n   * Callback called when the popper is created.<br />\n   * By default, it is set to no-op.<br />\n   * Access Popper.js instance with `data.instance`.\n   * @prop {onCreate}\n   */\n  onCreate: () => {},\n\n  /**\n   * Callback called when the popper is updated. This callback is not called\n   * on the initialization/creation of the popper, but only on subsequent\n   * updates.<br />\n   * By default, it is set to no-op.<br />\n   * Access Popper.js instance with `data.instance`.\n   * @prop {onUpdate}\n   */\n  onUpdate: () => {},\n\n  /**\n   * List of modifiers used to modify the offsets before they are applied to the popper.\n   * They provide most of the functionalities of Popper.js.\n   * @prop {modifiers}\n   */\n  modifiers,\n};\n\n/**\n * @callback onCreate\n * @param {dataObject} data\n */\n\n/**\n * @callback onUpdate\n * @param {dataObject} data\n */\n", "// Utils\nimport debounce from './utils/debounce';\nimport isFunction from './utils/isFunction';\n\n// Methods\nimport update from './methods/update';\nimport destroy from './methods/destroy';\nimport enableEventListeners from './methods/enableEventListeners';\nimport disableEventListeners from './methods/disableEventListeners';\nimport Defaults from './methods/defaults';\nimport placements from './methods/placements';\n\nexport default class Popper {\n  /**\n   * Creates a new Popper.js instance.\n   * @class Popper\n   * @param {Element|referenceObject} reference - The reference element used to position the popper\n   * @param {Element} popper - The HTML / XML element used as the popper\n   * @param {Object} options - Your custom options to override the ones defined in [Defaults](#defaults)\n   * @return {Object} instance - The generated Popper.js instance\n   */\n  constructor(reference, popper, options = {}) {\n    // make update() debounced, so that it only runs at most once-per-tick\n    this.update = debounce(this.update.bind(this));\n\n    // with {} we create a new object with the options inside it\n    this.options = { ...Popper.Defaults, ...options };\n\n    // init state\n    this.state = {\n      isDestroyed: false,\n      isCreated: false,\n      scrollParents: [],\n    };\n\n    // get reference and popper elements (allow jQuery wrappers)\n    this.reference = reference && reference.jquery ? reference[0] : reference;\n    this.popper = popper && popper.jquery ? popper[0] : popper;\n\n    // Deep merge modifiers options\n    this.options.modifiers = {};\n    Object.keys({\n      ...Popper.Defaults.modifiers,\n      ...options.modifiers,\n    }).forEach(name => {\n      this.options.modifiers[name] = {\n        // If it's a built-in modifier, use it as base\n        ...(Popper.Defaults.modifiers[name] || {}),\n        // If there are custom options, override and merge with default ones\n        ...(options.modifiers ? options.modifiers[name] : {}),\n      };\n    });\n\n    // Refactoring modifiers' list (Object => Array)\n    this.modifiers = Object.keys(this.options.modifiers)\n      .map(name => ({\n        name,\n        ...this.options.modifiers[name],\n      }))\n      // sort the modifiers by order\n      .sort((a, b) => a.order - b.order);\n\n    // modifiers have the ability to execute arbitrary code when Popper.js get inited\n    // such code is executed in the same order of its modifier\n    // they could add new properties to their options configuration\n    // BE AWARE: don't add options to `options.modifiers.name` but to `modifierOptions`!\n    this.modifiers.forEach(modifierOptions => {\n      if (modifierOptions.enabled && isFunction(modifierOptions.onLoad)) {\n        modifierOptions.onLoad(\n          this.reference,\n          this.popper,\n          this.options,\n          modifierOptions,\n          this.state\n        );\n      }\n    });\n\n    // fire the first update to position the popper in the right place\n    this.update();\n\n    const eventsEnabled = this.options.eventsEnabled;\n    if (eventsEnabled) {\n      // setup event listeners, they will take care of update the position in specific situations\n      this.enableEventListeners();\n    }\n\n    this.state.eventsEnabled = eventsEnabled;\n  }\n\n  // We can't use class properties because they don't get listed in the\n  // class prototype and break stuff like Sinon stubs\n  update() {\n    return update.call(this);\n  }\n  destroy() {\n    return destroy.call(this);\n  }\n  enableEventListeners() {\n    return enableEventListeners.call(this);\n  }\n  disableEventListeners() {\n    return disableEventListeners.call(this);\n  }\n\n  /**\n   * Schedules an update. It will run on the next UI update available.\n   * @method scheduleUpdate\n   * @memberof Popper\n   */\n  scheduleUpdate = () => requestAnimationFrame(this.update);\n\n  /**\n   * Collection of utilities useful when writing custom modifiers.\n   * Starting from version 1.7, this method is available only if you\n   * include `popper-utils.js` before `popper.js`.\n   *\n   * **DEPRECATION**: This way to access PopperUtils is deprecated\n   * and will be removed in v2! Use the PopperUtils module directly instead.\n   * Due to the high instability of the methods contained in Utils, we can't\n   * guarantee them to follow semver. Use them at your own risk!\n   * @static\n   * @private\n   * @type {Object}\n   * @deprecated since version 1.8\n   * @member Utils\n   * @memberof Popper\n   */\n  static Utils = (typeof window !== 'undefined' ? window : global).PopperUtils;\n\n  static placements = placements;\n\n  static Defaults = Defaults;\n}\n\n/**\n * The `referenceObject` is an object that provides an interface compatible with Popper.js\n * and lets you use it as replacement of a real DOM node.<br />\n * You can use this method to position a popper relatively to a set of coordinates\n * in case you don't have a DOM node to use as reference.\n *\n * ```\n * new Popper(referenceObject, popperNode);\n * ```\n *\n * NB: This feature isn't supported in Internet Explorer 10.\n * @name referenceObject\n * @property {Function} data.getBoundingClientRect\n * A function that returns a set of coordinates compatible with the native `getBoundingClientRect` method.\n * @property {number} data.clientWidth\n * An ES6 getter that will return the width of the virtual reference element.\n * @property {number} data.clientHeight\n * An ES6 getter that will return the height of the virtual reference element.\n */\n"], "names": ["window", "document", "navigator", "longerTimeoutBrowsers", "i", "length", "<PERSON><PERSON><PERSON><PERSON>", "userAgent", "indexOf", "called", "Promise", "resolve", "then", "scheduled", "supportsMicroTasks", "functionToCheck", "getType", "toString", "call", "element", "nodeType", "ownerDocument", "defaultView", "css", "getComputedStyle", "property", "nodeName", "parentNode", "host", "body", "overflow", "overflowX", "overflowY", "getStyleComputedProperty", "test", "getScrollParent", "getParentNode", "reference", "referenceNode", "isIE11", "MSInputMethodContext", "documentMode", "isIE10", "version", "documentElement", "noOffsetParent", "isIE", "offsetParent", "nextElement<PERSON><PERSON>ling", "getOffsetParent", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "node", "getRoot", "element1", "element2", "order", "compareDocumentPosition", "Node", "DOCUMENT_POSITION_FOLLOWING", "start", "end", "range", "createRange", "setStart", "setEnd", "commonAncestorContainer", "contains", "isOffsetContainer", "element1root", "findCommonOffsetParent", "side", "upperSide", "html", "scrollingElement", "subtract", "scrollTop", "getScroll", "scrollLeft", "modifier", "top", "bottom", "left", "right", "sideA", "axis", "sideB", "parseFloat", "styles", "Math", "max", "parseInt", "computedStyle", "getSize", "offsets", "width", "height", "rect", "getBoundingClientRect", "result", "sizes", "getWindowSizes", "clientWidth", "clientHeight", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "offsetWidth", "vertScrollbar", "offsetHeight", "getBordersSize", "getClientRect", "fixedPosition", "runIsIE", "isHTML", "parent", "childrenRect", "parentRect", "scrollParent", "borderTopWidth", "borderLeftWidth", "marginTop", "marginLeft", "includeScroll", "excludeScroll", "relativeOffset", "getOffsetRectRelativeToArbitraryNode", "innerWidth", "innerHeight", "offset", "isFixed", "parentElement", "el", "boundaries", "getFixedPositionOffsetParent", "getReferenceNode", "boundariesElement", "getViewportOffsetRectRelativeToArtbitraryNode", "boundariesNode", "popper", "padding", "isPaddingNumber", "placement", "getBoundaries", "rects", "refRect", "sorted<PERSON>reas", "Object", "keys", "map", "key", "getArea", "sort", "b", "area", "a", "filtered<PERSON><PERSON>s", "filter", "computedPlacement", "variation", "split", "commonOffsetParent", "x", "marginBottom", "y", "marginRight", "hash", "replace", "matched", "popperRect", "getOuterSizes", "popperOffsets", "isHoriz", "mainSide", "secondarySide", "measurement", "secondaryMeasurement", "referenceOffsets", "getOppositePlacement", "Array", "prototype", "find", "arr", "findIndex", "cur", "match", "obj", "modifiersToRun", "ends", "modifiers", "slice", "for<PERSON>ach", "warn", "fn", "enabled", "isFunction", "data", "state", "isDestroyed", "getReferenceOffsets", "options", "positionFixed", "computeAutoPlacement", "flip", "originalPlacement", "getPopperOffsets", "position", "runModifiers", "isCreated", "onUpdate", "onCreate", "some", "name", "prefixes", "upperProp", "char<PERSON>t", "toUpperCase", "prefix", "to<PERSON><PERSON><PERSON>", "style", "isModifierEnabled", "removeAttribute", "<PERSON><PERSON><PERSON><PERSON>", "getSupportedPropertyName", "disableEventListeners", "removeOnDestroy", "<PERSON><PERSON><PERSON><PERSON>", "isBody", "target", "addEventListener", "passive", "push", "updateBound", "scrollElement", "scrollParents", "eventsEnabled", "setupEventListeners", "scheduleUpdate", "removeEventListener", "removeEventListeners", "n", "isNaN", "isFinite", "prop", "unit", "isNumeric", "value", "attributes", "setAttribute", "instance", "arrowElement", "arrowStyles", "round", "floor", "noRound", "v", "referenceWidth", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isVertical", "isVariation", "horizontalToInteger", "verticalToInteger", "bothOddWidth", "isFirefox", "legacyGpuAccelerationOption", "gpuAcceleration", "offsetParentRect", "getRoundedOffsets", "devicePixelRatio", "prefixedProperty", "invertTop", "invertLeft", "arrow", "requesting", "isRequired", "requested", "isModifierRequired", "querySelector", "len", "sideCapitalized", "toLowerCase", "altSide", "opSide", "arrowElementSize", "center", "popperMarginSide", "popperBorderSide", "sideValue", "min", "validPlacements", "placements", "counter", "index", "concat", "reverse", "BEHAVIORS", "flipped", "placementOpposite", "flipOrder", "behavior", "FLIP", "CLOCKWISE", "clockwise", "COUNTERCLOCKWISE", "refOffsets", "overlapsRef", "overflowsLeft", "overflowsRight", "overflowsTop", "overflowsBottom", "overflowsBoundaries", "flippedVariationByRef", "flipVariations", "flippedVariationByContent", "flipVariationsByContent", "flippedVariation", "getOppositeVariation", "str", "size", "useHeight", "fragments", "frag", "trim", "divider", "search", "splitRegex", "ops", "mergeWithPrevious", "op", "reduce", "toValue", "index2", "basePlacement", "parseOffset", "transformProp", "popperStyles", "priority", "check", "escapeWithReference", "shiftvariation", "shiftOffsets", "bound", "hide", "subtractLength", "requestAnimationFrame", "update", "debounce", "bind", "<PERSON><PERSON>", "De<PERSON>ults", "j<PERSON>y", "modifierOptions", "onLoad", "enableEventListeners", "destroy", "Utils", "global", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;GAAA,MAAiC,WAAlB,QAAOA,OAAP,EAAqD,WAApB,QAAOC,SAAxC,EAAyF,WAArB,QAAOC,UAA1F,yBCGQC,oCACD,GAAIC,GAAI,EAAGA,EAAID,EAAsBE,OAAQD,GAAK,KACjDE,GAAsE,CAAzDJ,YAAUK,SAAVL,CAAoBM,OAApBN,CAA4BC,IAA5BD,QACR,SAGJ,EAPgB,IAUzB,aAAsC,IAChCO,YACG,IAAM,SAAA,QAKJC,QAAQC,UAAUC,KAAK,IAAM,KAAA,IAApC,EALW,CAAb,EAYF,aAAiC,IAC3BC,YACG,IAAM,SAAA,YAGE,IAAM,KAAA,IAAjB,IAHS,CAAb,EAWF,KAAMC,GAAqBR,GAAaN,OAAOU,OAA/C,CAYA,MAAgBI,KAAhB,CC5CA,aAAoD,OAGhDC,IAC2C,mBAA3CC,MAAQC,QAARD,CAAiBE,IAAjBF,ICJJ,eAAoE,IACzC,CAArBG,KAAQC,uBAINpB,GAASmB,EAAQE,aAARF,CAAsBG,YAC/BC,EAAMvB,EAAOwB,gBAAPxB,GAAiC,IAAjCA,QACLyB,GAAWF,IAAXE,GCPT,aAA+C,OACpB,MAArBN,KAAQO,QADiC,GAItCP,EAAQQ,UAARR,EAAsBA,EAAQS,KCDvC,aAAiD,IAE3C,SACK3B,UAAS4B,YAGVV,EAAQO,cACT,WACA,aACIP,GAAQE,aAARF,CAAsBU,SAC1B,kBACIV,GAAQU,WAIb,CAAEC,UAAF,CAAYC,WAAZ,CAAuBC,WAAvB,EAAqCC,KAfI,MAgB3C,yBAAwBC,IAAxB,CAA6BJ,KAA7B,CAhB2C,GAoBxCK,EAAgBC,IAAhBD,ECvBT,aAAoD,OAC3CE,IAAaA,EAAUC,aAAvBD,CAAuCA,EAAUC,aAAjDD,QCNHE,GAASjC,GAAa,CAAC,EAAEN,OAAOwC,oBAAPxC,EAA+BC,SAASwC,YAA1C,EACvBC,EAASpC,GAAa,UAAU4B,IAAV,CAAehC,UAAUK,SAAzB,EAS5B,aAAsC,OACpB,GAAZoC,IADgC,GAIpB,EAAZA,IAJgC,GAO7BJ,KCVT,aAAiD,IAC3C,SACKtC,UAAS2C,qBAGZC,GAAiBC,EAAK,EAALA,EAAW7C,SAAS4B,IAApBiB,CAA2B,QAG9CC,GAAe5B,EAAQ4B,YAAR5B,EAAwB,KARI,KAUxC4B,OAAmC5B,EAAQ6B,kBAVH,IAW9B,CAAC7B,EAAUA,EAAQ6B,kBAAnB,EAAuCD,kBAGlDrB,GAAWqB,GAAgBA,EAAarB,SAdC,MAgB3C,IAA0B,MAAbA,IAAb,EAAiD,MAAbA,IAhBO,CAuBY,CAAC,CAA1D,uBAAsBlB,OAAtB,CAA8BuC,EAAarB,QAA3C,GACuD,QAAvDO,OAAuC,UAAvCA,CAxB6C,CA0BtCgB,IA1BsC,GAiBtC9B,EAAUA,EAAQE,aAARF,CAAsByB,eAAhCzB,CAAkDlB,SAAS2C,6BCxBnB,MAC3C,CAAElB,UAAF,IAD2C,MAEhC,MAAbA,IAF6C,GAMlC,MAAbA,MAAuBuB,EAAgB9B,EAAQ+B,iBAAxBD,KANwB,ECKnD,aAAsC,OACZ,KAApBE,KAAKxB,UAD2B,GAE3ByB,EAAQD,EAAKxB,UAAbyB,ECGX,eAAmE,IAE7D,IAAa,CAACC,EAASjC,QAAvB,EAAmC,EAAnC,EAAgD,CAACkC,EAASlC,eACrDnB,UAAS2C,qBAIZW,GACJF,EAASG,uBAATH,IACAI,KAAKC,4BACDC,EAAQJ,MACRK,EAAML,MAGNM,EAAQ5D,SAAS6D,WAAT7D,KACR8D,WAAgB,EAf2C,GAgB3DC,SAAY,EAhB+C,MAiB3D,CAAEC,yBAAF,OAIHZ,OACCC,KADDD,EAEDM,EAAMO,QAANP,UAEIQ,QAIGlB,UAIHmB,GAAehB,KAjC4C,MAkC7DgB,GAAaxC,IAlCgD,CAmCxDyC,EAAuBD,EAAaxC,IAApCyC,GAnCwD,CAqCxDA,IAAiCjB,KAAkBxB,IAAnDyC,ECzCX,aAA2CC,EAAO,KAAlD,CAAyD,MACjDC,GAAqB,KAATD,KAAiB,WAAjBA,CAA+B,aAC3C5C,EAAWP,EAAQO,YAER,MAAbA,MAAoC,MAAbA,KAAqB,MACxC8C,GAAOrD,EAAQE,aAARF,CAAsByB,gBAC7B6B,EAAmBtD,EAAQE,aAARF,CAAsBsD,gBAAtBtD,UAClBsD,YAGFtD,MCPT,eAAqDuD,IAArD,CAAuE,MAC/DC,GAAYC,IAAmB,KAAnBA,EACZC,EAAaD,IAAmB,MAAnBA,EACbE,EAAWJ,EAAW,CAAC,CAAZA,CAAgB,WAC5BK,KAAOJ,MACPK,QAAUL,MACVM,MAAQJ,MACRK,OAASL,MCRhB,eAAqD,MAC7CM,GAAiB,GAATC,KAAe,MAAfA,CAAwB,MAChCC,EAAkB,MAAVF,IAAmB,OAAnBA,CAA6B,eAGzCG,YAAWC,WAAQ,QAARA,CAAXD,EACAA,WAAWC,WAAQ,QAARA,CAAXD,qBCd8C,OACzCE,MAAKC,GAALD,CACL3D,WAAM,GAANA,CADK2D,CAEL3D,WAAM,GAANA,CAFK2D,CAGLhB,WAAM,GAANA,CAHKgB,CAILhB,WAAM,GAANA,CAJKgB,CAKLhB,WAAM,GAANA,CALKgB,CAML1C,EAAK,EAALA,EACK4C,SAASlB,WAAM,GAANA,CAATkB,EACHA,SAASC,WAAgC,QAATP,KAAoB,KAApBA,CAA4B,QAAnDO,CAATD,CADGA,CAEHA,SAASC,WAAgC,QAATP,KAAoB,QAApBA,CAA+B,SAAtDO,CAATD,CAHF5C,CAIE,CAVG0C,EAcT,aAAiD,MACzC3D,GAAO5B,EAAS4B,KAChB2C,EAAOvE,EAAS2C,gBAChB+C,EAAgB7C,EAAK,EAALA,GAAYtB,0BAE3B,QACGoE,EAAQ,QAARA,OADH,OAEEA,EAAQ,OAARA,OAFF,uKCfT,aAA+C,sBAGpCC,EAAQZ,IAARY,CAAeA,EAAQC,aACtBD,EAAQd,GAARc,CAAcA,EAAQE,SCGlC,aAAuD,IACjDC,SAKA,IACElD,EAAK,EAALA,EAAU,GACL3B,EAAQ8E,qBAAR9E,EADK,MAENwD,GAAYC,IAAmB,KAAnBA,EACZC,EAAaD,IAAmB,MAAnBA,IACdG,MAJO,GAKPE,OALO,GAMPD,SANO,GAOPE,QAPP,QAUS/D,EAAQ8E,qBAAR9E,EAXX,CAcA,QAAQ,OAEF+E,GAAS,MACPF,EAAKf,IADE,KAERe,EAAKjB,GAFG,OAGNiB,EAAKd,KAALc,CAAaA,EAAKf,IAHZ,QAILe,EAAKhB,MAALgB,CAAcA,EAAKjB,GAJd,EAQToB,EAA6B,MAArBhF,KAAQO,QAARP,CAA8BiF,EAAejF,EAAQE,aAAvB+E,CAA9BjF,IACR2E,EACJK,EAAML,KAANK,EAAehF,EAAQkF,WAAvBF,EAAsCD,EAAOJ,MACzCC,EACJI,EAAMJ,MAANI,EAAgBhF,EAAQmF,YAAxBH,EAAwCD,EAAOH,UAE7CQ,GAAiBpF,EAAQqF,WAARrF,GACjBsF,EAAgBtF,EAAQuF,YAARvF,MAIhBoF,KAAiC,MAC7BhB,GAAStD,QACG0E,IAAuB,GAAvBA,CAFiB,IAGlBA,IAAuB,GAAvBA,CAHkB,GAK5Bb,QAL4B,GAM5BC,gBAGFa,qBCzDsEC,KAAuB,OAajFrB,KAAKC,GAb4E,MAC9F/C,GAASoE,EAAQ,EAARA,EACTC,EAA6B,MAApBC,KAAOtF,SAChBuF,EAAehB,KACfiB,EAAajB,KACbkB,EAAehF,KAEfoD,EAAStD,KACTmF,EAAiB9B,WAAWC,EAAO6B,cAAlB9B,EACjB+B,EAAkB/B,WAAWC,EAAO8B,eAAlB/B,EAGrBuB,IAZiG,KAavF9B,IAAMS,EAAS0B,EAAWnC,GAApBS,CAAyB,CAAzBA,CAbiF,GAcvFP,KAAOO,EAAS0B,EAAWjC,IAApBO,CAA0B,CAA1BA,CAdgF,KAgBhGK,GAAUe,EAAc,KACrBK,EAAalC,GAAbkC,CAAmBC,EAAWnC,GAA9BkC,EADqB,MAEpBA,EAAahC,IAAbgC,CAAoBC,EAAWjC,IAA/BgC,EAFoB,OAGnBA,EAAanB,KAHM,QAIlBmB,EAAalB,MAJK,CAAda,OAMNU,UAAY,IACZC,WAAa,EAMjB,MAAmB,MACfD,GAAYhC,WAAWC,EAAO+B,SAAlBhC,EACZiC,EAAajC,WAAWC,EAAOgC,UAAlBjC,IAEXP,KAAOqC,GAJM,GAKbpC,QAAUoC,GALG,GAMbnC,MAAQoC,GANK,GAObnC,OAASmC,GAPI,GAUbC,WAVa,GAWbC,oBAIR7E,GAAU,EAAVA,CACIsE,EAAO9C,QAAP8C,GADJtE,CAEIsE,OAAqD,MAA1BG,KAAazF,cAElC8F,uBCnDiEC,KAAuB,OAGtFjC,KAAKC,GAHiF,MAC9FjB,GAAOrD,EAAQE,aAARF,CAAsByB,gBAC7B8E,EAAiBC,OACjB7B,EAAQN,EAAShB,EAAK6B,WAAdb,CAA2BxF,OAAO4H,UAAP5H,EAAqB,CAAhDwF,EACRO,EAASP,EAAShB,EAAK8B,YAAdd,CAA4BxF,OAAO6H,WAAP7H,EAAsB,CAAlDwF,EAETb,EAAY,EAAmC,CAAnC,CAAiBC,KAC7BC,EAAa,EAA2C,CAA3C,CAAiBD,IAAgB,MAAhBA,EAE9BkD,EAAS,KACRnD,EAAY+C,EAAe3C,GAA3BJ,CAAiC+C,EAAeJ,SADxC,MAEPzC,EAAa6C,EAAezC,IAA5BJ,CAAmC6C,EAAeH,UAF3C,QAAA,SAAA,QAORX,MCTT,aAAyC,MACjClF,GAAWP,EAAQO,YACR,MAAbA,MAAoC,MAAbA,iBAG2B,OAAlDO,OAAkC,UAAlCA,gBAGEN,GAAaS,KARoB,WAYhC2F,KCbT,aAA8D,IAEvD,IAAY,CAAC5G,EAAQ6G,aAArB,EAAsClF,UAClC7C,UAAS2C,mBAEdqF,GAAK9G,EAAQ6G,cAL2C,KAMrDC,GAAoD,MAA9ChG,OAA6B,WAA7BA,CAN+C,IAOrDgG,EAAGD,oBAEHC,IAAMhI,SAAS2C,gBCExB,mBAKEiE,IALF,CAME,IAGIqB,GAAa,CAAEnD,IAAK,CAAP,CAAUE,KAAM,CAAhB,OACXlC,GAAe8D,EAAgBsB,IAAhBtB,CAAuDxC,IAA+B+D,IAA/B/D,KAGlD,UAAtBgE,OACWC,WAGV,IAECC,GACsB,cAAtBF,IAHD,IAIgBlG,EAAgBC,IAAhBD,CAJhB,CAK+B,MAA5BoG,KAAe7G,QALlB,KAMkB8G,EAAOnH,aAAPmH,CAAqB5F,eANvC,GAQ8B,QAAtByF,IARR,GASgBG,EAAOnH,aAAPmH,CAAqB5F,eATrC,IAAA,MAcGiD,GAAU8B,YAOgB,MAA5BY,KAAe7G,QAAf6G,EAAsC,CAACR,KAAuB,MAC1D,CAAEhC,QAAF,CAAUD,OAAV,EAAoBM,EAAeoC,EAAOnH,aAAtB+E,IACfrB,KAAOc,EAAQd,GAARc,CAAcA,EAAQyB,SAFwB,GAGrDtC,OAASe,EAASF,EAAQd,GAH2B,GAIrDE,MAAQY,EAAQZ,IAARY,CAAeA,EAAQ0B,UAJsB,GAKrDrC,MAAQY,EAAQD,EAAQZ,IALrC,YAaQwD,GAAW,CA7CrB,MA8CMC,GAAqC,QAAnB,oBACbzD,MAAQyD,IAA4BD,EAAQxD,IAARwD,EAAgB,IACpD1D,KAAO2D,IAA4BD,EAAQ1D,GAAR0D,EAAe,IAClDvD,OAASwD,IAA4BD,EAAQvD,KAARuD,EAAiB,IACtDzD,QAAU0D,IAA4BD,EAAQzD,MAARyD,EAAkB,eC3EpD,CAAE3C,OAAF,CAASC,QAAT,EAAmB,OAC3BD,KAYT,qBAME2C,EAAU,CANZ,CAOE,IACkC,CAAC,CAA/BE,KAAUnI,OAAVmI,CAAkB,MAAlBA,gBAIET,GAAaU,WAObC,EAAQ,KACP,OACIX,EAAWpC,KADf,QAEKgD,EAAQ/D,GAAR+D,CAAcZ,EAAWnD,GAF9B,CADO,OAKL,OACEmD,EAAWhD,KAAXgD,CAAmBY,EAAQ5D,KAD7B,QAEGgD,EAAWnC,MAFd,CALK,QASJ,OACCmC,EAAWpC,KADZ,QAEEoC,EAAWlD,MAAXkD,CAAoBY,EAAQ9D,MAF9B,CATI,MAaN,OACG8D,EAAQ7D,IAAR6D,CAAeZ,EAAWjD,IAD7B,QAEIiD,EAAWnC,MAFf,CAbM,EAmBRgD,EAAcC,OAAOC,IAAPD,IACjBE,GADiBF,CACbG,eAEAN,WACGO,EAAQP,IAARO,GAJUJ,EAMjBK,IANiBL,CAMZ,OAAUM,EAAEC,IAAFD,CAASE,EAAED,IANTP,EAQdS,EAAgBV,EAAYW,MAAZX,CACpB,CAAC,CAAEjD,OAAF,CAASC,QAAT,CAAD,GACED,GAAS0C,EAAOnC,WAAhBP,EAA+BC,GAAUyC,EAAOlC,YAF9ByC,EAKhBY,EAA2C,CAAvBF,GAAcpJ,MAAdoJ,CACtBA,EAAc,CAAdA,EAAiBN,GADKM,CAEtBV,EAAY,CAAZA,EAAeI,IAEbS,EAAYjB,EAAUkB,KAAVlB,CAAgB,GAAhBA,EAAqB,CAArBA,QAEXgB,IAAqBC,MAAa,GAAbA,CAA8B,EAAnDD,ECzDT,iBAAsE9C,EAAgB,IAAtF,CAA4F,MACpFiD,GAAqBjD,EAAgBsB,IAAhBtB,CAAuDxC,IAA+B+D,IAA/B/D,QAC3EsD,UCVT,aAA+C,MACvC3H,GAASmB,EAAQE,aAARF,CAAsBG,YAC/BiE,EAASvF,EAAOwB,gBAAPxB,IACT+J,EAAIzE,WAAWC,EAAO+B,SAAP/B,EAAoB,CAA/BD,EAAoCA,WAAWC,EAAOyE,YAAPzE,EAAuB,CAAlCD,EACxC2E,EAAI3E,WAAWC,EAAOgC,UAAPhC,EAAqB,CAAhCD,EAAqCA,WAAWC,EAAO2E,WAAP3E,EAAsB,CAAjCD,EACzCY,EAAS,OACN/E,EAAQqF,WAARrF,EADM,QAELA,EAAQuF,YAARvF,EAFK,WCLjB,aAAwD,MAChDgJ,GAAO,CAAElF,KAAM,OAAR,CAAiBC,MAAO,MAAxB,CAAgCF,OAAQ,KAAxC,CAA+CD,IAAK,QAApD,QACN4D,GAAUyB,OAAVzB,CAAkB,wBAAlBA,CAA4C0B,KAAWF,IAAvDxB,ECIT,iBAA8E,GAChEA,EAAUkB,KAAVlB,CAAgB,GAAhBA,EAAqB,CAArBA,CADgE,MAItE2B,GAAaC,KAGbC,EAAgB,OACbF,EAAWxE,KADE,QAEZwE,EAAWvE,MAFC,EAMhB0E,EAAmD,CAAC,CAA1C,oBAAkBjK,OAAlB,IACVkK,EAAWD,EAAU,KAAVA,CAAkB,OAC7BE,EAAgBF,EAAU,MAAVA,CAAmB,MACnCG,EAAcH,EAAU,QAAVA,CAAqB,QACnCI,EAAuB,EAAsB,OAAtB,CAAW,qBAGtCC,KACAA,KAAgC,CADhCA,CAEAR,KAA0B,OACxB3B,MAEAmC,KAAkCR,KAGlCQ,EAAiBC,IAAjBD,IChCN,eAAyC,OAEnCE,OAAMC,SAAND,CAAgBE,IAFmB,CAG9BC,EAAID,IAAJC,GAH8B,CAOhCA,EAAIzB,MAAJyB,IAAkB,CAAlBA,ECLT,iBAAoD,IAE9CH,MAAMC,SAAND,CAAgBI,gBACXD,GAAIC,SAAJD,CAAcE,KAAOA,QAArBF,OAIHG,GAAQJ,IAAUK,KAAOA,QAAjBL,QACPC,GAAI3K,OAAJ2K,ICLT,iBAA4D,MACpDK,GAAiBC,aAEnBC,EAAUC,KAAVD,CAAgB,CAAhBA,CAAmBN,IAAqB,MAArBA,GAAnBM,WAEWE,QAAQ9G,KAAY,CAC7BA,EAAS,UAATA,CAD6B,UAEvB+G,KAAK,wDAFkB,MAI3BC,GAAKhH,EAAS,UAATA,GAAwBA,EAASgH,GACxChH,EAASiH,OAATjH,EAAoBkH,IALS,KAS1BnG,QAAQ2C,OAAS5B,EAAcqF,EAAKpG,OAALoG,CAAazD,MAA3B5B,CATS,GAU1Bf,QAAQxD,UAAYuE,EAAcqF,EAAKpG,OAALoG,CAAa5J,SAA3BuE,CAVM,GAYxBkF,MAZwB,CAAnC,KCPF,YAAiC,IAE3B,KAAKI,KAAL,CAAWC,sBAIXF,GAAO,UACC,IADD,UAAA,eAAA,cAAA,WAAA,WAAA,IAUNpG,QAAQxD,UAAY+J,EACvB,KAAKF,KADkBE,CAEvB,KAAK5D,MAFkB4D,CAGvB,KAAK/J,SAHkB+J,CAIvB,KAAKC,OAAL,CAAaC,aAJUF,CAhBM,GA0B1BzD,UAAY4D,EACf,KAAKF,OAAL,CAAa1D,SADE4D,CAEfN,EAAKpG,OAALoG,CAAa5J,SAFEkK,CAGf,KAAK/D,MAHU+D,CAIf,KAAKlK,SAJUkK,CAKf,KAAKF,OAAL,CAAaX,SAAb,CAAuBc,IAAvB,CAA4BnE,iBALbkE,CAMf,KAAKF,OAAL,CAAaX,SAAb,CAAuBc,IAAvB,CAA4B/D,OANb8D,CA1Bc,GAoC1BE,kBAAoBR,EAAKtD,SApCC,GAsC1B2D,cAAgB,KAAKD,OAAL,CAAaC,aAtCH,GAyC1BzG,QAAQ2C,OAASkE,EACpB,KAAKlE,MADekE,CAEpBT,EAAKpG,OAALoG,CAAa5J,SAFOqK,CAGpBT,EAAKtD,SAHe+D,CAzCS,GA+C1B7G,QAAQ2C,OAAOmE,SAAW,KAAKN,OAAL,CAAaC,aAAb,CAC3B,OAD2B,CAE3B,UAjD2B,GAoDxBM,EAAa,KAAKlB,SAAlBkB,GApDwB,CAwD1B,KAAKV,KAAL,CAAWW,SAxDe,MA4DxBR,QAAQS,WA5DgB,OAyDxBZ,MAAMW,YAzDkB,MA0DxBR,QAAQU,WA1DgB,ECNjC,eAAmE,OAC1DrB,GAAUsB,IAAVtB,CACL,CAAC,CAAEuB,MAAF,CAAQlB,SAAR,CAAD,GAAuBA,GAAWkB,KAD7BvB,ECAT,aAA2D,MACnDwB,gCACAC,EAAY1L,EAAS2L,MAAT3L,CAAgB,CAAhBA,EAAmB4L,WAAnB5L,GAAmCA,EAASkK,KAATlK,CAAe,CAAfA,MAEhD,GAAIrB,GAAI,EAAGA,EAAI8M,EAAS7M,OAAQD,IAAK,MAClCkN,GAASJ,KACTK,EAAUD,KAAU,IAAA,GAAVA,MAC4B,WAAxC,QAAOrN,UAAS4B,IAAT5B,CAAcuN,KAAdvN,mBAIN,MCVT,YAAkC,aAC3BiM,MAAMC,eAGPsB,EAAkB,KAAK/B,SAAvB+B,CAAkC,YAAlCA,SACGjF,OAAOkF,gBAAgB,oBACvBlF,OAAOgF,MAAMb,SAAW,QACxBnE,OAAOgF,MAAMzI,IAAM,QACnByD,OAAOgF,MAAMvI,KAAO,QACpBuD,OAAOgF,MAAMtI,MAAQ,QACrBsD,OAAOgF,MAAMxI,OAAS,QACtBwD,OAAOgF,MAAMG,WAAa,QAC1BnF,OAAOgF,MAAMI,EAAyB,WAAzBA,GAAyC,SAGxDC,wBAID,KAAKxB,OAAL,CAAayB,sBACVtF,OAAO7G,WAAWoM,YAAY,KAAKvF,QAEnC,KCzBT,aAA2C,MACnCnH,GAAgBF,EAAQE,oBACvBA,GAAgBA,EAAcC,WAA9BD,CAA4CrB,0BCJwB,MACrEgO,GAAmC,MAA1B7G,KAAazF,SACtBuM,EAASD,EAAS7G,EAAa9F,aAAb8F,CAA2B7F,WAApC0M,KACRE,qBAAkC,CAAEC,UAAF,EAHkC,MAOvEhM,EAAgB8L,EAAOtM,UAAvBQ,QAPuE,GAa7DiM,QAShB,mBAKE,GAEMC,aAFN,MAGqBH,iBAAiB,SAAUhC,EAAMmC,YAAa,CAAEF,UAAF,EAHnE,MAMMG,GAAgBnM,gBAGpB,SACA+J,EAAMmC,YACNnC,EAAMqC,iBAEFD,kBACAE,mBCpCR,YAA+C,CACxC,KAAKtC,KAAL,CAAWsC,aAD6B,QAEtCtC,MAAQuC,EACX,KAAKpM,SADMoM,CAEX,KAAKpC,OAFMoC,CAGX,KAAKvC,KAHMuC,CAIX,KAAKC,cAJMD,CAF8B,ECA/C,eAA+D,aAExCE,oBAAoB,SAAUzC,EAAMmC,eAGnDE,cAAc3C,QAAQqC,KAAU,GAC7BU,oBAAoB,SAAUzC,EAAMmC,YAD7C,KAKMA,YAAc,OACdE,mBACAD,cAAgB,OAChBE,mBCZR,YAAgD,CAC1C,KAAKtC,KAAL,CAAWsC,aAD+B,wBAEvB,KAAKE,eAFkB,MAGvCxC,MAAQ0C,EAAqB,KAAKvM,SAA1BuM,CAAqC,KAAK1C,KAA1C0C,CAH+B,ECFhD,aAAqC,OACtB,EAANC,MAAY,CAACC,MAAMxJ,aAANwJ,CAAbD,EAAqCE,YCE9C,eAAmD,QAC1C9F,QAAa2C,QAAQoD,KAAQ,IAC9BC,GAAO,GAIP,CAAC,CADH,oDAAsDzO,OAAtD,KAEA0O,EAAU3J,IAAV2J,CANgC,KAQzB,IARyB,IAU1B1B,SAAcjI,MAVxB,GCHF,eAA2D,QAClD0D,QAAiB2C,QAAQ,WAAe,MACvCuD,GAAQC,KACVD,MAFyC,GAKnCzB,kBALmC,GAGnC2B,eAAmBD,KAH/B,GCKF,aAAyC,UAK7BnD,EAAKqD,QAALrD,CAAczD,OAAQyD,EAAK1G,UAIvB0G,EAAKqD,QAALrD,CAAczD,OAAQyD,EAAKmD,YAGrCnD,EAAKsD,YAALtD,EAAqBjD,OAAOC,IAAPD,CAAYiD,EAAKuD,WAAjBxG,EAA8B3I,UAC3C4L,EAAKsD,aAActD,EAAKuD,eAgBtC,sBAME,MAEM1E,GAAmBsB,QAA8CC,EAAQC,aAAtDF,EAKnBzD,EAAY4D,EAChBF,EAAQ1D,SADQ4D,OAKhBF,EAAQX,SAARW,CAAkBG,IAAlBH,CAAuBhE,iBALPkE,CAMhBF,EAAQX,SAARW,CAAkBG,IAAlBH,CAAuB5D,OANP8D,WASX8C,aAAa,qBAIF,CAAE1C,SAAUN,EAAQC,aAARD,CAAwB,OAAxBA,CAAkC,UAA9C,KClDpB,gBAA6D,MACrD,CAAE7D,QAAF,CAAUnG,WAAV,EAAwB4J,EAAKpG,QAC7B,CAAE4J,OAAF,CAASC,OAAT,EAAmBlK,KACnBmK,EAAUC,OAEVC,EAAiBJ,EAAMpN,EAAUyD,KAAhB2J,EACjBK,EAAcL,EAAMjH,EAAO1C,KAAb2J,EAEdM,EAA2D,CAAC,CAA/C,oBAAkBvP,OAAlB,CAA0ByL,EAAKtD,SAA/B,EACbqH,EAA8C,CAAC,CAAjC/D,KAAKtD,SAALsD,CAAezL,OAAfyL,CAAuB,GAAvBA,EAIdgE,EAAsB,EAExBF,MALoBF,EAAiB,CAAjBA,EAAuBC,EAAc,CAKzDC,IAFwB,GAKtBG,EAAoB,YAEnB,MACCD,EAVoC,CAAvBJ,IAAiB,CAAjBA,EAAgD,CAApBC,IAAc,CAW3DK,EAAgB,EAAhBA,IACI3H,EAAOvD,IAAPuD,CAAc,CADlB2H,CAEI3H,EAAOvD,IAHPgL,CADD,KAMAC,EAAkB1H,EAAOzD,GAAzBmL,CANA,QAOGA,EAAkB1H,EAAOxD,MAAzBkL,CAPH,OAQED,EAAoBzH,EAAOtD,KAA3B+K,CARF,OChCHG,IAAY9P,GAAa,WAAW4B,IAAX,CAAgBhC,UAAUK,SAA1B,EAS/B,gBAAoD,MAC5C,CAAEwJ,GAAF,CAAKE,GAAL,IACA,CAAEzB,QAAF,EAAayD,EAAKpG,QAGlBwK,EAA8BnF,EAClCe,EAAKqD,QAALrD,CAAcP,SADoBR,CAElCpG,KAA8B,YAAlBA,KAASmI,IAFa/B,EAGlCoF,gBACED,UAT8C,UAUxCxE,KACN,gIAX8C,MAc5CyE,GACJD,WAEIhE,EAAQiE,eAFZD,GAIItN,EAAeE,EAAgBgJ,EAAKqD,QAALrD,CAAczD,MAA9BvF,EACfsN,EAAmBtK,KAGnBV,EAAS,UACHiD,EAAOmE,QADJ,EAIT9G,EAAU2K,KAEY,CAA1BxQ,QAAOyQ,gBAAPzQ,EAA+B,GAFjBwQ,EAKVrL,EAAc,QAAN4E,KAAiB,KAAjBA,CAAyB,SACjC1E,EAAc,OAAN4E,KAAgB,MAAhBA,CAAyB,QAKjCyG,EAAmB9C,EAAyB,WAAzBA,KAWrB3I,GAAMF,OACI,QAAVI,IAG4B,MAA1BpC,KAAarB,SACT,CAACqB,EAAauD,YAAd,CAA6BT,EAAQb,OAErC,CAACuL,EAAiBxK,MAAlB,CAA2BF,EAAQb,OAGrCa,EAAQd,MAEF,OAAVM,IAC4B,MAA1BtC,KAAarB,SACR,CAACqB,EAAasD,WAAd,CAA4BR,EAAQX,MAEpC,CAACqL,EAAiBzK,KAAlB,CAA0BD,EAAQX,MAGpCW,EAAQZ,KAEbqL,yBAC0B,QAAA,eACZ,OACA,IACT3C,WAAa,gBACf,MAECgD,GAAsB,QAAVxL,IAAqB,CAAC,CAAtBA,CAA0B,EACtCyL,EAAuB,OAAVvL,IAAoB,CAAC,CAArBA,CAAyB,OAC5BN,GAJX,MAKWE,GALX,GAME0I,cAAc,MAAA,SAIjByB,GAAa,eACFnD,EAAKtD,SADH,WAKdyG,kBAAiCnD,EAAKmD,cACtC7J,cAAyB0G,EAAK1G,UAC9BiK,iBAAmBvD,EAAKpG,OAALoG,CAAa4E,MAAU5E,EAAKuD,eChGtD,kBAIE,MACMsB,GAAa5F,IAAgB,CAAC,CAAE+B,MAAF,CAAD,GAAcA,KAA9B/B,EAEb6F,EACJ,CAAC,EAAD,EACArF,EAAUsB,IAAVtB,CAAe5G,KAEXA,EAASmI,IAATnI,MACAA,EAASiH,OADTjH,EAEAA,EAASvB,KAATuB,CAAiBgM,EAAWvN,KAJhCmI,KAQE,GAAa,MACToF,QAAc,MACdE,OAAa,cACXnF,QACL,6BAAA,6DAAA,eCrBP,gBAA6C,IAEvC,CAACoF,GAAmBhF,EAAKqD,QAALrD,CAAcP,SAAjCuF,CAA4C,OAA5CA,CAAqD,cAArDA,cAID1B,GAAelD,EAAQlL,WAGC,QAAxB,iBACa8K,EAAKqD,QAALrD,CAAczD,MAAdyD,CAAqBiF,aAArBjF,IAGX,qBAMA,CAACA,EAAKqD,QAALrD,CAAczD,MAAdyD,CAAqB/H,QAArB+H,mBACKJ,KACN,wEAMAlD,GAAYsD,EAAKtD,SAALsD,CAAepC,KAAfoC,CAAqB,GAArBA,EAA0B,CAA1BA,EACZ,CAAEzD,QAAF,CAAUnG,WAAV,EAAwB4J,EAAKpG,QAC7BkK,EAAsD,CAAC,CAA1C,oBAAkBvP,OAAlB,IAEb2Q,EAAMpB,EAAa,QAAbA,CAAwB,QAC9BqB,EAAkBrB,EAAa,KAAbA,CAAqB,OACvCzL,EAAO8M,EAAgBC,WAAhBD,GACPE,EAAUvB,EAAa,MAAbA,CAAsB,MAChCwB,EAASxB,EAAa,QAAbA,CAAwB,QACjCyB,EAAmBjH,QAQrBlI,OAAuCmG,IA5CA,KA6CpC3C,QAAQ2C,WACXA,MAAgBnG,MAAhBmG,CA9CuC,EAiDvCnG,OAAqCmG,IAjDE,KAkDpC3C,QAAQ2C,WACXnG,OAAqCmG,IAnDE,IAqDtC3C,QAAQ2C,OAAS5B,EAAcqF,EAAKpG,OAALoG,CAAazD,MAA3B5B,CArDqB,MAwDrC6K,GAASpP,KAAkBA,KAAiB,CAAnCA,CAAuCmP,EAAmB,EAInEjQ,EAAMU,EAAyBgK,EAAKqD,QAALrD,CAAczD,MAAvCvG,EACNyP,EAAmBpM,WAAW/D,WAAK,GAALA,CAAX+D,EACnBqM,EAAmBrM,WAAW/D,WAAK,QAALA,CAAX+D,KACrBsM,GACFH,EAASxF,EAAKpG,OAALoG,CAAazD,MAAbyD,GAATwF,cAGUjM,KAAKC,GAALD,CAASA,KAAKqM,GAALrM,CAASgD,MAAThD,GAATA,CAA8D,CAA9DA,IAEP+J,iBACA1J,QAAQgL,MAAQ,KACXrL,KAAKiK,KAALjK,GADW,KAER,EAFQ,IC3EvB,cAAwD,IACpC,KAAdoE,WACK,QAF6C,MAG7B,OAAdA,IAH2C,CAI7C,KAJ6C,GCwBxD,yKAAA,CC5BA,KAAMkI,IAAkBC,GAAWpG,KAAXoG,CAAiB,CAAjBA,CAAxB,CAYA,cAA6CC,IAA7C,CAA8D,MACtDC,GAAQH,GAAgBtR,OAAhBsR,IACR3G,EAAM2G,GACTnG,KADSmG,CACHG,EAAQ,CADLH,EAETI,MAFSJ,CAEFA,GAAgBnG,KAAhBmG,CAAsB,CAAtBA,GAFEA,QAGLE,GAAU7G,EAAIgH,OAAJhH,EAAV6G,QCZHI,IAAY,MACV,MADU,WAEL,WAFK,kBAGE,kBAHF,EAalB,gBAA4C,IAEtC3E,EAAkBxB,EAAKqD,QAALrD,CAAcP,SAAhC+B,CAA2C,OAA3CA,cAIAxB,EAAKoG,OAALpG,EAAgBA,EAAKtD,SAALsD,GAAmBA,EAAKQ,gCAKtCvE,GAAaU,EACjBqD,EAAKqD,QAALrD,CAAczD,MADGI,CAEjBqD,EAAKqD,QAALrD,CAAc5J,SAFGuG,CAGjByD,EAAQ5D,OAHSG,CAIjByD,EAAQhE,iBAJSO,CAKjBqD,EAAKK,aALY1D,KAQfD,GAAYsD,EAAKtD,SAALsD,CAAepC,KAAfoC,CAAqB,GAArBA,EAA0B,CAA1BA,EACZqG,EAAoBvH,KACpBnB,EAAYqC,EAAKtD,SAALsD,CAAepC,KAAfoC,CAAqB,GAArBA,EAA0B,CAA1BA,GAAgC,GAE5CsG,YAEIlG,EAAQmG,cACTJ,IAAUK,OACD,gBAETL,IAAUM,YACDC,gBAETP,IAAUQ,mBACDD,yBAGAtG,EAAQmG,mBAGd5G,QAAQ,OAAiB,IAC7BjD,OAAsB4J,EAAUlS,MAAVkS,GAAqBN,EAAQ,aAI3ChG,EAAKtD,SAALsD,CAAepC,KAAfoC,CAAqB,GAArBA,EAA0B,CAA1BA,CALqB,GAMblB,IANa,MAQ3BP,GAAgByB,EAAKpG,OAALoG,CAAazD,OAC7BqK,EAAa5G,EAAKpG,OAALoG,CAAa5J,UAG1BqN,EAAQlK,KAAKkK,MACboD,EACW,MAAdnK,MACC+G,EAAMlF,EAActF,KAApBwK,EAA6BA,EAAMmD,EAAW5N,IAAjByK,CAD9B/G,EAEc,OAAdA,MACC+G,EAAMlF,EAAcvF,IAApByK,EAA4BA,EAAMmD,EAAW3N,KAAjBwK,CAH7B/G,EAIc,KAAdA,MACC+G,EAAMlF,EAAcxF,MAApB0K,EAA8BA,EAAMmD,EAAW9N,GAAjB2K,CAL/B/G,EAMc,QAAdA,MACC+G,EAAMlF,EAAczF,GAApB2K,EAA2BA,EAAMmD,EAAW7N,MAAjB0K,EAEzBqD,EAAgBrD,EAAMlF,EAAcvF,IAApByK,EAA4BA,EAAMxH,EAAWjD,IAAjByK,EAC5CsD,EAAiBtD,EAAMlF,EAActF,KAApBwK,EAA6BA,EAAMxH,EAAWhD,KAAjBwK,EAC9CuD,EAAevD,EAAMlF,EAAczF,GAApB2K,EAA2BA,EAAMxH,EAAWnD,GAAjB2K,EAC1CwD,EACJxD,EAAMlF,EAAcxF,MAApB0K,EAA8BA,EAAMxH,EAAWlD,MAAjB0K,EAE1ByD,EACW,MAAdxK,SACc,OAAdA,OADAA,EAEc,KAAdA,OAFAA,EAGc,QAAdA,QAGGoH,EAAsD,CAAC,CAA1C,oBAAkBvP,OAAlB,IAGb4S,EACJ,CAAC,CAAC/G,EAAQgH,cAAV,GACEtD,GAA4B,OAAdnG,IAAdmG,KACCA,GAA4B,KAAdnG,IAAdmG,GADDA,EAEC,IAA6B,OAAdnG,IAAf,GAFDmG,EAGC,IAA6B,KAAdnG,IAAf,GAJH,EAOI0J,EACJ,CAAC,CAACjH,EAAQkH,uBAAV,GACExD,GAA4B,OAAdnG,IAAdmG,KACCA,GAA4B,KAAdnG,IAAdmG,GADDA,EAEC,IAA6B,OAAdnG,IAAf,GAFDmG,EAGC,IAA6B,KAAdnG,IAAf,GAJH,EAMI4J,EAAmBJ,KAtDQ,CAwD7BN,OAxD6B,MA0D1BT,UA1D0B,EA4D3BS,IA5D2B,MA6DjBP,EAAUN,EAAQ,CAAlBM,CA7DiB,QAiEjBkB,KAjEiB,IAoE1B9K,UAAYA,GAAaiB,EAAY,KAAZA,CAA8B,EAA3CjB,CApEc,GAwE1B9C,QAAQ2C,YACRyD,EAAKpG,OAALoG,CAAazD,OACbkE,EACDT,EAAKqD,QAALrD,CAAczD,MADbkE,CAEDT,EAAKpG,OAALoG,CAAa5J,SAFZqK,CAGDT,EAAKtD,SAHJ+D,EA1E0B,GAiFxBE,EAAaX,EAAKqD,QAALrD,CAAcP,SAA3BkB,GAA4C,MAA5CA,CAjFwB,CAAnC,KCrDF,cAA2C,MACnC,CAAEpE,QAAF,CAAUnG,WAAV,EAAwB4J,EAAKpG,QAC7B8C,EAAYsD,EAAKtD,SAALsD,CAAepC,KAAfoC,CAAqB,GAArBA,EAA0B,CAA1BA,EACZyD,EAAQlK,KAAKkK,MACbK,EAAsD,CAAC,CAA1C,oBAAkBvP,OAAlB,IACb8D,EAAOyL,EAAa,OAAbA,CAAuB,SAC9BwB,EAASxB,EAAa,MAAbA,CAAsB,MAC/BnF,EAAcmF,EAAa,OAAbA,CAAuB,eAEvCvH,MAAekH,EAAMrN,IAANqN,MACZ7J,QAAQ2C,UACXkH,EAAMrN,IAANqN,EAA2BlH,MAE3BA,KAAiBkH,EAAMrN,IAANqN,MACd7J,QAAQ2C,UAAiBkH,EAAMrN,IAANqN,KCLlC,oBAA2E,OA6B9DlK,KAAKC,GA7ByD,MAEnEoE,GAAQ6J,EAAIpI,KAAJoI,CAAU,2BAAVA,EACRvE,EAAQ,CAACtF,EAAM,CAANA,EACToF,EAAOpF,EAAM,CAANA,KAGT,eAIsB,CAAtBoF,KAAKzO,OAALyO,CAAa,GAAbA,EAAyB,IACvB9N,iBAEG,mBAGA,QACA,uBAKD6E,GAAOY,WACNZ,MAAoB,GAApBA,EAbT,CAcO,GAAa,IAATiJ,MAA0B,IAATA,IAArB,CAAoC,IAErC0E,YACS,IAAT1E,KACKzJ,EACLvF,SAAS2C,eAAT3C,CAAyBqG,YADpBd,CAELxF,OAAO6H,WAAP7H,EAAsB,CAFjBwF,EAKAA,EACLvF,SAAS2C,eAAT3C,CAAyBoG,WADpBb,CAELxF,OAAO4H,UAAP5H,EAAqB,CAFhBwF,EAKFmO,EAAO,GAAPA,EAdF,UAiCT,oBAKE,MACM9N,SAKA+N,EAAyD,CAAC,CAA9C,oBAAkBpT,OAAlB,IAIZqT,EAAY/L,EAAO+B,KAAP/B,CAAa,SAAbA,EAAwBoB,GAAxBpB,CAA4BgM,KAAQA,EAAKC,IAALD,EAApChM,EAIZkM,EAAUH,EAAUrT,OAAVqT,CACd3I,IAAgB4I,KAAgC,CAAC,CAAzBA,KAAKG,MAALH,CAAY,MAAZA,CAAxB5I,CADc2I,EAIZA,MAA0D,CAAC,CAArCA,QAAmBrT,OAAnBqT,CAA2B,GAA3BA,CAlB1B,UAmBUhI,KACN,+EApBJ,MA0BMqI,GAAa,iBACfC,GAAkB,CAAC,CAAbH,KASN,GATMA,CACN,CACEH,EACGlI,KADHkI,CACS,CADTA,IAEG3B,MAFH2B,CAEU,CAACA,KAAmBhK,KAAnBgK,IAAqC,CAArCA,CAAD,CAFVA,CADF,CAIE,CAACA,KAAmBhK,KAAnBgK,IAAqC,CAArCA,CAAD,EAA0C3B,MAA1C,CACE2B,EAAUlI,KAAVkI,CAAgBG,EAAU,CAA1BH,CADF,CAJF,WAWEM,EAAIjL,GAAJiL,CAAQ,OAAe,MAErBvJ,GAAc,CAAW,CAAVqH,KAAc,EAAdA,EAAD,EAChB,QADgB,CAEhB,WACAmC,YAEFC,GAGGC,MAHHD,CAGU,OACkB,EAApB7K,KAAEA,EAAEnJ,MAAFmJ,CAAW,CAAbA,GAAoD,CAAC,CAA3B,aAAWhJ,OAAX,GADxB,IAEFgJ,EAAEnJ,MAAFmJ,CAAW,IAFT,KAAA,SAMFA,EAAEnJ,MAAFmJ,CAAW,KANT,KAAA,IAUGA,EAAE0I,MAAF1I,GAbb6K,KAiBGnL,GAjBHmL,CAiBOX,KAAOa,WAjBdF,CAPE,CAAAF,IA6BFvI,QAAQ,OAAe,GACtBA,QAAQ,OAAkB,CACvBsD,IADuB,SAEP4E,GAA2B,GAAnBO,KAAGG,EAAS,CAAZH,EAAyB,CAAC,CAA1BA,CAA8B,CAAtCP,CAFO,CAA7B,EADF,KAmBF,cAAqC,CAAEhM,QAAF,CAArC,CAAiD,MACzC,CAAEa,WAAF,CAAa9C,QAAS,CAAE2C,QAAF,CAAUnG,WAAV,CAAtB,IACAoS,EAAgB9L,EAAUkB,KAAVlB,CAAgB,GAAhBA,EAAqB,CAArBA,KAElB9C,YACAqJ,EAAU,EAAVA,EACQ,CAAC,EAAD,CAAU,CAAV,EAEAwF,YAGU,MAAlBD,QACK1P,KAAOc,EAAQ,CAARA,IACPZ,MAAQY,EAAQ,CAARA,GACY,OAAlB4O,QACF1P,KAAOc,EAAQ,CAARA,IACPZ,MAAQY,EAAQ,CAARA,GACY,KAAlB4O,QACFxP,MAAQY,EAAQ,CAARA,IACRd,KAAOc,EAAQ,CAARA,GACa,QAAlB4O,SACFxP,MAAQY,EAAQ,CAARA,IACRd,KAAOc,EAAQ,CAARA,KAGX2C,WCpLP,gBAAuD,IACjDH,GACFgE,EAAQhE,iBAARgE,EAA6BpJ,EAAgBgJ,EAAKqD,QAALrD,CAAczD,MAA9BvF,EAK3BgJ,EAAKqD,QAALrD,CAAc5J,SAAd4J,IAPiD,KAQ/BhJ,IAR+B,OAc/C0R,GAAgB/G,EAAyB,WAAzBA,EAChBgH,EAAe3I,EAAKqD,QAALrD,CAAczD,MAAdyD,CAAqBuB,MACpC,CAAEzI,KAAF,CAAOE,MAAP,CAAa,KAAb,MACOF,IAAM,EAjBkC,GAkBxCE,KAAO,EAlBiC,MAmBvB,EAnBuB,MAqB/CiD,GAAaU,EACjBqD,EAAKqD,QAALrD,CAAczD,MADGI,CAEjBqD,EAAKqD,QAALrD,CAAc5J,SAFGuG,CAGjByD,EAAQ5D,OAHSG,GAKjBqD,EAAKK,aALY1D,IAUN7D,KA/BwC,GAgCxCE,MAhCwC,OAAA,GAmC7CiD,YAnC6C,MAqC/C3E,GAAQ8I,EAAQwI,YAClBrM,GAASyD,EAAKpG,OAALoG,CAAazD,YAEpBsM,GAAQ,WACO,IACb3F,GAAQ3G,WAEVA,MAAoBN,IAApBM,EACA,CAAC6D,EAAQ0I,wBAEDvP,KAAKC,GAALD,CAASgD,IAAThD,CAA4B0C,IAA5B1C,GAEH,CAAE,KAAF,CATG,CAAA,aAWS,MACbkF,GAAyB,OAAd/B,KAAwB,MAAxBA,CAAiC,SAC9CwG,GAAQ3G,WAEVA,MAAoBN,IAApBM,EACA,CAAC6D,EAAQ0I,wBAEDvP,KAAKqM,GAALrM,CACNgD,IADMhD,CAEN0C,MACiB,OAAdS,KAAwBH,EAAO1C,KAA/B6C,CAAuCH,EAAOzC,MADjDmC,CAFM1C,GAMH,CAAE,KAAF,EAxBG,WA4BRoG,QAAQjD,KAAa,MACnBrE,GACmC,CAAC,CAAxC,kBAAgB9D,OAAhB,IAAwD,WAAxD,CAA4C,mBACrBsU,QAH3B,KAMKjP,QAAQ2C,WC9Ef,cAAoC,MAC5BG,GAAYsD,EAAKtD,UACjB8L,EAAgB9L,EAAUkB,KAAVlB,CAAgB,GAAhBA,EAAqB,CAArBA,EAChBqM,EAAiBrM,EAAUkB,KAAVlB,CAAgB,GAAhBA,EAAqB,CAArBA,OAGH,MACZ,CAAEtG,WAAF,CAAamG,QAAb,EAAwByD,EAAKpG,QAC7BkK,EAA0D,CAAC,CAA9C,oBAAkBvP,OAAlB,IACb8D,EAAOyL,EAAa,MAAbA,CAAsB,MAC7BnF,EAAcmF,EAAa,OAAbA,CAAuB,SAErCkF,EAAe,OACZ,CAAE,IAAQ5S,IAAV,CADY,KAEd,KACKA,KAAkBA,IAAlBA,CAA2CmG,IADhD,CAFc,IAOhB3C,QAAQ2C,cAAyByM,eChB1C,cAAmC,IAC7B,CAAChE,GAAmBhF,EAAKqD,QAALrD,CAAcP,SAAjCuF,CAA4C,MAA5CA,CAAoD,iBAApDA,gBAICnI,GAAUmD,EAAKpG,OAALoG,CAAa5J,UACvB6S,EAAQhK,EACZe,EAAKqD,QAALrD,CAAcP,SADFR,CAEZpG,KAA8B,iBAAlBA,KAASmI,IAFT/B,EAGZhD,cAGAY,EAAQ9D,MAAR8D,CAAiBoM,EAAMnQ,GAAvB+D,EACAA,EAAQ7D,IAAR6D,CAAeoM,EAAMhQ,KADrB4D,EAEAA,EAAQ/D,GAAR+D,CAAcoM,EAAMlQ,MAFpB8D,EAGAA,EAAQ5D,KAAR4D,CAAgBoM,EAAMjQ,KACtB,IAEIgH,OAAKkJ,gBAIJA,OANL,GAOK/F,WAAW,uBAAyB,EAZ3C,KAaO,IAEDnD,OAAKkJ,gBAIJA,OANA,GAOA/F,WAAW,mCC/BpB,cAAoC,MAC5BzG,GAAYsD,EAAKtD,UACjB8L,EAAgB9L,EAAUkB,KAAVlB,CAAgB,GAAhBA,EAAqB,CAArBA,EAChB,CAAEH,QAAF,CAAUnG,WAAV,EAAwB4J,EAAKpG,QAC7B4E,EAAuD,CAAC,CAA9C,oBAAkBjK,OAAlB,IAEV4U,EAA4D,CAAC,CAA5C,kBAAgB5U,OAAhB,aAEhBiK,EAAU,MAAVA,CAAmB,OACxBpI,MACC+S,EAAiB5M,EAAOiC,EAAU,OAAVA,CAAoB,QAA3BjC,CAAjB4M,CAAwD,CADzD/S,IAGGsG,UAAYoC,OACZlF,QAAQ2C,OAAS5B,OCSxB,OAAe,OASN,OAEE,GAFF,WAAA,MAAA,CATM,QAwDL,OAEC,GAFD,WAAA,MAAA,QAUE,CAVF,CAxDK,iBAsFI,OAER,GAFQ,WAAA,MAAA,yCAAA,SAmBN,CAnBM,mBAyBI,cAzBJ,CAtFJ,cA2HC,OAEL,GAFK,WAAA,MAAA,CA3HD,OA8IN,OAEE,GAFF,WAAA,MAAA,SAQI,WARJ,CA9IM,MAoKP,OAEG,GAFH,WAAA,MAAA,UAaM,MAbN,SAkBK,CAlBL,mBAyBe,UAzBf,kBAAA,2BAAA,CApKO,OAuNN,OAEE,GAFF,WAAA,MAAA,CAvNM,MA0OP,OAEG,GAFH,WAAA,MAAA,CA1OO,cAkQC,OAEL,GAFK,WAAA,MAAA,mBAAA,GAkBT,QAlBS,GAwBT,OAxBS,CAlQD,YA4SD,OAEH,GAFG,WAAA,KAAA,UAAA,uBAAA,CA5SC,CAAf,ICde,WAKF,QALE,iBAAA,iBAAA,mBAAA,UAgCH,IAAM,CAhCH,CAAA,UA0CH,IAAM,CA1CH,CAAA,aAAA,CDcf,CE3BA,QAO4B,iBASKyF,KAAc,MAyF7CqC,eAAiB,IAAM2G,sBAAsB,KAAKC,MAA3BD,CAzFsB,MAEtCC,OAASC,EAAS,KAAKD,MAAL,CAAYE,IAAZ,CAAiB,IAAjB,CAATD,CAF6B,MAKtClJ,aAAeoJ,GAAOC,WALgB,MAQtCxJ,MAAQ,eAAA,aAAA,iBAAA,CAR8B,MAetC7J,UAAYA,GAAaA,EAAUsT,MAAvBtT,CAAgCA,EAAU,CAAVA,CAAhCA,EAf0B,MAgBtCmG,OAASA,GAAUA,EAAOmN,MAAjBnN,CAA0BA,EAAO,CAAPA,CAA1BA,EAhB6B,MAmBtC6D,QAAQX,YAnB8B,QAoBpCzC,UACFwM,GAAOC,QAAPD,CAAgB/J,UAChBW,EAAQX,YACVE,QAAQqB,KAAQ,MACZZ,QAAQX,kBAEP+J,GAAOC,QAAPD,CAAgB/J,SAAhB+J,QAEApJ,EAAQX,SAARW,CAAoBA,EAAQX,SAARW,GAApBA,IARR,EApB2C,MAiCtCX,UAAY1C,OAAOC,IAAPD,CAAY,KAAKqD,OAAL,CAAaX,SAAzB1C,EACdE,GADcF,CACViE,gBAEA,KAAKZ,OAAL,CAAaX,SAAb,IAHU1C,EAMdK,IANcL,CAMT,OAAUQ,EAAEjG,KAAFiG,CAAUF,EAAE/F,KANbyF,CAjC0B,MA6CtC0C,UAAUE,QAAQgK,KAAmB,CACpCA,EAAgB7J,OAAhB6J,EAA2B5J,EAAW4J,EAAgBC,MAA3B7J,CADS,IAEtB6J,OACd,KAAKxT,UACL,KAAKmG,OACL,KAAK6D,UAEL,KAAKH,MAPX,EA7C2C,MA0DtCoJ,QA1DsC,MA4DrC9G,GAAgB,KAAKnC,OAAL,CAAamC,cA5DQ,QA+DpCsH,sBA/DoC,MAkEtC5J,MAAMsC,wBAKJ,OACA8G,GAAOpU,IAAPoU,CAAY,IAAZA,WAEC,OACDS,GAAQ7U,IAAR6U,CAAa,IAAbA,wBAEc,OACdD,GAAqB5U,IAArB4U,CAA0B,IAA1BA,yBAEe,OACfjI,GAAsB3M,IAAtB2M,CAA2B,IAA3BA,EA1FiB,CAAP4H,GAoHZO,KApHYP,CAoHJ,CAAmB,WAAlB,QAAOzV,OAAP,CAAyCiW,MAAzC,CAAgCjW,MAAjC,EAAkDkW,YApH9CT,GAsHZ1D,UAtHY0D,IAAAA,GAwHZC,QAxHYD"}