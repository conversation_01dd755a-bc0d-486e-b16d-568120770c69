@extends('layouts.app')

@section('content')
<!-- Enhanced User Section with Dashboard Styling -->
<div class="enhanced-user-section">
  <!-- Background Effects -->
  <div class="section-background">
    <div class="blur-overlay"></div>
    <div class="gradient-mesh"></div>
  </div>

  <!-- Enhanced Header -->
  <div class="section-header">
    <div class="header-container">
      <div class="header-left">
        <h1 class="section-title">
          <i class="bx bx-user-circle"></i>
          User Management
        </h1>
        <p class="section-subtitle">
          Manage and organize user accounts
          <span class="count-badge">{{ count($users) }} users</span>
        </p>
      </div>

      <div class="header-right">
        <div class="header-actions">
          <!-- Export Buttons -->
          <div class="export-dropdown">
            <button class="action-btn secondary dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
              <i class="bx bx-download"></i>
              Export
            </button>
            <ul class="dropdown-menu">
              <li>
                <a class="dropdown-item" href="#" id="exportExcel">
                  <i class="bx bx-file-blank text-success me-2"></i>
                  Export to Excel
                </a>
              </li>
              <li>
                <a class="dropdown-item" href="#" id="exportPDF">
                  <i class="bx bx-file text-danger me-2"></i>
                  Export to PDF
                </a>
              </li>
            </ul>
          </div>

          <!-- Search -->
          <div class="search-container">
            <input type="text" class="search-input" placeholder="Search users..." id="userSearch">
            <i class="bx bx-search search-icon"></i>
          </div>

          <!-- Create User Button -->
          <a href="{{ route('users.create') }}" class="action-btn primary">
            <i class="bx bx-plus"></i>
            <span>Create User</span>
          </a>
        </div>
      </div>
    </div>
  </div>

  <!-- Enhanced Content Card -->
  <div class="content-container">
    <div class="enhanced-card">
      @if(session('success'))
        <div class="alert alert-success alert-dismissible fade show custom-alert" role="alert">
          <i class="bx bx-check-circle me-2"></i>
          {{ session('success') }}
          <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
      @endif

      <!-- Enhanced Table -->
      <div class="table-container">
        <div class="table-responsive">
          <table id="usersTable" class="enhanced-table">
            <thead>
              <tr>
                <th>
                  <div class="th-content">
                    <span>Sr No</span>
                  </div>
                </th>
                <th>
                  <div class="th-content">
                    <i class="bx bx-user me-1"></i>
                    <span>Name</span>
                  </div>
                </th>
                <th>
                  <div class="th-content">
                    <i class="bx bx-envelope me-1"></i>
                    <span>Email</span>
                  </div>
                </th>
                <th>
                  <div class="th-content">
                    <i class="bx bx-shield me-1"></i>
                    <span>Role</span>
                  </div>
                </th>
                <th class="text-center no-export">
                  <div class="th-content">
                    <i class="bx bx-cog me-1"></i>
                    <span>Actions</span>
                  </div>
                </th>
              </tr>
            </thead>
            <tbody>
              @forelse ($users as $index => $user)
                <tr class="table-row">
                  <td>
                    <span class="row-number">{{ $index + 1 }}</span>
                  </td>
                  <td>
                    <div class="user-info">
                      <div class="user-avatar">
                        <i class="bx bx-user"></i>
                      </div>
                      <div class="user-details">
                        <span class="user-name">{{ $user->username }}</span>
                        <small class="user-meta">ID: {{ $user->id }}</small>
                      </div>
                    </div>
                  </td>
                  <td>
                    <span class="email-text">{{ $user->email }}</span>
                  </td>
                  <td>
                    <span class="role-badge role-{{ $user->role }}">
                      <i class="bx bx-{{ $user->role == 'admin' ? 'crown' : ($user->role == 'tutor' ? 'user-voice' : 'user-pin') }}"></i>
                      {{ ucfirst($user->role) }}
                    </span>
                  </td>
                  <td class="text-center">
                    <div class="action-buttons">
                      <a href="{{ route('users.show', $user->id) }}" class="action-btn-small view" title="View">
                        <i class="bx bx-show"></i>
                      </a>
                      <a href="{{ route('users.edit', $user->id) }}" class="action-btn-small edit" title="Edit">
                        <i class="bx bx-edit"></i>
                      </a>
                      <button type="button" class="action-btn-small delete" onclick="deleteUser({{ $user->id }})" title="Delete">
                        <i class="bx bx-trash"></i>
                      </button>
                    </div>
                  </td>
                </tr>
              @empty
                <tr>
                  <td colspan="5" class="text-center py-5">
                    <div class="empty-state">
                      <div class="empty-icon">
                        <i class="bx bx-user-x"></i>
                      </div>
                      <h6 class="empty-title">No Users Found</h6>
                      <p class="empty-text">Start by creating your first user account</p>
                      <a href="{{ route('users.create') }}" class="btn btn-primary btn-sm">
                        <i class="bx bx-plus me-1"></i>
                        Create User
                      </a>
                    </div>
                  </td>
                </tr>
              @endforelse
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  function deleteUser(userID) {
    Swal.fire({
        title: 'Are you sure?',
        text: 'Do you want to delete this user?',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: 'Yes, delete it!',
        cancelButtonText: 'No, cancel!',
        reverseButtons: true
    }).then((result) => {
        if (result.isConfirmed) {
            $.ajax({
                url: '{{ route('users.delete', '') }}/' + userID,
                type: 'DELETE',
                headers: {
                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                },
                success: function(response) {
                    if (response.success) {
                        Swal.fire(
                            'Deleted!',
                            response.message || 'User deleted successfully.',
                            'success'
                        ).then(() => {
                            location.reload();
                        });
                    } else {
                        Swal.fire(
                            'Error!',
                            response.message || 'Failed to delete user.',
                            'error'
                        );
                    }
                },
                error: function(xhr) {
                    Swal.fire(
                        'Error!',
                        xhr.responseJSON?.message || 'An unexpected error occurred.',
                        'error'
                    );
                }
            });
        }
    });
}
</script>

@push('styles')
<style>
/* Enhanced User Section Styling - Dashboard Theme */
.enhanced-user-section {
  position: relative;
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

/* Background Effects */
.section-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

.blur-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(248, 250, 252, 0.8);
  backdrop-filter: blur(10px);
}

.gradient-mesh {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 20% 50%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
              radial-gradient(circle at 80% 20%, rgba(139, 92, 246, 0.1) 0%, transparent 50%),
              radial-gradient(circle at 40% 80%, rgba(34, 197, 94, 0.1) 0%, transparent 50%);
}

/* Enhanced Header */
.section-header {
  position: relative;
  z-index: 10;
  padding: 1rem 1.5rem;
  margin: 0.75rem 0.75rem 0 0.75rem;
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(226, 232, 240, 0.8);
  border-radius: 0.75rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.header-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  max-width: 1400px;
  margin: 0 auto;
}

.header-left {
  flex: 1;
}

.section-title {
  font-size: 1.75rem;
  font-weight: 700;
  color: #4a5568;
  margin: 0;
  line-height: 1.2;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.section-title i {
  color: #3b82f6;
  font-size: 2rem;
}

.section-subtitle {
  font-size: 0.875rem;
  color: #64748b;
  margin: 0.25rem 0 0 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.count-badge {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  padding: 0.125rem 0.5rem;
  border-radius: 0.375rem;
  font-size: 0.75rem;
  font-weight: 600;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

/* Action Buttons */
.action-btn {
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  border: 1px solid rgba(226, 232, 240, 0.8);
  background: rgba(255, 255, 255, 0.95);
  color: #64748b;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.375rem;
  text-decoration: none;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.action-btn:hover {
  background: rgba(241, 245, 249, 0.9);
  color: #475569;
  border-color: rgba(203, 213, 225, 0.8);
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.action-btn.primary {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  border-color: #3b82f6;
}

.action-btn.primary:hover {
  background: linear-gradient(135deg, #1d4ed8, #1e40af);
  color: white;
  box-shadow: 0 2px 6px rgba(59, 130, 246, 0.3);
}

.action-btn.secondary {
  background: rgba(248, 250, 252, 0.9);
  border-color: rgba(226, 232, 240, 0.8);
}

/* Export Dropdown */
.export-dropdown .dropdown-menu {
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(226, 232, 240, 0.8);
  border-radius: 0.5rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  padding: 0.5rem;
  min-width: 180px;
}

.export-dropdown .dropdown-item {
  border-radius: 0.375rem;
  padding: 0.5rem 0.75rem;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
}

.export-dropdown .dropdown-item:hover {
  background: rgba(59, 130, 246, 0.08);
  color: #1e293b;
}

/* Search Container */
.search-container {
  position: relative;
  display: flex;
  align-items: center;
}

.search-input {
  padding: 0.5rem 0.75rem 0.5rem 2.5rem;
  border: 1px solid rgba(226, 232, 240, 0.8);
  border-radius: 0.5rem;
  background: rgba(255, 255, 255, 0.95);
  color: #4a5568;
  font-size: 0.875rem;
  width: 250px;
  transition: all 0.2s ease;
}

.search-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.search-icon {
  position: absolute;
  left: 0.75rem;
  color: #9ca3af;
  font-size: 1rem;
}

/* Content Container */
.content-container {
  position: relative;
  z-index: 5;
  padding: 0.75rem;
  max-width: 1400px;
  margin: 0 auto;
}

.enhanced-card {
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(226, 232, 240, 0.8);
  border-radius: 0.75rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  overflow: hidden;
}

/* Custom Alert */
.custom-alert {
  margin: 1rem 1rem 0 1rem;
  border: none;
  border-radius: 0.5rem;
  background: rgba(34, 197, 94, 0.1);
  border-left: 4px solid #22c55e;
  color: #166534;
}

/* Enhanced Table */
.table-container {
  padding: 1rem;
}

.enhanced-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  background: transparent;
}

.enhanced-table thead th {
  background: rgba(248, 250, 252, 0.8);
  border: none;
  padding: 1rem 0.75rem;
  font-weight: 600;
  color: #4a5568;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.025em;
  border-bottom: 2px solid rgba(226, 232, 240, 0.8);
}

.enhanced-table thead th:first-child {
  border-top-left-radius: 0.5rem;
}

.enhanced-table thead th:last-child {
  border-top-right-radius: 0.5rem;
}

.th-content {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.th-content i {
  color: #3b82f6;
  font-size: 0.875rem;
}

.table-row {
  transition: all 0.2s ease;
  border-bottom: 1px solid rgba(226, 232, 240, 0.5);
}

.table-row:hover {
  background: rgba(59, 130, 246, 0.02);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.table-row td {
  padding: 1rem 0.75rem;
  vertical-align: middle;
  border: none;
}

.row-number {
  font-weight: 600;
  color: #6b7280;
  font-size: 0.875rem;
}

/* User Info */
.user-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.user-avatar {
  width: 2.5rem;
  height: 2.5rem;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.125rem;
}

.user-details {
  display: flex;
  flex-direction: column;
}

.user-name {
  font-weight: 600;
  color: #1e293b;
  font-size: 0.875rem;
}

.user-meta {
  color: #6b7280;
  font-size: 0.75rem;
}

.email-text {
  color: #4a5568;
  font-size: 0.875rem;
}

/* Role Badges */
.role-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.375rem;
  padding: 0.375rem 0.75rem;
  border-radius: 0.5rem;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.role-badge.role-admin {
  background: rgba(139, 92, 246, 0.1);
  color: #7c3aed;
  border: 1px solid rgba(139, 92, 246, 0.2);
}

.role-badge.role-tutor {
  background: rgba(34, 197, 94, 0.1);
  color: #16a34a;
  border: 1px solid rgba(34, 197, 94, 0.2);
}

.role-badge.role-parent {
  background: rgba(59, 130, 246, 0.1);
  color: #2563eb;
  border: 1px solid rgba(59, 130, 246, 0.2);
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: 0.375rem;
  justify-content: center;
}

.action-btn-small {
  width: 2rem;
  height: 2rem;
  border-radius: 0.375rem;
  border: 1px solid rgba(226, 232, 240, 0.8);
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  font-size: 0.875rem;
}

.action-btn-small.view {
  color: #3b82f6;
  border-color: rgba(59, 130, 246, 0.2);
}

.action-btn-small.view:hover {
  background: rgba(59, 130, 246, 0.1);
  color: #1d4ed8;
  transform: scale(1.1);
}

.action-btn-small.edit {
  color: #f59e0b;
  border-color: rgba(245, 158, 11, 0.2);
}

.action-btn-small.edit:hover {
  background: rgba(245, 158, 11, 0.1);
  color: #d97706;
  transform: scale(1.1);
}

.action-btn-small.delete {
  color: #ef4444;
  border-color: rgba(239, 68, 68, 0.2);
}

.action-btn-small.delete:hover {
  background: rgba(239, 68, 68, 0.1);
  color: #dc2626;
  transform: scale(1.1);
}

/* Empty State */
.empty-state {
  padding: 3rem 1rem;
  text-align: center;
}

.empty-icon {
  width: 4rem;
  height: 4rem;
  background: rgba(107, 114, 128, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1rem auto;
}

.empty-icon i {
  font-size: 2rem;
  color: #9ca3af;
}

.empty-title {
  color: #4a5568;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.empty-text {
  color: #6b7280;
  margin-bottom: 1.5rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .header-container {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }

  .header-actions {
    flex-wrap: wrap;
    width: 100%;
    justify-content: space-between;
  }

  .search-input {
    width: 200px;
  }

  .action-btn span {
    display: none;
  }

  .section-title {
    font-size: 1.5rem;
  }

  .user-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .action-buttons {
    flex-direction: column;
    gap: 0.25rem;
  }
}

@media (max-width: 640px) {
  .enhanced-table {
    font-size: 0.8rem;
  }

  .enhanced-table thead th,
  .table-row td {
    padding: 0.75rem 0.5rem;
  }

  .user-avatar {
    width: 2rem;
    height: 2rem;
    font-size: 1rem;
  }
}
</style>
@endpush

@push('scripts')
<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf-autotable/3.5.31/jspdf.plugin.autotable.min.js"></script>

<script>
$(document).ready(function() {
    // Initialize DataTable with enhanced options
    const table = initDataTable('usersTable', {
        order: [[1, 'asc']],
        columnDefs: [
            { orderable: false, targets: [0, 4] },
            { className: 'text-center', targets: [0, 4] }
        ],
        dom: 'rt<"d-flex justify-content-between align-items-center mt-3"<"dataTables_info"i><"dataTables_paginate"p>>',
        pageLength: 25,
        responsive: true
    });

    // Search functionality
    $('#userSearch').on('keyup', function() {
        table.search(this.value).draw();
    });

    // Export to Excel
    $('#exportExcel').on('click', function(e) {
        e.preventDefault();
        exportToExcel();
    });

    // Export to PDF
    $('#exportPDF').on('click', function(e) {
        e.preventDefault();
        exportToPDF();
    });

    function exportToExcel() {
        // Get table data
        const data = [];
        const headers = ['Sr No', 'Name', 'Email', 'Role'];
        data.push(headers);

        // Get visible rows from DataTable
        table.rows({ search: 'applied' }).every(function() {
            const rowData = this.data();
            const row = [
                $(rowData[0]).text(),
                $(rowData[1]).find('.user-name').text(),
                $(rowData[2]).text(),
                $(rowData[3]).find('.role-badge').text().trim()
            ];
            data.push(row);
        });

        // Create workbook
        const wb = XLSX.utils.book_new();
        const ws = XLSX.utils.aoa_to_sheet(data);

        // Style the header
        const range = XLSX.utils.decode_range(ws['!ref']);
        for (let col = range.s.c; col <= range.e.c; col++) {
            const cellAddress = XLSX.utils.encode_cell({ r: 0, c: col });
            if (!ws[cellAddress]) continue;
            ws[cellAddress].s = {
                font: { bold: true },
                fill: { fgColor: { rgb: "3B82F6" } },
                color: { rgb: "FFFFFF" }
            };
        }

        // Set column widths
        ws['!cols'] = [
            { width: 10 },
            { width: 20 },
            { width: 30 },
            { width: 15 }
        ];

        XLSX.utils.book_append_sheet(wb, ws, 'Users');
        XLSX.writeFile(wb, `users_list_${new Date().toISOString().split('T')[0]}.xlsx`);

        // Show success message
        Swal.fire({
            title: 'Export Successful!',
            text: 'Users list has been exported to Excel.',
            icon: 'success',
            timer: 2000,
            showConfirmButton: false
        });
    }

    function exportToPDF() {
        const { jsPDF } = window.jspdf;
        const doc = new jsPDF();

        // Add title
        doc.setFontSize(20);
        doc.setTextColor(59, 130, 246);
        doc.text('User Management Report', 20, 20);

        // Add date
        doc.setFontSize(10);
        doc.setTextColor(100, 100, 100);
        doc.text(`Generated on: ${new Date().toLocaleDateString()}`, 20, 30);

        // Prepare table data
        const tableData = [];
        table.rows({ search: 'applied' }).every(function() {
            const rowData = this.data();
            const row = [
                $(rowData[0]).text(),
                $(rowData[1]).find('.user-name').text(),
                $(rowData[2]).text(),
                $(rowData[3]).find('.role-badge').text().trim()
            ];
            tableData.push(row);
        });

        // Add table
        doc.autoTable({
            head: [['Sr No', 'Name', 'Email', 'Role']],
            body: tableData,
            startY: 40,
            theme: 'grid',
            headStyles: {
                fillColor: [59, 130, 246],
                textColor: [255, 255, 255],
                fontStyle: 'bold'
            },
            bodyStyles: {
                textColor: [74, 85, 104]
            },
            alternateRowStyles: {
                fillColor: [248, 250, 252]
            },
            columnStyles: {
                0: { cellWidth: 20, halign: 'center' },
                1: { cellWidth: 40 },
                2: { cellWidth: 60 },
                3: { cellWidth: 30, halign: 'center' }
            }
        });

        // Add footer
        const pageCount = doc.internal.getNumberOfPages();
        for (let i = 1; i <= pageCount; i++) {
            doc.setPage(i);
            doc.setFontSize(8);
            doc.setTextColor(150, 150, 150);
            doc.text(`Page ${i} of ${pageCount}`, doc.internal.pageSize.width - 30, doc.internal.pageSize.height - 10);
        }

        doc.save(`users_list_${new Date().toISOString().split('T')[0]}.pdf`);

        // Show success message
        Swal.fire({
            title: 'Export Successful!',
            text: 'Users list has been exported to PDF.',
            icon: 'success',
            timer: 2000,
            showConfirmButton: false
        });
    }
});
</script>
@endpush

@endsection