@extends('layouts.app')

@section('content')
<!-- Modern User Management Dashboard -->
<div class="modern-user-dashboard">
    <!-- Background Effects -->
    <div class="dashboard-background">
        <div class="blur-overlay"></div>
        <div class="gradient-mesh"></div>
    </div>

    <!-- Enhanced Header -->
    <div class="modern-header">
        <div class="header-container">
            <div class="header-left">
                <h1 class="page-title">
                    <i class="bx bx-users"></i>
                    User Management
                </h1>
                <p class="page-subtitle">
                    Manage system users and their permissions
                    <span class="user-count-badge">{{ count($users) }} users</span>
                </p>
            </div>
            <div class="header-actions">
                <button class="action-btn secondary" id="export-btn" title="Export Data">
                    <i class="bx bx-download"></i>
                    <span>Export</span>
                </button>
                <button class="action-btn secondary" id="filter-btn" title="Filter Users">
                    <i class="bx bx-filter"></i>
                    <span>Filter</span>
                </button>
                <a href="{{ route('users.create') }}" class="action-btn primary">
                    <i class="bx bx-plus"></i>
                    <span>Add User</span>
                </a>
            </div>
        </div>
    </div>

    <!-- Success Alert -->
    @if(session('success'))
        <div class="modern-alert success">
            <div class="alert-content">
                <i class="bx bx-check-circle"></i>
                <span>{{ session('success') }}</span>
                <button class="alert-close" onclick="this.parentElement.parentElement.remove()">
                    <i class="bx bx-x"></i>
                </button>
            </div>
        </div>
    @endif

    <!-- Enhanced User Cards Grid -->
    <div class="users-container">
        <div class="users-grid">
            @forelse ($users as $index => $user)
                <div class="user-card" data-user-id="{{ $user->id }}">
                    <div class="user-card-header">
                        <div class="user-avatar">
                            <i class="bx bx-user"></i>
                            <span class="user-status {{ $user->role }}"></span>
                        </div>
                        <div class="user-role-badge">
                            <span class="role-badge {{ $user->role }}">
                                {{ ucfirst($user->role) }}
                            </span>
                        </div>
                    </div>

                    <div class="user-card-body">
                        <h3 class="user-name">{{ $user->username }}</h3>
                        <p class="user-email">{{ $user->email }}</p>
                        <div class="user-meta">
                            <span class="meta-item">
                                <i class="bx bx-calendar"></i>
                                Joined {{ $user->created_at ? $user->created_at->format('M Y') : 'N/A' }}
                            </span>
                        </div>
                    </div>

                    <div class="user-card-actions">
                        <a href="{{ route('users.show', $user->id) }}" class="card-action-btn view" title="View User">
                            <i class="bx bx-show"></i>
                        </a>
                        <a href="{{ route('users.edit', $user->id) }}" class="card-action-btn edit" title="Edit User">
                            <i class="bx bx-edit"></i>
                        </a>
                        <button type="button" class="card-action-btn delete" onclick="deleteUser({{ $user->id }})" title="Delete User">
                            <i class="bx bx-trash"></i>
                        </button>
                    </div>
                </div>
            @empty
                <div class="empty-state">
                    <div class="empty-icon">
                        <i class="bx bx-user-x"></i>
                    </div>
                    <h3>No Users Found</h3>
                    <p>Get started by creating your first user account.</p>
                    <a href="{{ route('users.create') }}" class="empty-action-btn">
                        <i class="bx bx-plus"></i>
                        Create First User
                    </a>
                </div>
            @endforelse
        </div>
    </div>
</div>

<script>
  function deleteUser(userID) {
    Swal.fire({
        title: 'Are you sure?',
        text: 'Do you want to delete this user?',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: 'Yes, delete it!',
        cancelButtonText: 'No, cancel!',
        reverseButtons: true
    }).then((result) => {
        if (result.isConfirmed) {
            $.ajax({
                url: '{{ route('users.delete', '') }}/' + userID,
                type: 'DELETE',
                headers: {
                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                },
                success: function(response) {
                    if (response.success) {
                        Swal.fire(
                            'Deleted!',
                            response.message || 'User deleted successfully.',
                            'success'
                        ).then(() => {
                            location.reload();
                        });
                    } else {
                        Swal.fire(
                            'Error!',
                            response.message || 'Failed to delete user.',
                            'error'
                        );
                    }
                },
                error: function(xhr) {
                    Swal.fire(
                        'Error!',
                        xhr.responseJSON?.message || 'An unexpected error occurred.',
                        'error'
                    );
                }
            });
        }
    });
}
</script>

@push('styles')
<style>
/* Modern User Dashboard Styles */
.modern-user-dashboard {
    position: relative;
    min-height: 100vh;
    padding: 0;
    margin: 0;
}

/* Background Effects */
.dashboard-background {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.blur-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(248, 250, 252, 0.8);
    backdrop-filter: blur(20px);
}

.gradient-mesh {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(139, 92, 246, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 60%, rgba(16, 185, 129, 0.1) 0%, transparent 50%);
}

/* Enhanced Header */
.modern-header {
    position: relative;
    z-index: 10;
    padding: 1.5rem 2rem;
    margin: 1rem 1rem 0 1rem;
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(226, 232, 240, 0.8);
    border-radius: 1rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
}

.header-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    max-width: 1400px;
    margin: 0 auto;
}

.header-left {
    flex: 1;
}

.page-title {
    font-size: 2rem;
    font-weight: 700;
    color: #1e293b;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.page-title i {
    color: #3b82f6;
    font-size: 2.25rem;
}

.page-subtitle {
    font-size: 1rem;
    color: #64748b;
    margin: 0.5rem 0 0 0;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.user-count-badge {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.875rem;
    font-weight: 600;
}

.header-actions {
    display: flex;
    gap: 0.75rem;
    align-items: center;
}

.action-btn {
    padding: 0.75rem 1.25rem;
    border-radius: 0.75rem;
    border: none;
    font-size: 0.875rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
    min-height: 2.75rem;
}

.action-btn.secondary {
    background: rgba(248, 250, 252, 0.9);
    color: #64748b;
    border: 1px solid rgba(226, 232, 240, 0.8);
}

.action-btn.secondary:hover {
    background: rgba(241, 245, 249, 0.9);
    color: #475569;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.action-btn.primary {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.action-btn.primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
    color: white;
    text-decoration: none;
}

/* Modern Alert */
.modern-alert {
    position: relative;
    z-index: 15;
    margin: 1rem 2rem;
    padding: 1rem 1.5rem;
    border-radius: 0.75rem;
    backdrop-filter: blur(20px);
    border: 1px solid;
    animation: slideInDown 0.3s ease;
}

.modern-alert.success {
    background: rgba(16, 185, 129, 0.1);
    border-color: rgba(16, 185, 129, 0.3);
    color: #065f46;
}

.alert-content {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.alert-content i {
    font-size: 1.25rem;
    color: #10b981;
}

.alert-close {
    margin-left: auto;
    background: none;
    border: none;
    color: #6b7280;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 0.25rem;
    transition: all 0.2s ease;
}

.alert-close:hover {
    background: rgba(107, 114, 128, 0.1);
    color: #374151;
}

/* Users Container */
.users-container {
    position: relative;
    z-index: 5;
    padding: 1.5rem 2rem 2rem 2rem;
}

.users-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 1.5rem;
    max-width: 1400px;
    margin: 0 auto;
}

/* User Cards */
.user-card {
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(226, 232, 240, 0.8);
    border-radius: 1rem;
    padding: 1.5rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.user-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    border-color: rgba(59, 130, 246, 0.3);
}

.user-card-header {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    margin-bottom: 1rem;
}

.user-avatar {
    width: 3.5rem;
    height: 3.5rem;
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.user-avatar i {
    color: white;
    font-size: 1.75rem;
}

.user-status {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 1rem;
    height: 1rem;
    border-radius: 50%;
    border: 2px solid white;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.user-status.admin {
    background: #ef4444;
}

.user-status.tutor {
    background: #10b981;
}

.user-status.parent {
    background: #3b82f6;
}

.user-role-badge {
    margin-top: 0.25rem;
}

.role-badge {
    padding: 0.375rem 0.75rem;
    border-radius: 0.5rem;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.role-badge.admin {
    background: rgba(239, 68, 68, 0.1);
    color: #dc2626;
    border: 1px solid rgba(239, 68, 68, 0.2);
}

.role-badge.tutor {
    background: rgba(16, 185, 129, 0.1);
    color: #059669;
    border: 1px solid rgba(16, 185, 129, 0.2);
}

.role-badge.parent {
    background: rgba(59, 130, 246, 0.1);
    color: #2563eb;
    border: 1px solid rgba(59, 130, 246, 0.2);
}

.user-card-body {
    margin-bottom: 1.5rem;
}

.user-name {
    font-size: 1.25rem;
    font-weight: 700;
    color: #1e293b;
    margin: 0 0 0.5rem 0;
}

.user-email {
    color: #64748b;
    font-size: 0.875rem;
    margin: 0 0 1rem 0;
    word-break: break-word;
}

.user-meta {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.8rem;
    color: #6b7280;
}

.meta-item i {
    color: #9ca3af;
    font-size: 0.875rem;
}

.user-card-actions {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
    padding-top: 1rem;
    border-top: 1px solid rgba(226, 232, 240, 0.6);
}

.card-action-btn {
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 0.5rem;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    font-size: 1rem;
}

.card-action-btn.view {
    background: rgba(59, 130, 246, 0.1);
    color: #2563eb;
    border: 1px solid rgba(59, 130, 246, 0.2);
}

.card-action-btn.view:hover {
    background: rgba(59, 130, 246, 0.2);
    color: #1d4ed8;
    transform: scale(1.1);
}

.card-action-btn.edit {
    background: rgba(245, 158, 11, 0.1);
    color: #d97706;
    border: 1px solid rgba(245, 158, 11, 0.2);
}

.card-action-btn.edit:hover {
    background: rgba(245, 158, 11, 0.2);
    color: #b45309;
    transform: scale(1.1);
}

.card-action-btn.delete {
    background: rgba(239, 68, 68, 0.1);
    color: #dc2626;
    border: 1px solid rgba(239, 68, 68, 0.2);
}

.card-action-btn.delete:hover {
    background: rgba(239, 68, 68, 0.2);
    color: #b91c1c;
    transform: scale(1.1);
}

/* Empty State */
.empty-state {
    grid-column: 1 / -1;
    text-align: center;
    padding: 4rem 2rem;
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(20px);
    border: 2px dashed rgba(226, 232, 240, 0.8);
    border-radius: 1rem;
    margin: 2rem 0;
}

.empty-icon {
    width: 5rem;
    height: 5rem;
    background: rgba(107, 114, 128, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem auto;
}

.empty-icon i {
    font-size: 2.5rem;
    color: #9ca3af;
}

.empty-state h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: #374151;
    margin: 0 0 0.5rem 0;
}

.empty-state p {
    color: #6b7280;
    margin: 0 0 2rem 0;
    font-size: 1rem;
}

.empty-action-btn {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 0.75rem;
    text-decoration: none;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.2s ease;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.empty-action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
    color: white;
    text-decoration: none;
}

/* Animations */
@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .modern-header {
        padding: 1rem;
        margin: 0.5rem;
    }

    .header-container {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }

    .header-actions {
        width: 100%;
        justify-content: space-between;
    }

    .users-container {
        padding: 1rem;
    }

    .users-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .page-title {
        font-size: 1.5rem;
    }

    .action-btn span {
        display: none;
    }
}
</style>
@endpush

@push('scripts')
<script>
    $(document).ready(function() {
        // Add any modern interactions here
        console.log('Modern User Dashboard Loaded');

        // Export functionality
        $('#export-btn').click(function() {
            // Add export logic here
            console.log('Export clicked');
        });

        // Filter functionality
        $('#filter-btn').click(function() {
            // Add filter logic here
            console.log('Filter clicked');
        });
    });
</script>
@endpush

@endsection