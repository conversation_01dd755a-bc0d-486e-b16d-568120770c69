<!DOCTYPE html>
<html lang="en" class="light-style" dir="ltr" data-theme="theme-default">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0" />
  <title><?php echo $__env->yieldContent('title', 'Dashboard'); ?></title>
  <!-- Favicon -->
  <link rel="icon" type="image/png" href="<?php echo e(asset('zoffnesscollegeprep-logo.png'), false); ?>">
  <!-- Fonts -->
  <link rel="preconnect" href="https://fonts.googleapis.com" />
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
  <link href="https://fonts.googleapis.com/css2?family=Public+Sans:ital,wght@0,300;0,400;0,500;0,600;0,700;1,300;1,400;1,500;1,600;1,700&display=swap" rel="stylesheet" />
  <!-- Icons -->
  <link href='https://unpkg.com/boxicons@2.1.4/css/boxicons.min.css' rel='stylesheet'>
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
  <!-- Core CSS -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="<?php echo e(asset('css/dashboard-styles.css'), false); ?>" />
  <link rel="stylesheet" href="<?php echo e(asset('css/navbar-variables.css'), false); ?>" />
  <link rel="stylesheet" href="<?php echo e(asset('css/navbar-styles.css'), false); ?>" />
  <link rel="stylesheet" href="<?php echo e(asset('css/navbar-utilities.css'), false); ?>" />
  <!-- DataTables CSS -->
  <link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css">
  <link rel="stylesheet" href="https://cdn.datatables.net/responsive/2.4.1/css/responsive.bootstrap5.min.css">
  <link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.2.2/css/buttons.bootstrap5.min.css">
  <link rel="stylesheet" href="<?php echo e(asset('css/custom-datatables.css'), false); ?>">
  <link rel="stylesheet" href="<?php echo e(asset('css/action-buttons.css'), false); ?>">
  <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
  <?php echo $__env->yieldPushContent('styles'); ?>
  <style>
    /* Clean Dashboard Layout - No Sidebar */
    body {
      margin: 0;
      padding: 0;
      background: #f5f5f9;
      font-family: 'Public Sans', sans-serif;
      overflow-x: hidden;
    }

    /* Full width layout */
    .layout-wrapper {
      width: 100%;
      min-height: 100vh;
      display: flex;
      flex-direction: column;
    }

    .layout-container {
      width: 100%;
      flex: 1;
      display: flex;
      flex-direction: column;
    }

    .content-wrapper {
      flex: 1;
      width: 100%;
      padding: 0;
      margin: 0;
      background: transparent;
    }

    .container-xxl {
      width: 100%;
      max-width: none;
      padding: 0;
      margin: 0;
    }

    .container-p-y {
      padding: 0 !important;
    }

    /* Navigation Menu for Clean Dashboard */
    .clean-nav {
      position: fixed;
      top: 20px;
      right: 20px;
      z-index: 1000;
      display: flex;
      gap: 0.5rem;
      align-items: center;
    }

    .nav-menu-btn {
      width: 44px;
      height: 44px;
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(20px);
      border: 1px solid rgba(37, 99, 235, 0.1);
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.3s ease;
      color: #64748b;
      font-size: 18px;
      text-decoration: none;
      box-shadow: 0 4px 20px rgba(37, 99, 235, 0.08);
    }

    .nav-menu-btn:hover {
      background: rgba(37, 99, 235, 0.1);
      color: #2563eb;
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(37, 99, 235, 0.15);
      border-color: rgba(37, 99, 235, 0.2);
    }

    /* Dropdown Menu */
    .nav-dropdown {
      position: relative;
      display: inline-block;
    }

    .nav-dropdown-content {
      display: none;
      position: absolute;
      right: 0;
      top: 100%;
      margin-top: 0.5rem;
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(25px);
      border: 1px solid rgba(37, 99, 235, 0.1);
      border-radius: 12px;
      box-shadow: 0 8px 32px rgba(37, 99, 235, 0.12);
      min-width: 200px;
      z-index: 1001;
      padding: 0.5rem 0;
    }

    .nav-dropdown:hover .nav-dropdown-content {
      display: block;
    }

    .nav-dropdown-item {
      display: block;
      padding: 0.75rem 1rem;
      color: #64748b;
      text-decoration: none;
      font-size: 0.875rem;
      font-weight: 500;
      transition: all 0.2s ease;
      border: none;
      background: none;
      width: 100%;
      text-align: left;
    }

    .nav-dropdown-item:hover {
      background: rgba(37, 99, 235, 0.08);
      color: #2563eb;
    }

    .nav-dropdown-item i {
      margin-right: 0.5rem;
      width: 16px;
    }

    /* Logo in top left */
    .clean-logo {
      position: fixed;
      top: 20px;
      left: 20px;
      z-index: 1000;
      display: flex;
      align-items: center;
      gap: 0.75rem;
      padding: 0.75rem 1rem;
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(20px);
      border: 1px solid rgba(37, 99, 235, 0.1);
      border-radius: 12px;
      box-shadow: 0 4px 20px rgba(37, 99, 235, 0.08);
      text-decoration: none;
      color: #1e293b;
      font-weight: 600;
      font-size: 1rem;
      transition: all 0.3s ease;
    }

    .clean-logo:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(37, 99, 235, 0.15);
      color: #2563eb;
      border-color: rgba(37, 99, 235, 0.2);
    }

    .clean-logo img {
      height: 32px;
      width: auto;
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
      .clean-nav {
        top: 15px;
        right: 15px;
        gap: 0.25rem;
      }

      .clean-logo {
        top: 15px;
        left: 15px;
        padding: 0.5rem 0.75rem;
        font-size: 0.875rem;
      }

      .clean-logo img {
        height: 28px;
      }

      .nav-menu-btn {
        width: 40px;
        height: 40px;
        font-size: 16px;
      }

      .nav-dropdown-content {
        min-width: 180px;
      }
    }
  </style>
</head>
<body>
  <!-- Clean Logo -->
  <a href="<?php echo e(route('dashboard'), false); ?>" class="clean-logo">
    <img src="/zoffnesscollegeprep-logo.png" alt="Zoffness College Prep">
    <span>Zoffness College Prep</span>
  </a>

  <!-- Clean Navigation -->
  <div class="clean-nav">
    <div class="nav-dropdown">
      <button class="nav-menu-btn">
        <i class="bx bx-menu"></i>
      </button>
      <div class="nav-dropdown-content">
        <a href="<?php echo e(route('dashboard'), false); ?>" class="nav-dropdown-item">
          <i class="bx bx-home-circle"></i>
          Dashboard
        </a>
        <a href="<?php echo e(route('users'), false); ?>" class="nav-dropdown-item">
          <i class="bx bx-user"></i>
          Users
        </a>
        <a href="<?php echo e(url('tutors'), false); ?>" class="nav-dropdown-item">
          <i class="bx bx-user-voice"></i>
          Tutors
        </a>
        <a href="<?php echo e(url('student'), false); ?>" class="nav-dropdown-item">
          <i class="bx bx-user-pin"></i>
          Students
        </a>
        <a href="<?php echo e(url('session'), false); ?>" class="nav-dropdown-item">
          <i class="bx bx-calendar-event"></i>
          Sessions
        </a>
        <a href="<?php echo e(url('sat_act_packages'), false); ?>" class="nav-dropdown-item">
          <i class="bx bx-package"></i>
          SAT ACT Packages
        </a>
        <a href="<?php echo e(url('package'), false); ?>" class="nav-dropdown-item">
          <i class="bx bx-box"></i>
          Packages
        </a>
        <a href="<?php echo e(url('essay_packages'), false); ?>" class="nav-dropdown-item">
          <i class="bx bx-edit-alt"></i>
          Essay Packages
        </a>
        <a href="<?php echo e(url('executive_packages'), false); ?>" class="nav-dropdown-item">
          <i class="bx bx-briefcase"></i>
          Executive Packages
        </a>
        <a href="<?php echo e(url('rankings'), false); ?>" class="nav-dropdown-item">
          <i class="bx bx-trophy"></i>
          Rankings
        </a>
        <hr style="margin: 0.5rem 0; border: none; border-top: 1px solid rgba(37, 99, 235, 0.1);">
        <form method="POST" action="<?php echo e(route('logout'), false); ?>" style="margin: 0;">
          <?php echo csrf_field(); ?>
          <button type="submit" class="nav-dropdown-item">
            <i class="bx bx-log-out"></i>
            Logout
          </button>
        </form>
      </div>
    </div>
  </div>

  <!-- Layout wrapper -->
  <div class="layout-wrapper">
    <div class="layout-container">
      <!-- Content wrapper -->
      <div class="content-wrapper">
        <div class="container-xxl container-p-y">
          <?php echo $__env->yieldContent('content'); ?>
        </div>
      </div>
    </div>
  </div>

  <!-- Core JS -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
  <!-- DataTables JS -->
  <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
  <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
  <script src="https://cdn.datatables.net/responsive/2.4.1/js/dataTables.responsive.min.js"></script>
  <script src="https://cdn.datatables.net/responsive/2.4.1/js/responsive.bootstrap5.min.js"></script>
  <script src="https://cdn.datatables.net/buttons/2.2.2/js/dataTables.buttons.min.js"></script>
  <script src="https://cdn.datatables.net/buttons/2.2.2/js/buttons.bootstrap5.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.1.3/jszip.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
  <script src="https://cdn.datatables.net/buttons/2.2.2/js/buttons.html5.min.js"></script>
  <script src="https://cdn.datatables.net/buttons/2.2.2/js/buttons.print.min.js"></script>
  <?php echo $__env->yieldPushContent('scripts'); ?>
</body>
</html>
<?php /**PATH C:\xampp\htdocs\Zoffness_backend\resources\views/layouts/dashboard-clean.blade.php ENDPATH**/ ?>