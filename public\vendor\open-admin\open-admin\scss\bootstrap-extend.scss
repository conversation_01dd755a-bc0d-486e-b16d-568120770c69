.callout {
    padding: 20px;
    margin: 20px 0;
    border: 1px solid #eee;
    border-left-width: 5px;
    border-radius: 3px;

    h4 {
        margin-top: 0;
        margin-bottom: 5px;
    }

    p:last-child {
        margin-bottom: 0;
    }

    code {
        border-radius: 3px;
    }

    & + .bs-callout {
        margin-top: -5px;
    }
}
@each $name,$color in $theme-colors{
    .callout-#{$name} {
        border-left-color: $color;

        h4 {
            color: $color;
        }
    }
}

.text-dotted{
    text-decoration-style:dotted;
}