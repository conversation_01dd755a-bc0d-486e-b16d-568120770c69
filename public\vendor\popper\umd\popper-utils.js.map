{"version": 3, "file": "popper-utils.js", "sources": ["../../src/utils/getStyleComputedProperty.js", "../../src/utils/getParentNode.js", "../../src/utils/getScrollParent.js", "../../src/utils/getReferenceNode.js", "../../src/utils/isBrowser.js", "../../src/utils/isIE.js", "../../src/utils/getOffsetParent.js", "../../src/utils/isOffsetContainer.js", "../../src/utils/getRoot.js", "../../src/utils/findCommonOffsetParent.js", "../../src/utils/getScroll.js", "../../src/utils/includeScroll.js", "../../src/utils/getBordersSize.js", "../../src/utils/getWindowSizes.js", "../../src/utils/getClientRect.js", "../../src/utils/getBoundingClientRect.js", "../../src/utils/getOffsetRectRelativeToArbitraryNode.js", "../../src/utils/getViewportOffsetRectRelativeToArtbitraryNode.js", "../../src/utils/isFixed.js", "../../src/utils/getFixedPositionOffsetParent.js", "../../src/utils/getBoundaries.js", "../../src/utils/computeAutoPlacement.js", "../../src/utils/debounce.js", "../../src/utils/find.js", "../../src/utils/findIndex.js", "../../src/utils/getOffsetRect.js", "../../src/utils/getOuterSizes.js", "../../src/utils/getOppositePlacement.js", "../../src/utils/getPopperOffsets.js", "../../src/utils/getReferenceOffsets.js", "../../src/utils/getSupportedPropertyName.js", "../../src/utils/isFunction.js", "../../src/utils/isModifierEnabled.js", "../../src/utils/isModifierRequired.js", "../../src/utils/isNumeric.js", "../../src/utils/getWindow.js", "../../src/utils/removeEventListeners.js", "../../src/utils/runModifiers.js", "../../src/utils/setAttributes.js", "../../src/utils/setStyles.js", "../../src/utils/setupEventListeners.js", "../../src/utils/index.js"], "sourcesContent": ["/**\n * Get CSS computed property of the given element\n * @method\n * @memberof Popper.Utils\n * @argument {Eement} element\n * @argument {String} property\n */\nexport default function getStyleComputedProperty(element, property) {\n  if (element.nodeType !== 1) {\n    return [];\n  }\n  // NOTE: 1 DOM access here\n  const window = element.ownerDocument.defaultView;\n  const css = window.getComputedStyle(element, null);\n  return property ? css[property] : css;\n}\n", "/**\n * Returns the parentNode or the host of the element\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Element} parent\n */\nexport default function getParentNode(element) {\n  if (element.nodeName === 'HTML') {\n    return element;\n  }\n  return element.parentNode || element.host;\n}\n", "import getStyleComputedProperty from './getStyleComputedProperty';\nimport getParentNode from './getParentNode';\n\n/**\n * Returns the scrolling parent of the given element\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Element} scroll parent\n */\nexport default function getScrollParent(element) {\n  // Return body, `getScroll` will take care to get the correct `scrollTop` from it\n  if (!element) {\n    return document.body\n  }\n\n  switch (element.nodeName) {\n    case 'HTML':\n    case 'BODY':\n      return element.ownerDocument.body\n    case '#document':\n      return element.body\n  }\n\n  // Firefox want us to check `-x` and `-y` variations as well\n  const { overflow, overflowX, overflowY } = getStyleComputedProperty(element);\n  if (/(auto|scroll|overlay)/.test(overflow + overflowY + overflowX)) {\n    return element;\n  }\n\n  return getScrollParent(getParentNode(element));\n}\n", "/**\n * Returns the reference node of the reference object, or the reference object itself.\n * @method\n * @memberof Popper.Utils\n * @param {Element|Object} reference - the reference element (the popper will be relative to this)\n * @returns {Element} parent\n */\nexport default function getReferenceNode(reference) {\n  return reference && reference.referenceNode ? reference.referenceNode : reference;\n}\n", "export default typeof window !== 'undefined' && typeof document !== 'undefined' && typeof navigator !== 'undefined';\n", "import isBrowser from './isBrowser';\n\nconst isIE11 = isBrowser && !!(window.MSInputMethodContext && document.documentMode);\nconst isIE10 = isBrowser && /MSIE 10/.test(navigator.userAgent);\n\n/**\n * Determines if the browser is Internet Explorer\n * @method\n * @memberof Popper.Utils\n * @param {Number} version to check\n * @returns {Boolean} isIE\n */\nexport default function isIE(version) {\n  if (version === 11) {\n    return isIE11;\n  }\n  if (version === 10) {\n    return isIE10;\n  }\n  return isIE11 || isIE10;\n}\n", "import getStyleComputedProperty from './getStyleComputedProperty';\nimport isIE from './isIE';\n/**\n * Returns the offset parent of the given element\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Element} offset parent\n */\nexport default function getOffsetParent(element) {\n  if (!element) {\n    return document.documentElement;\n  }\n\n  const noOffsetParent = isIE(10) ? document.body : null;\n\n  // NOTE: 1 DOM access here\n  let offsetParent = element.offsetParent || null;\n  // Skip hidden elements which don't have an offsetParent\n  while (offsetParent === noOffsetParent && element.nextElementSibling) {\n    offsetParent = (element = element.nextElementSibling).offsetParent;\n  }\n\n  const nodeName = offsetParent && offsetParent.nodeName;\n\n  if (!nodeName || nodeName === 'BODY' || nodeName === 'HTML') {\n    return element ? element.ownerDocument.documentElement : document.documentElement;\n  }\n\n  // .offsetParent will return the closest TH, TD or TABLE in case\n  // no offsetParent is present, I hate this job...\n  if (\n    ['TH', 'TD', 'TABLE'].indexOf(offsetParent.nodeName) !== -1 &&\n    getStyleComputedProperty(offsetParent, 'position') === 'static'\n  ) {\n    return getOffsetParent(offsetParent);\n  }\n\n  return offsetParent;\n}\n", "import getOffsetParent from './getOffsetParent';\n\nexport default function isOffsetContainer(element) {\n  const { nodeName } = element;\n  if (nodeName === 'BODY') {\n    return false;\n  }\n  return (\n    nodeName === 'HTML' || getOffsetParent(element.firstElementChild) === element\n  );\n}\n", "/**\n * Finds the root node (document, shadowDOM root) of the given element\n * @method\n * @memberof Popper.Utils\n * @argument {Element} node\n * @returns {Element} root node\n */\nexport default function getRoot(node) {\n  if (node.parentNode !== null) {\n    return getRoot(node.parentNode);\n  }\n\n  return node;\n}\n", "import isOffsetContainer from './isOffsetContainer';\nimport getRoot from './getRoot';\nimport getOffsetParent from './getOffsetParent';\n\n/**\n * Finds the offset parent common to the two provided nodes\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element1\n * @argument {Element} element2\n * @returns {Element} common offset parent\n */\nexport default function findCommonOffsetParent(element1, element2) {\n  // This check is needed to avoid errors in case one of the elements isn't defined for any reason\n  if (!element1 || !element1.nodeType || !element2 || !element2.nodeType) {\n    return document.documentElement;\n  }\n\n  // Here we make sure to give as \"start\" the element that comes first in the DOM\n  const order =\n    element1.compareDocumentPosition(element2) &\n    Node.DOCUMENT_POSITION_FOLLOWING;\n  const start = order ? element1 : element2;\n  const end = order ? element2 : element1;\n\n  // Get common ancestor container\n  const range = document.createRange();\n  range.setStart(start, 0);\n  range.setEnd(end, 0);\n  const { commonAncestorContainer } = range;\n\n  // Both nodes are inside #document\n  if (\n    (element1 !== commonAncestorContainer &&\n      element2 !== commonAncestorContainer) ||\n    start.contains(end)\n  ) {\n    if (isOffsetContainer(commonAncestorContainer)) {\n      return commonAncestorContainer;\n    }\n\n    return getOffsetParent(commonAncestorContainer);\n  }\n\n  // one of the nodes is inside shadowDOM, find which one\n  const element1root = getRoot(element1);\n  if (element1root.host) {\n    return findCommonOffsetParent(element1root.host, element2);\n  } else {\n    return findCommonOffsetParent(element1, getRoot(element2).host);\n  }\n}\n", "/**\n * Gets the scroll value of the given element in the given side (top and left)\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @argument {String} side `top` or `left`\n * @returns {number} amount of scrolled pixels\n */\nexport default function getScroll(element, side = 'top') {\n  const upperSide = side === 'top' ? 'scrollTop' : 'scrollLeft';\n  const nodeName = element.nodeName;\n\n  if (nodeName === 'BODY' || nodeName === 'HTML') {\n    const html = element.ownerDocument.documentElement;\n    const scrollingElement = element.ownerDocument.scrollingElement || html;\n    return scrollingElement[upperSide];\n  }\n\n  return element[upperSide];\n}\n", "import getScroll from './getScroll';\n\n/*\n * Sum or subtract the element scroll values (left and top) from a given rect object\n * @method\n * @memberof Popper.Utils\n * @param {Object} rect - Rect object you want to change\n * @param {HTMLElement} element - The element from the function reads the scroll values\n * @param {Boolean} subtract - set to true if you want to subtract the scroll values\n * @return {Object} rect - The modifier rect object\n */\nexport default function includeScroll(rect, element, subtract = false) {\n  const scrollTop = getScroll(element, 'top');\n  const scrollLeft = getScroll(element, 'left');\n  const modifier = subtract ? -1 : 1;\n  rect.top += scrollTop * modifier;\n  rect.bottom += scrollTop * modifier;\n  rect.left += scrollLeft * modifier;\n  rect.right += scrollLeft * modifier;\n  return rect;\n}\n", "/*\n * Helper to detect borders of a given element\n * @method\n * @memberof Popper.Utils\n * @param {CSSStyleDeclaration} styles\n * Result of `getStyleComputedProperty` on the given element\n * @param {String} axis - `x` or `y`\n * @return {number} borders - The borders size of the given axis\n */\n\nexport default function getBordersSize(styles, axis) {\n  const sideA = axis === 'x' ? 'Left' : 'Top';\n  const sideB = sideA === 'Left' ? 'Right' : 'Bottom';\n\n  return (\n    parseFloat(styles[`border${sideA}Width`]) +\n    parseFloat(styles[`border${sideB}Width`])\n  );\n}\n", "import isIE from './isIE';\n\nfunction getSize(axis, body, html, computedStyle) {\n  return Math.max(\n    body[`offset${axis}`],\n    body[`scroll${axis}`],\n    html[`client${axis}`],\n    html[`offset${axis}`],\n    html[`scroll${axis}`],\n    isIE(10)\n      ? (parseInt(html[`offset${axis}`]) + \n      parseInt(computedStyle[`margin${axis === 'Height' ? 'Top' : 'Left'}`]) + \n      parseInt(computedStyle[`margin${axis === 'Height' ? 'Bottom' : 'Right'}`]))\n    : 0 \n  );\n}\n\nexport default function getWindowSizes(document) {\n  const body = document.body;\n  const html = document.documentElement;\n  const computedStyle = isIE(10) && getComputedStyle(html);\n\n  return {\n    height: getSize('Height', body, html, computedStyle),\n    width: getSize('Width', body, html, computedStyle),\n  };\n}\n", "/**\n * Given element offsets, generate an output similar to getBoundingClientRect\n * @method\n * @memberof Popper.Utils\n * @argument {Object} offsets\n * @returns {Object} ClientRect like output\n */\nexport default function getClientRect(offsets) {\n  return {\n    ...offsets,\n    right: offsets.left + offsets.width,\n    bottom: offsets.top + offsets.height,\n  };\n}\n", "import getStyleComputedProperty from './getStyleComputedProperty';\nimport getBordersSize from './getBordersSize';\nimport getWindowSizes from './getWindowSizes';\nimport getScroll from './getScroll';\nimport getClientRect from './getClientRect';\nimport isIE from './isIE';\n\n/**\n * Get bounding client rect of given element\n * @method\n * @memberof Popper.Utils\n * @param {HTMLElement} element\n * @return {Object} client rect\n */\nexport default function getBoundingClientRect(element) {\n  let rect = {};\n\n  // IE10 10 FIX: Please, don't ask, the element isn't\n  // considered in DOM in some circumstances...\n  // This isn't reproducible in IE10 compatibility mode of IE11\n  try {\n    if (isIE(10)) {\n      rect = element.getBoundingClientRect();\n      const scrollTop = getScroll(element, 'top');\n      const scrollLeft = getScroll(element, 'left');\n      rect.top += scrollTop;\n      rect.left += scrollLeft;\n      rect.bottom += scrollTop;\n      rect.right += scrollLeft;\n    }\n    else {\n      rect = element.getBoundingClientRect();\n    }\n  }\n  catch(e){}\n\n  const result = {\n    left: rect.left,\n    top: rect.top,\n    width: rect.right - rect.left,\n    height: rect.bottom - rect.top,\n  };\n\n  // subtract scrollbar size from sizes\n  const sizes = element.nodeName === 'HTML' ? getWindowSizes(element.ownerDocument) : {};\n  const width =\n    sizes.width || element.clientWidth || result.width;\n  const height =\n    sizes.height || element.clientHeight || result.height;\n\n  let horizScrollbar = element.offsetWidth - width;\n  let vertScrollbar = element.offsetHeight - height;\n\n  // if an hypothetical scrollbar is detected, we must be sure it's not a `border`\n  // we make this check conditional for performance reasons\n  if (horizScrollbar || vertScrollbar) {\n    const styles = getStyleComputedProperty(element);\n    horizScrollbar -= getBordersSize(styles, 'x');\n    vertScrollbar -= getBordersSize(styles, 'y');\n\n    result.width -= horizScrollbar;\n    result.height -= vertScrollbar;\n  }\n\n  return getClientRect(result);\n}\n", "import getStyleComputedProperty from './getStyleComputedProperty';\nimport includeScroll from './includeScroll';\nimport getScrollParent from './getScrollParent';\nimport getBoundingClientRect from './getBoundingClientRect';\nimport runIsIE from './isIE';\nimport getClientRect from './getClientRect';\n\nexport default function getOffsetRectRelativeToArbitraryNode(children, parent, fixedPosition = false) {\n  const isIE10 = runIsIE(10);\n  const isHTML = parent.nodeName === 'HTML';\n  const childrenRect = getBoundingClientRect(children);\n  const parentRect = getBoundingClientRect(parent);\n  const scrollParent = getScrollParent(children);\n\n  const styles = getStyleComputedProperty(parent);\n  const borderTopWidth = parseFloat(styles.borderTopWidth);\n  const borderLeftWidth = parseFloat(styles.borderLeftWidth);\n\n  // In cases where the parent is fixed, we must ignore negative scroll in offset calc\n  if(fixedPosition && isHTML) {\n    parentRect.top = Math.max(parentRect.top, 0);\n    parentRect.left = Math.max(parentRect.left, 0);\n  }\n  let offsets = getClientRect({\n    top: childrenRect.top - parentRect.top - borderTopWidth,\n    left: childrenRect.left - parentRect.left - borderLeftWidth,\n    width: childrenRect.width,\n    height: childrenRect.height,\n  });\n  offsets.marginTop = 0;\n  offsets.marginLeft = 0;\n\n  // Subtract margins of documentElement in case it's being used as parent\n  // we do this only on HTML because it's the only element that behaves\n  // differently when margins are applied to it. The margins are included in\n  // the box of the documentElement, in the other cases not.\n  if (!isIE10 && isHTML) {\n    const marginTop = parseFloat(styles.marginTop);\n    const marginLeft = parseFloat(styles.marginLeft);\n\n    offsets.top -= borderTopWidth - marginTop;\n    offsets.bottom -= borderTopWidth - marginTop;\n    offsets.left -= borderLeftWidth - marginLeft;\n    offsets.right -= borderLeftWidth - marginLeft;\n\n    // Attach marginTop and marginLeft because in some circumstances we may need them\n    offsets.marginTop = marginTop;\n    offsets.marginLeft = marginLeft;\n  }\n\n  if (\n    isIE10 && !fixedPosition\n      ? parent.contains(scrollParent)\n      : parent === scrollParent && scrollParent.nodeName !== 'BODY'\n  ) {\n    offsets = includeScroll(offsets, parent);\n  }\n\n  return offsets;\n}\n", "import getOffsetRectRelativeToArbitraryNode from './getOffsetRectRelativeToArbitraryNode';\nimport getScroll from './getScroll';\nimport getClientRect from './getClientRect';\n\nexport default function getViewportOffsetRectRelativeToArtbitraryNode(element, excludeScroll = false) {\n  const html = element.ownerDocument.documentElement;\n  const relativeOffset = getOffsetRectRelativeToArbitraryNode(element, html);\n  const width = Math.max(html.clientWidth, window.innerWidth || 0);\n  const height = Math.max(html.clientHeight, window.innerHeight || 0);\n\n  const scrollTop = !excludeScroll ? getScroll(html) : 0;\n  const scrollLeft = !excludeScroll ? getScroll(html, 'left') : 0;\n\n  const offset = {\n    top: scrollTop - relativeOffset.top + relativeOffset.marginTop,\n    left: scrollLeft - relativeOffset.left + relativeOffset.marginLeft,\n    width,\n    height,\n  };\n\n  return getClientRect(offset);\n}\n", "import getStyleComputedProperty from './getStyleComputedProperty';\nimport getParentNode from './getParentNode';\n\n/**\n * Check if the given element is fixed or is inside a fixed parent\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @argument {Element} customContainer\n * @returns {<PERSON><PERSON><PERSON>} answer to \"isFixed?\"\n */\nexport default function isFixed(element) {\n  const nodeName = element.nodeName;\n  if (nodeName === 'BODY' || nodeName === 'HTML') {\n    return false;\n  }\n  if (getStyleComputedProperty(element, 'position') === 'fixed') {\n    return true;\n  }\n  const parentNode = getParentNode(element);\n  if (!parentNode) {\n    return false;\n  }\n  return isFixed(parentNode);\n}\n", "import getStyleComputedProperty from './getStyleComputedProperty';\nimport isIE from './isIE';\n/**\n * Finds the first parent of an element that has a transformed property defined\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Element} first transformed parent or documentElement\n */\n\nexport default function getFixedPositionOffsetParent(element) {\n  // This check is needed to avoid errors in case one of the elements isn't defined for any reason\n   if (!element || !element.parentElement || isIE()) {\n    return document.documentElement;\n  }\n  let el = element.parentElement;\n  while (el && getStyleComputedProperty(el, 'transform') === 'none') {\n    el = el.parentElement;\n  }\n  return el || document.documentElement;\n\n}\n", "import getScrollParent from './getScrollParent';\nimport getParentNode from './getParentNode';\nimport getReferenceNode from './getReferenceNode';\nimport findCommonOffsetParent from './findCommonOffsetParent';\nimport getOffsetRectRelativeToArbitraryNode from './getOffsetRectRelativeToArbitraryNode';\nimport getViewportOffsetRectRelativeToArtbitraryNode from './getViewportOffsetRectRelativeToArtbitraryNode';\nimport getWindowSizes from './getWindowSizes';\nimport isFixed from './isFixed';\nimport getFixedPositionOffsetParent from './getFixedPositionOffsetParent';\n\n/**\n * Computed the boundaries limits and return them\n * @method\n * @memberof Popper.Utils\n * @param {HTMLElement} popper\n * @param {HTMLElement} reference\n * @param {number} padding\n * @param {HTMLElement} boundariesElement - Element used to define the boundaries\n * @param {Boolean} fixedPosition - Is in fixed position mode\n * @returns {Object} Coordinates of the boundaries\n */\nexport default function getBoundaries(\n  popper,\n  reference,\n  padding,\n  boundariesElement,\n  fixedPosition = false\n) {\n  // NOTE: 1 DOM access here\n\n  let boundaries = { top: 0, left: 0 };\n  const offsetParent = fixedPosition ? getFixedPositionOffsetParent(popper) : findCommonOffsetParent(popper, getReferenceNode(reference));\n\n  // Handle viewport case\n  if (boundariesElement === 'viewport' ) {\n    boundaries = getViewportOffsetRectRelativeToArtbitraryNode(offsetParent, fixedPosition);\n  }\n\n  else {\n    // Handle other cases based on DOM element used as boundaries\n    let boundariesNode;\n    if (boundariesElement === 'scrollParent') {\n      boundariesNode = getScrollParent(getParentNode(reference));\n      if (boundariesNode.nodeName === 'BODY') {\n        boundariesNode = popper.ownerDocument.documentElement;\n      }\n    } else if (boundariesElement === 'window') {\n      boundariesNode = popper.ownerDocument.documentElement;\n    } else {\n      boundariesNode = boundariesElement;\n    }\n\n    const offsets = getOffsetRectRelativeToArbitraryNode(\n      boundariesNode,\n      offsetParent,\n      fixedPosition\n    );\n\n    // In case of HTML, we need a different computation\n    if (boundariesNode.nodeName === 'HTML' && !isFixed(offsetParent)) {\n      const { height, width } = getWindowSizes(popper.ownerDocument);\n      boundaries.top += offsets.top - offsets.marginTop;\n      boundaries.bottom = height + offsets.top;\n      boundaries.left += offsets.left - offsets.marginLeft;\n      boundaries.right = width + offsets.left;\n    } else {\n      // for all the other DOM elements, this one is good\n      boundaries = offsets;\n    }\n  }\n\n  // Add paddings\n  padding = padding || 0;\n  const isPaddingNumber = typeof padding === 'number';\n  boundaries.left += isPaddingNumber ? padding : padding.left || 0; \n  boundaries.top += isPaddingNumber ? padding : padding.top || 0; \n  boundaries.right -= isPaddingNumber ? padding : padding.right || 0; \n  boundaries.bottom -= isPaddingNumber ? padding : padding.bottom || 0; \n\n  return boundaries;\n}\n", "import getBoundaries from '../utils/getBoundaries';\n\nfunction getArea({ width, height }) {\n  return width * height;\n}\n\n/**\n * Utility used to transform the `auto` placement to the placement with more\n * available space.\n * @method\n * @memberof Popper.Utils\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nexport default function computeAutoPlacement(\n  placement,\n  refRect,\n  popper,\n  reference,\n  boundariesElement,\n  padding = 0\n) {\n  if (placement.indexOf('auto') === -1) {\n    return placement;\n  }\n\n  const boundaries = getBoundaries(\n    popper,\n    reference,\n    padding,\n    boundariesElement\n  );\n\n  const rects = {\n    top: {\n      width: boundaries.width,\n      height: refRect.top - boundaries.top,\n    },\n    right: {\n      width: boundaries.right - refRect.right,\n      height: boundaries.height,\n    },\n    bottom: {\n      width: boundaries.width,\n      height: boundaries.bottom - refRect.bottom,\n    },\n    left: {\n      width: refRect.left - boundaries.left,\n      height: boundaries.height,\n    },\n  };\n\n  const sortedAreas = Object.keys(rects)\n    .map(key => ({\n      key,\n      ...rects[key],\n      area: getArea(rects[key]),\n    }))\n    .sort((a, b) => b.area - a.area);\n\n  const filteredAreas = sortedAreas.filter(\n    ({ width, height }) =>\n      width >= popper.clientWidth && height >= popper.clientHeight\n  );\n\n  const computedPlacement = filteredAreas.length > 0\n    ? filteredAreas[0].key\n    : sortedAreas[0].key;\n\n  const variation = placement.split('-')[1];\n\n  return computedPlacement + (variation ? `-${variation}` : '');\n}\n", "import isBrowser from './isBrowser';\n\nconst timeoutDuration = (function(){\n  const longerTimeoutBrowsers = ['Edge', 'Trident', 'Firefox'];\n  for (let i = 0; i < longerTimeoutBrowsers.length; i += 1) {\n    if (isBrowser && navigator.userAgent.indexOf(longerTimeoutBrowsers[i]) >= 0) {\n      return 1;\n    }\n  }\n  return 0;\n}());\n\nexport function microtaskDebounce(fn) {\n  let called = false\n  return () => {\n    if (called) {\n      return\n    }\n    called = true\n    window.Promise.resolve().then(() => {\n      called = false\n      fn()\n    })\n  }\n}\n\nexport function taskDebounce(fn) {\n  let scheduled = false;\n  return () => {\n    if (!scheduled) {\n      scheduled = true;\n      setTimeout(() => {\n        scheduled = false;\n        fn();\n      }, timeoutDuration);\n    }\n  };\n}\n\nconst supportsMicroTasks = isBrowser && window.Promise\n\n\n/**\n* Create a debounced version of a method, that's asynchronously deferred\n* but called in the minimum time possible.\n*\n* @method\n* @memberof Popper.Utils\n* @argument {Function} fn\n* @returns {Function}\n*/\nexport default (supportsMicroTasks\n  ? microtaskDebounce\n  : taskDebounce);\n", "/**\n * Mimics the `find` method of Array\n * @method\n * @memberof Popper.Utils\n * @argument {Array} arr\n * @argument prop\n * @argument value\n * @returns index or -1\n */\nexport default function find(arr, check) {\n  // use native find if supported\n  if (Array.prototype.find) {\n    return arr.find(check);\n  }\n\n  // use `filter` to obtain the same behavior of `find`\n  return arr.filter(check)[0];\n}\n", "import find from './find';\n\n/**\n * Return the index of the matching object\n * @method\n * @memberof Popper.Utils\n * @argument {Array} arr\n * @argument prop\n * @argument value\n * @returns index or -1\n */\nexport default function findIndex(arr, prop, value) {\n  // use native findIndex if supported\n  if (Array.prototype.findIndex) {\n    return arr.findIndex(cur => cur[prop] === value);\n  }\n\n  // use `find` + `indexOf` if `findIndex` isn't supported\n  const match = find(arr, obj => obj[prop] === value);\n  return arr.indexOf(match);\n}\n", "import getWindowSizes from './getWindowSizes';\nimport getClientRect from './getClientRect';\n\n/**\n * Get the position of the given element, relative to its offset parent\n * @method\n * @memberof Popper.Utils\n * @param {Element} element\n * @return {Object} position - Coordinates of the element and its `scrollTop`\n */\nexport default function getOffsetRect(element) {\n  let elementRect;\n  if (element.nodeName === 'HTML') {\n    const { width, height } = getWindowSizes(element.ownerDocument);\n    elementRect = {\n      width,\n      height,\n      left: 0,\n      top: 0,\n    };\n  } else {\n    elementRect = {\n      width: element.offsetWidth,\n      height: element.offsetHeight,\n      left: element.offsetLeft,\n      top: element.offsetTop,\n    };\n  }\n\n  // position\n  return getClientRect(elementRect);\n}\n", "/**\n * Get the outer sizes of the given element (offset size + margins)\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Object} object containing width and height properties\n */\nexport default function getOuterSizes(element) {\n  const window = element.ownerDocument.defaultView;\n  const styles = window.getComputedStyle(element);\n  const x = parseFloat(styles.marginTop || 0) + parseFloat(styles.marginBottom || 0);\n  const y = parseFloat(styles.marginLeft || 0) + parseFloat(styles.marginRight || 0);\n  const result = {\n    width: element.offsetWidth + y,\n    height: element.offsetHeight + x,\n  };\n  return result;\n}\n", "/**\n * Get the opposite placement of the given one\n * @method\n * @memberof Popper.Utils\n * @argument {String} placement\n * @returns {String} flipped placement\n */\nexport default function getOppositePlacement(placement) {\n  const hash = { left: 'right', right: 'left', bottom: 'top', top: 'bottom' };\n  return placement.replace(/left|right|bottom|top/g, matched => hash[matched]);\n}\n", "import getOuterSizes from './getOuterSizes';\nimport getOppositePlacement from './getOppositePlacement';\n\n/**\n * Get offsets to the popper\n * @method\n * @memberof Popper.Utils\n * @param {Object} position - CSS position the Popper will get applied\n * @param {HTMLElement} popper - the popper element\n * @param {Object} referenceOffsets - the reference offsets (the popper will be relative to this)\n * @param {String} placement - one of the valid placement options\n * @returns {Object} popperOffsets - An object containing the offsets which will be applied to the popper\n */\nexport default function getPopperOffsets(popper, referenceOffsets, placement) {\n  placement = placement.split('-')[0];\n\n  // Get popper node sizes\n  const popperRect = getOuterSizes(popper);\n\n  // Add position, width and height to our offsets object\n  const popperOffsets = {\n    width: popperRect.width,\n    height: popperRect.height,\n  };\n\n  // depending by the popper placement we have to compute its offsets slightly differently\n  const isHoriz = ['right', 'left'].indexOf(placement) !== -1;\n  const mainSide = isHoriz ? 'top' : 'left';\n  const secondarySide = isHoriz ? 'left' : 'top';\n  const measurement = isHoriz ? 'height' : 'width';\n  const secondaryMeasurement = !isHoriz ? 'height' : 'width';\n\n  popperOffsets[mainSide] =\n    referenceOffsets[mainSide] +\n    referenceOffsets[measurement] / 2 -\n    popperRect[measurement] / 2;\n  if (placement === secondarySide) {\n    popperOffsets[secondarySide] =\n      referenceOffsets[secondarySide] - popperRect[secondaryMeasurement];\n  } else {\n    popperOffsets[secondarySide] =\n      referenceOffsets[getOppositePlacement(secondarySide)];\n  }\n\n  return popperOffsets;\n}\n", "import findCommonOffsetParent from './findCommonOffsetParent';\nimport getOffsetRectRelativeToArbitraryNode from './getOffsetRectRelativeToArbitraryNode';\nimport getFixedPositionOffsetParent from './getFixedPositionOffsetParent';\nimport getReferenceNode from './getReferenceNode';\n\n/**\n * Get offsets to the reference element\n * @method\n * @memberof Popper.Utils\n * @param {Object} state\n * @param {Element} popper - the popper element\n * @param {Element} reference - the reference element (the popper will be relative to this)\n * @param {Element} fixedPosition - is in fixed position mode\n * @returns {Object} An object containing the offsets which will be applied to the popper\n */\nexport default function getReferenceOffsets(state, popper, reference, fixedPosition = null) {\n  const commonOffsetParent = fixedPosition ? getFixedPositionOffsetParent(popper) : findCommonOffsetParent(popper, getReferenceNode(reference));\n  return getOffsetRectRelativeToArbitraryNode(reference, commonOffsetParent, fixedPosition);\n}\n", "/**\n * Get the prefixed supported property name\n * @method\n * @memberof Popper.Utils\n * @argument {String} property (camelCase)\n * @returns {String} prefixed property (camelCase or PascalCase, depending on the vendor prefix)\n */\nexport default function getSupportedPropertyName(property) {\n  const prefixes = [false, 'ms', 'Webkit', 'Moz', 'O'];\n  const upperProp = property.charAt(0).toUpperCase() + property.slice(1);\n\n  for (let i = 0; i < prefixes.length; i++) {\n    const prefix = prefixes[i];\n    const toCheck = prefix ? `${prefix}${upperProp}` : property;\n    if (typeof document.body.style[toCheck] !== 'undefined') {\n      return toCheck;\n    }\n  }\n  return null;\n}\n", "/**\n * Check if the given variable is a function\n * @method\n * @memberof Popper.Utils\n * @argument {Any} functionToCheck - variable to check\n * @returns {Boolean} answer to: is a function?\n */\nexport default function isFunction(functionToCheck) {\n  const getType = {};\n  return (\n    functionToCheck &&\n    getType.toString.call(functionToCheck) === '[object Function]'\n  );\n}\n", "/**\n * Helper used to know if the given modifier is enabled.\n * @method\n * @memberof Popper.Utils\n * @returns {Boolean}\n */\nexport default function isModifierEnabled(modifiers, modifierName) {\n  return modifiers.some(\n    ({ name, enabled }) => enabled && name === modifierName\n  );\n}\n", "import find from './find';\n\n/**\n * Helper used to know if the given modifier depends from another one.<br />\n * It checks if the needed modifier is listed and enabled.\n * @method\n * @memberof Popper.Utils\n * @param {Array} modifiers - list of modifiers\n * @param {String} requestingName - name of requesting modifier\n * @param {String} requestedName - name of requested modifier\n * @returns {Boolean}\n */\nexport default function isModifierRequired(\n  modifiers,\n  requestingName,\n  requestedName\n) {\n  const requesting = find(modifiers, ({ name }) => name === requestingName);\n\n  const isRequired =\n    !!requesting &&\n    modifiers.some(modifier => {\n      return (\n        modifier.name === requestedName &&\n        modifier.enabled &&\n        modifier.order < requesting.order\n      );\n    });\n\n  if (!isRequired) {\n    const requesting = `\\`${requestingName}\\``;\n    const requested = `\\`${requestedName}\\``;\n    console.warn(\n      `${requested} modifier is required by ${requesting} modifier in order to work, be sure to include it before ${requesting}!`\n    );\n  }\n  return isRequired;\n}\n", "/**\n * Tells if a given input is a number\n * @method\n * @memberof Popper.Utils\n * @param {*} input to check\n * @return {Boolean}\n */\nexport default function isNumeric(n) {\n  return n !== '' && !isNaN(parseFloat(n)) && isFinite(n);\n}\n", "/**\n * Get the window associated with the element\n * @argument {Element} element\n * @returns {Window}\n */\nexport default function getWindow(element) {\n  const ownerDocument = element.ownerDocument;\n  return ownerDocument ? ownerDocument.defaultView : window;\n}\n", "import getWindow from './getWindow';\n\n/**\n * Remove event listeners used to update the popper position\n * @method\n * @memberof Popper.Utils\n * @private\n */\nexport default function removeEventListeners(reference, state) {\n  // Remove resize event listener on window\n  getWindow(reference).removeEventListener('resize', state.updateBound);\n\n  // Remove scroll event listener on scroll parents\n  state.scrollParents.forEach(target => {\n    target.removeEventListener('scroll', state.updateBound);\n  });\n\n  // Reset state\n  state.updateBound = null;\n  state.scrollParents = [];\n  state.scrollElement = null;\n  state.eventsEnabled = false;\n  return state;\n}\n", "import isFunction from './isFunction';\nimport findIndex from './findIndex';\nimport getClientRect from '../utils/getClientRect';\n\n/**\n * Loop trough the list of modifiers and run them in order,\n * each of them will then edit the data object.\n * @method\n * @memberof Popper.Utils\n * @param {dataObject} data\n * @param {Array} modifiers\n * @param {String} ends - Optional modifier name used as stopper\n * @returns {dataObject}\n */\nexport default function runModifiers(modifiers, data, ends) {\n  const modifiersToRun = ends === undefined\n    ? modifiers\n    : modifiers.slice(0, findIndex(modifiers, 'name', ends));\n\n  modifiersToRun.forEach(modifier => {\n    if (modifier['function']) { // eslint-disable-line dot-notation\n      console.warn('`modifier.function` is deprecated, use `modifier.fn`!');\n    }\n    const fn = modifier['function'] || modifier.fn; // eslint-disable-line dot-notation\n    if (modifier.enabled && isFunction(fn)) {\n      // Add properties to offsets to make them a complete clientRect object\n      // we do this before each modifier to make sure the previous one doesn't\n      // mess with these values\n      data.offsets.popper = getClientRect(data.offsets.popper);\n      data.offsets.reference = getClientRect(data.offsets.reference);\n\n      data = fn(data, modifier);\n    }\n  });\n\n  return data;\n}\n", "/**\n * Set the attributes to the given popper\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element - Element to apply the attributes to\n * @argument {Object} styles\n * Object with a list of properties and values which will be applied to the element\n */\nexport default function setAttributes(element, attributes) {\n  Object.keys(attributes).forEach(function(prop) {\n    const value = attributes[prop];\n    if (value !== false) {\n      element.setAttribute(prop, attributes[prop]);\n    } else {\n      element.removeAttribute(prop);\n    }\n  });\n}\n", "import isNumeric from './isNumeric';\n\n/**\n * Set the style to the given popper\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element - Element to apply the style to\n * @argument {Object} styles\n * Object with a list of properties and values which will be applied to the element\n */\nexport default function setStyles(element, styles) {\n  Object.keys(styles).forEach(prop => {\n    let unit = '';\n    // add unit if the value is numeric and is one of the following\n    if (\n      ['width', 'height', 'top', 'right', 'bottom', 'left'].indexOf(prop) !==\n        -1 &&\n      isNumeric(styles[prop])\n    ) {\n      unit = 'px';\n    }\n    element.style[prop] = styles[prop] + unit;\n  });\n}\n", "import getScrollParent from './getScrollParent';\nimport getWindow from './getWindow';\n\nfunction attachToScrollParents(scrollParent, event, callback, scrollParents) {\n  const isBody = scrollParent.nodeName === 'BODY';\n  const target = isBody ? scrollParent.ownerDocument.defaultView : scrollParent;\n  target.addEventListener(event, callback, { passive: true });\n\n  if (!isBody) {\n    attachToScrollParents(\n      getScrollParent(target.parentNode),\n      event,\n      callback,\n      scrollParents\n    );\n  }\n  scrollParents.push(target);\n}\n\n/**\n * Setup needed event listeners used to update the popper position\n * @method\n * @memberof Popper.Utils\n * @private\n */\nexport default function setupEventListeners(\n  reference,\n  options,\n  state,\n  updateBound\n) {\n  // Resize event listener on window\n  state.updateBound = updateBound;\n  getWindow(reference).addEventListener('resize', state.updateBound, { passive: true });\n\n  // Scroll event listener on scroll parents\n  const scrollElement = getScrollParent(reference);\n  attachToScrollParents(\n    scrollElement,\n    'scroll',\n    state.updateBound,\n    state.scrollParents\n  );\n  state.scrollElement = scrollElement;\n  state.eventsEnabled = true;\n\n  return state;\n}\n", "import computeAutoPlacement from './computeAutoPlacement';\nimport debounce from './debounce';\nimport findIndex from './findIndex';\nimport getBordersSize from './getBordersSize';\nimport getBoundaries from './getBoundaries';\nimport getBoundingClientRect from './getBoundingClientRect';\nimport getClientRect from './getClientRect';\nimport getOffsetParent from './getOffsetParent';\nimport getOffsetRect from './getOffsetRect';\nimport getOffsetRectRelativeToArbitraryNode from './getOffsetRectRelativeToArbitraryNode';\nimport getOuterSizes from './getOuterSizes';\nimport getParentNode from './getParentNode';\nimport getPopperOffsets from './getPopperOffsets';\nimport getReferenceOffsets from './getReferenceOffsets';\nimport getScroll from './getScroll';\nimport getScrollParent from './getScrollParent';\nimport getStyleComputedProperty from './getStyleComputedProperty';\nimport getSupportedPropertyName from './getSupportedPropertyName';\nimport getWindowSizes from './getWindowSizes';\nimport isFixed from './isFixed';\nimport isFunction from './isFunction';\nimport isModifierEnabled from './isModifierEnabled';\nimport isModifierRequired from './isModifierRequired';\nimport isNumeric from './isNumeric';\nimport removeEventListeners from './removeEventListeners';\nimport runModifiers from './runModifiers';\nimport setAttributes from './setAttributes';\nimport setStyles from './setStyles';\nimport setupEventListeners from './setupEventListeners';\n\n/** @namespace Popper.Utils */\nexport {\n  computeAutoPlacement,\n  debounce,\n  findIndex,\n  getBordersSize,\n  getBoundaries,\n  getBoundingClientRect,\n  getClientRect,\n  getOffsetParent,\n  getOffsetRect,\n  getOffsetRectRelativeToArbitraryNode,\n  getOuterSizes,\n  getParentNode,\n  getPopperOffsets,\n  getReferenceOffsets,\n  getScroll,\n  getScrollParent,\n  getStyleComputedProperty,\n  getSupportedPropertyName,\n  getWindowSizes,\n  isFixed,\n  isFunction,\n  isModifierEnabled,\n  isModifierRequired,\n  isNumeric,\n  removeEventListeners,\n  runModifiers,\n  setAttributes,\n  setStyles,\n  setupEventListeners,\n};\n\n// This is here just for backward compatibility with versions lower than v1.10.3\n// you should import the utilities using named exports, if you want them all use:\n// ```\n// import * as PopperUtils from 'popper-utils';\n// ```\n// The default export will be removed in the next major version.\nexport default {\n  computeAutoPlacement,\n  debounce,\n  findIndex,\n  getBordersSize,\n  getBoundaries,\n  getBoundingClientRect,\n  getClientRect,\n  getOffsetParent,\n  getOffsetRect,\n  getOffsetRectRelativeToArbitraryNode,\n  getOuterSizes,\n  getParentNode,\n  getPopperOffsets,\n  getReferenceOffsets,\n  getScroll,\n  getScrollParent,\n  getStyleComputedProperty,\n  getSupportedPropertyName,\n  getWindowSizes,\n  isFixed,\n  isFunction,\n  isModifierEnabled,\n  isModifierRequired,\n  isNumeric,\n  removeEventListeners,\n  runModifiers,\n  setAttributes,\n  setStyles,\n  setupEventListeners,\n};\n"], "names": ["getStyleComputedProperty", "element", "property", "nodeType", "window", "ownerDocument", "defaultView", "css", "getComputedStyle", "getParentNode", "nodeName", "parentNode", "host", "getScrollParent", "document", "body", "overflow", "overflowX", "overflowY", "test", "getReferenceNode", "reference", "referenceNode", "navigator", "isIE11", "<PERSON><PERSON><PERSON><PERSON>", "MSInputMethodContext", "documentMode", "isIE10", "userAgent", "isIE", "version", "getOffsetParent", "documentElement", "noOffsetParent", "offsetParent", "nextElement<PERSON><PERSON>ling", "indexOf", "isOffsetContainer", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "getRoot", "node", "findCommonOffsetParent", "element1", "element2", "order", "compareDocumentPosition", "Node", "DOCUMENT_POSITION_FOLLOWING", "start", "end", "range", "createRange", "setStart", "setEnd", "commonAncestorContainer", "contains", "element1root", "getScroll", "side", "upperSide", "html", "scrollingElement", "includeScroll", "rect", "subtract", "scrollTop", "scrollLeft", "modifier", "top", "bottom", "left", "right", "getBordersSize", "styles", "axis", "sideA", "sideB", "parseFloat", "getSize", "computedStyle", "Math", "max", "parseInt", "getWindowSizes", "getClientRect", "offsets", "width", "height", "getBoundingClientRect", "e", "result", "sizes", "clientWidth", "clientHeight", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "offsetWidth", "vertScrollbar", "offsetHeight", "getOffsetRectRelativeToArbitraryNode", "children", "parent", "fixedPosition", "runIsIE", "isHTML", "childrenRect", "parentRect", "scrollParent", "borderTopWidth", "borderLeftWidth", "marginTop", "marginLeft", "getViewportOffsetRectRelativeToArtbitraryNode", "excludeScroll", "relativeOffset", "innerWidth", "innerHeight", "offset", "isFixed", "getFixedPositionOffsetParent", "parentElement", "el", "getBoundaries", "popper", "padding", "boundariesElement", "boundaries", "boundariesNode", "isPaddingNumber", "getArea", "computeAutoPlacement", "placement", "refRect", "rects", "sorted<PERSON>reas", "Object", "keys", "map", "key", "sort", "a", "b", "area", "filtered<PERSON><PERSON>s", "filter", "computedPlacement", "length", "variation", "split", "timeoutDuration", "longerTimeoutBrowsers", "i", "microtaskDebounce", "fn", "called", "Promise", "resolve", "then", "taskDebounce", "scheduled", "supportsMicroTasks", "find", "arr", "check", "Array", "prototype", "findIndex", "prop", "value", "cur", "match", "obj", "getOffsetRect", "elementRect", "offsetLeft", "offsetTop", "getOuterSizes", "x", "marginBottom", "y", "marginRight", "getOppositePlacement", "hash", "replace", "matched", "getPopperOffsets", "referenceOffsets", "popperRect", "popperOffsets", "isHoriz", "mainSide", "secondarySide", "measurement", "secondaryMeasurement", "getReferenceOffsets", "state", "commonOffsetParent", "getSupportedPropertyName", "prefixes", "upperProp", "char<PERSON>t", "toUpperCase", "slice", "prefix", "to<PERSON><PERSON><PERSON>", "style", "isFunction", "functionToCheck", "getType", "toString", "call", "isModifierEnabled", "modifiers", "modifierName", "some", "name", "enabled", "isModifierRequired", "requestingName", "requested<PERSON><PERSON>", "requesting", "isRequired", "requested", "warn", "isNumeric", "n", "isNaN", "isFinite", "getWindow", "removeEventListeners", "removeEventListener", "updateBound", "scrollParents", "for<PERSON>ach", "scrollElement", "eventsEnabled", "runModifiers", "data", "ends", "modifiersToRun", "undefined", "setAttributes", "attributes", "setAttribute", "removeAttribute", "setStyles", "unit", "attachToScrollParents", "event", "callback", "isBody", "target", "addEventListener", "passive", "push", "setupEventListeners", "options"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;;;;;;AAOA,AAAe,SAASA,wBAAT,CAAkCC,OAAlC,EAA2CC,QAA3C,EAAqD;MAC9DD,QAAQE,QAAR,KAAqB,CAAzB,EAA4B;WACnB,EAAP;;;MAGIC,SAASH,QAAQI,aAAR,CAAsBC,WAArC;MACMC,MAAMH,OAAOI,gBAAP,CAAwBP,OAAxB,EAAiC,IAAjC,CAAZ;SACOC,WAAWK,IAAIL,QAAJ,CAAX,GAA2BK,GAAlC;;;ACdF;;;;;;;AAOA,AAAe,SAASE,aAAT,CAAuBR,OAAvB,EAAgC;MACzCA,QAAQS,QAAR,KAAqB,MAAzB,EAAiC;WACxBT,OAAP;;SAEKA,QAAQU,UAAR,IAAsBV,QAAQW,IAArC;;;ACRF;;;;;;;AAOA,AAAe,SAASC,eAAT,CAAyBZ,OAAzB,EAAkC;;MAE3C,CAACA,OAAL,EAAc;WACLa,SAASC,IAAhB;;;UAGMd,QAAQS,QAAhB;SACO,MAAL;SACK,MAAL;aACST,QAAQI,aAAR,CAAsBU,IAA7B;SACG,WAAL;aACSd,QAAQc,IAAf;;;;;8BAIuCf,yBAAyBC,OAAzB,CAfI;MAevCe,QAfuC,yBAevCA,QAfuC;MAe7BC,SAf6B,yBAe7BA,SAf6B;MAelBC,SAfkB,yBAelBA,SAfkB;;MAgB3C,wBAAwBC,IAAxB,CAA6BH,WAAWE,SAAX,GAAuBD,SAApD,CAAJ,EAAoE;WAC3DhB,OAAP;;;SAGKY,gBAAgBJ,cAAcR,OAAd,CAAhB,CAAP;;;AC9BF;;;;;;;AAOA,AAAe,SAASmB,gBAAT,CAA0BC,SAA1B,EAAqC;SAC3CA,aAAaA,UAAUC,aAAvB,GAAuCD,UAAUC,aAAjD,GAAiED,SAAxE;;;ACRF,gBAAe,OAAOjB,MAAP,KAAkB,WAAlB,IAAiC,OAAOU,QAAP,KAAoB,WAArD,IAAoE,OAAOS,SAAP,KAAqB,WAAxG;;ACEA,IAAMC,SAASC,aAAa,CAAC,EAAErB,OAAOsB,oBAAP,IAA+BZ,SAASa,YAA1C,CAA7B;AACA,IAAMC,SAASH,aAAa,UAAUN,IAAV,CAAeI,UAAUM,SAAzB,CAA5B;;;;;;;;;AASA,AAAe,SAASC,IAAT,CAAcC,OAAd,EAAuB;MAChCA,YAAY,EAAhB,EAAoB;WACXP,MAAP;;MAEEO,YAAY,EAAhB,EAAoB;WACXH,MAAP;;SAEKJ,UAAUI,MAAjB;;;ACjBF;;;;;;;AAOA,AAAe,SAASI,eAAT,CAAyB/B,OAAzB,EAAkC;MAC3C,CAACA,OAAL,EAAc;WACLa,SAASmB,eAAhB;;;MAGIC,iBAAiBJ,KAAK,EAAL,IAAWhB,SAASC,IAApB,GAA2B,IAAlD;;;MAGIoB,eAAelC,QAAQkC,YAAR,IAAwB,IAA3C;;SAEOA,iBAAiBD,cAAjB,IAAmCjC,QAAQmC,kBAAlD,EAAsE;mBACrD,CAACnC,UAAUA,QAAQmC,kBAAnB,EAAuCD,YAAtD;;;MAGIzB,WAAWyB,gBAAgBA,aAAazB,QAA9C;;MAEI,CAACA,QAAD,IAAaA,aAAa,MAA1B,IAAoCA,aAAa,MAArD,EAA6D;WACpDT,UAAUA,QAAQI,aAAR,CAAsB4B,eAAhC,GAAkDnB,SAASmB,eAAlE;;;;;MAMA,CAAC,IAAD,EAAO,IAAP,EAAa,OAAb,EAAsBI,OAAtB,CAA8BF,aAAazB,QAA3C,MAAyD,CAAC,CAA1D,IACAV,yBAAyBmC,YAAzB,EAAuC,UAAvC,MAAuD,QAFzD,EAGE;WACOH,gBAAgBG,YAAhB,CAAP;;;SAGKA,YAAP;;;ACpCa,SAASG,iBAAT,CAA2BrC,OAA3B,EAAoC;MACzCS,QADyC,GAC5BT,OAD4B,CACzCS,QADyC;;MAE7CA,aAAa,MAAjB,EAAyB;WAChB,KAAP;;SAGAA,aAAa,MAAb,IAAuBsB,gBAAgB/B,QAAQsC,iBAAxB,MAA+CtC,OADxE;;;ACPF;;;;;;;AAOA,AAAe,SAASuC,OAAT,CAAiBC,IAAjB,EAAuB;MAChCA,KAAK9B,UAAL,KAAoB,IAAxB,EAA8B;WACrB6B,QAAQC,KAAK9B,UAAb,CAAP;;;SAGK8B,IAAP;;;ACRF;;;;;;;;AAQA,AAAe,SAASC,sBAAT,CAAgCC,QAAhC,EAA0CC,QAA1C,EAAoD;;MAE7D,CAACD,QAAD,IAAa,CAACA,SAASxC,QAAvB,IAAmC,CAACyC,QAApC,IAAgD,CAACA,SAASzC,QAA9D,EAAwE;WAC/DW,SAASmB,eAAhB;;;;MAIIY,QACJF,SAASG,uBAAT,CAAiCF,QAAjC,IACAG,KAAKC,2BAFP;MAGMC,QAAQJ,QAAQF,QAAR,GAAmBC,QAAjC;MACMM,MAAML,QAAQD,QAAR,GAAmBD,QAA/B;;;MAGMQ,QAAQrC,SAASsC,WAAT,EAAd;QACMC,QAAN,CAAeJ,KAAf,EAAsB,CAAtB;QACMK,MAAN,CAAaJ,GAAb,EAAkB,CAAlB;MACQK,uBAjByD,GAiB7BJ,KAjB6B,CAiBzDI,uBAjByD;;;;MAqB9DZ,aAAaY,uBAAb,IACCX,aAAaW,uBADf,IAEAN,MAAMO,QAAN,CAAeN,GAAf,CAHF,EAIE;QACIZ,kBAAkBiB,uBAAlB,CAAJ,EAAgD;aACvCA,uBAAP;;;WAGKvB,gBAAgBuB,uBAAhB,CAAP;;;;MAIIE,eAAejB,QAAQG,QAAR,CAArB;MACIc,aAAa7C,IAAjB,EAAuB;WACd8B,uBAAuBe,aAAa7C,IAApC,EAA0CgC,QAA1C,CAAP;GADF,MAEO;WACEF,uBAAuBC,QAAvB,EAAiCH,QAAQI,QAAR,EAAkBhC,IAAnD,CAAP;;;;ACjDJ;;;;;;;;AAQA,AAAe,SAAS8C,SAAT,CAAmBzD,OAAnB,EAA0C;MAAd0D,IAAc,uEAAP,KAAO;;MACjDC,YAAYD,SAAS,KAAT,GAAiB,WAAjB,GAA+B,YAAjD;MACMjD,WAAWT,QAAQS,QAAzB;;MAEIA,aAAa,MAAb,IAAuBA,aAAa,MAAxC,EAAgD;QACxCmD,OAAO5D,QAAQI,aAAR,CAAsB4B,eAAnC;QACM6B,mBAAmB7D,QAAQI,aAAR,CAAsByD,gBAAtB,IAA0CD,IAAnE;WACOC,iBAAiBF,SAAjB,CAAP;;;SAGK3D,QAAQ2D,SAAR,CAAP;;;AChBF;;;;;;;;;AASA,AAAe,SAASG,aAAT,CAAuBC,IAAvB,EAA6B/D,OAA7B,EAAwD;MAAlBgE,QAAkB,uEAAP,KAAO;;MAC/DC,YAAYR,UAAUzD,OAAV,EAAmB,KAAnB,CAAlB;MACMkE,aAAaT,UAAUzD,OAAV,EAAmB,MAAnB,CAAnB;MACMmE,WAAWH,WAAW,CAAC,CAAZ,GAAgB,CAAjC;OACKI,GAAL,IAAYH,YAAYE,QAAxB;OACKE,MAAL,IAAeJ,YAAYE,QAA3B;OACKG,IAAL,IAAaJ,aAAaC,QAA1B;OACKI,KAAL,IAAcL,aAAaC,QAA3B;SACOJ,IAAP;;;ACnBF;;;;;;;;;;AAUA,AAAe,SAASS,cAAT,CAAwBC,MAAxB,EAAgCC,IAAhC,EAAsC;MAC7CC,QAAQD,SAAS,GAAT,GAAe,MAAf,GAAwB,KAAtC;MACME,QAAQD,UAAU,MAAV,GAAmB,OAAnB,GAA6B,QAA3C;;SAGEE,WAAWJ,kBAAgBE,KAAhB,WAAX,IACAE,WAAWJ,kBAAgBG,KAAhB,WAAX,CAFF;;;ACZF,SAASE,OAAT,CAAiBJ,IAAjB,EAAuB5D,IAAvB,EAA6B8C,IAA7B,EAAmCmB,aAAnC,EAAkD;SACzCC,KAAKC,GAAL,CACLnE,gBAAc4D,IAAd,CADK,EAEL5D,gBAAc4D,IAAd,CAFK,EAGLd,gBAAcc,IAAd,CAHK,EAILd,gBAAcc,IAAd,CAJK,EAKLd,gBAAcc,IAAd,CALK,EAML7C,KAAK,EAAL,IACKqD,SAAStB,gBAAcc,IAAd,CAAT,IACHQ,SAASH,0BAAuBL,SAAS,QAAT,GAAoB,KAApB,GAA4B,MAAnD,EAAT,CADG,GAEHQ,SAASH,0BAAuBL,SAAS,QAAT,GAAoB,QAApB,GAA+B,OAAtD,EAAT,CAHF,GAIE,CAVG,CAAP;;;AAcF,AAAe,SAASS,cAAT,CAAwBtE,QAAxB,EAAkC;MACzCC,OAAOD,SAASC,IAAtB;MACM8C,OAAO/C,SAASmB,eAAtB;MACM+C,gBAAgBlD,KAAK,EAAL,KAAYtB,iBAAiBqD,IAAjB,CAAlC;;SAEO;YACGkB,QAAQ,QAAR,EAAkBhE,IAAlB,EAAwB8C,IAAxB,EAA8BmB,aAA9B,CADH;WAEED,QAAQ,OAAR,EAAiBhE,IAAjB,EAAuB8C,IAAvB,EAA6BmB,aAA7B;GAFT;;;;;;;;;;;;;;;;;ACtBF;;;;;;;AAOA,AAAe,SAASK,aAAT,CAAuBC,OAAvB,EAAgC;sBAExCA,OADL;WAESA,QAAQf,IAAR,GAAee,QAAQC,KAFhC;YAGUD,QAAQjB,GAAR,GAAciB,QAAQE;;;;ACJlC;;;;;;;AAOA,AAAe,SAASC,qBAAT,CAA+BxF,OAA/B,EAAwC;MACjD+D,OAAO,EAAX;;;;;MAKI;QACElC,KAAK,EAAL,CAAJ,EAAc;aACL7B,QAAQwF,qBAAR,EAAP;UACMvB,YAAYR,UAAUzD,OAAV,EAAmB,KAAnB,CAAlB;UACMkE,aAAaT,UAAUzD,OAAV,EAAmB,MAAnB,CAAnB;WACKoE,GAAL,IAAYH,SAAZ;WACKK,IAAL,IAAaJ,UAAb;WACKG,MAAL,IAAeJ,SAAf;WACKM,KAAL,IAAcL,UAAd;KAPF,MASK;aACIlE,QAAQwF,qBAAR,EAAP;;GAXJ,CAcA,OAAMC,CAAN,EAAQ;;MAEFC,SAAS;UACP3B,KAAKO,IADE;SAERP,KAAKK,GAFG;WAGNL,KAAKQ,KAAL,GAAaR,KAAKO,IAHZ;YAILP,KAAKM,MAAL,GAAcN,KAAKK;GAJ7B;;;MAQMuB,QAAQ3F,QAAQS,QAAR,KAAqB,MAArB,GAA8B0E,eAAenF,QAAQI,aAAvB,CAA9B,GAAsE,EAApF;MACMkF,QACJK,MAAML,KAAN,IAAetF,QAAQ4F,WAAvB,IAAsCF,OAAOJ,KAD/C;MAEMC,SACJI,MAAMJ,MAAN,IAAgBvF,QAAQ6F,YAAxB,IAAwCH,OAAOH,MADjD;;MAGIO,iBAAiB9F,QAAQ+F,WAAR,GAAsBT,KAA3C;MACIU,gBAAgBhG,QAAQiG,YAAR,GAAuBV,MAA3C;;;;MAIIO,kBAAkBE,aAAtB,EAAqC;QAC7BvB,SAAS1E,yBAAyBC,OAAzB,CAAf;sBACkBwE,eAAeC,MAAf,EAAuB,GAAvB,CAAlB;qBACiBD,eAAeC,MAAf,EAAuB,GAAvB,CAAjB;;WAEOa,KAAP,IAAgBQ,cAAhB;WACOP,MAAP,IAAiBS,aAAjB;;;SAGKZ,cAAcM,MAAd,CAAP;;;ACzDa,SAASQ,oCAAT,CAA8CC,QAA9C,EAAwDC,MAAxD,EAAuF;MAAvBC,aAAuB,uEAAP,KAAO;;MAC9F1E,SAAS2E,KAAQ,EAAR,CAAf;MACMC,SAASH,OAAO3F,QAAP,KAAoB,MAAnC;MACM+F,eAAehB,sBAAsBW,QAAtB,CAArB;MACMM,aAAajB,sBAAsBY,MAAtB,CAAnB;MACMM,eAAe9F,gBAAgBuF,QAAhB,CAArB;;MAEM1B,SAAS1E,yBAAyBqG,MAAzB,CAAf;MACMO,iBAAiB9B,WAAWJ,OAAOkC,cAAlB,CAAvB;MACMC,kBAAkB/B,WAAWJ,OAAOmC,eAAlB,CAAxB;;;MAGGP,iBAAiBE,MAApB,EAA4B;eACfnC,GAAX,GAAiBY,KAAKC,GAAL,CAASwB,WAAWrC,GAApB,EAAyB,CAAzB,CAAjB;eACWE,IAAX,GAAkBU,KAAKC,GAAL,CAASwB,WAAWnC,IAApB,EAA0B,CAA1B,CAAlB;;MAEEe,UAAUD,cAAc;SACrBoB,aAAapC,GAAb,GAAmBqC,WAAWrC,GAA9B,GAAoCuC,cADf;UAEpBH,aAAalC,IAAb,GAAoBmC,WAAWnC,IAA/B,GAAsCsC,eAFlB;WAGnBJ,aAAalB,KAHM;YAIlBkB,aAAajB;GAJT,CAAd;UAMQsB,SAAR,GAAoB,CAApB;UACQC,UAAR,GAAqB,CAArB;;;;;;MAMI,CAACnF,MAAD,IAAW4E,MAAf,EAAuB;QACfM,YAAYhC,WAAWJ,OAAOoC,SAAlB,CAAlB;QACMC,aAAajC,WAAWJ,OAAOqC,UAAlB,CAAnB;;YAEQ1C,GAAR,IAAeuC,iBAAiBE,SAAhC;YACQxC,MAAR,IAAkBsC,iBAAiBE,SAAnC;YACQvC,IAAR,IAAgBsC,kBAAkBE,UAAlC;YACQvC,KAAR,IAAiBqC,kBAAkBE,UAAnC;;;YAGQD,SAAR,GAAoBA,SAApB;YACQC,UAAR,GAAqBA,UAArB;;;MAIAnF,UAAU,CAAC0E,aAAX,GACID,OAAO7C,QAAP,CAAgBmD,YAAhB,CADJ,GAEIN,WAAWM,YAAX,IAA2BA,aAAajG,QAAb,KAA0B,MAH3D,EAIE;cACUqD,cAAcuB,OAAd,EAAuBe,MAAvB,CAAV;;;SAGKf,OAAP;;;ACtDa,SAAS0B,6CAAT,CAAuD/G,OAAvD,EAAuF;MAAvBgH,aAAuB,uEAAP,KAAO;;MAC9FpD,OAAO5D,QAAQI,aAAR,CAAsB4B,eAAnC;MACMiF,iBAAiBf,qCAAqClG,OAArC,EAA8C4D,IAA9C,CAAvB;MACM0B,QAAQN,KAAKC,GAAL,CAASrB,KAAKgC,WAAd,EAA2BzF,OAAO+G,UAAP,IAAqB,CAAhD,CAAd;MACM3B,SAASP,KAAKC,GAAL,CAASrB,KAAKiC,YAAd,EAA4B1F,OAAOgH,WAAP,IAAsB,CAAlD,CAAf;;MAEMlD,YAAY,CAAC+C,aAAD,GAAiBvD,UAAUG,IAAV,CAAjB,GAAmC,CAArD;MACMM,aAAa,CAAC8C,aAAD,GAAiBvD,UAAUG,IAAV,EAAgB,MAAhB,CAAjB,GAA2C,CAA9D;;MAEMwD,SAAS;SACRnD,YAAYgD,eAAe7C,GAA3B,GAAiC6C,eAAeJ,SADxC;UAEP3C,aAAa+C,eAAe3C,IAA5B,GAAmC2C,eAAeH,UAF3C;gBAAA;;GAAf;;SAOO1B,cAAcgC,MAAd,CAAP;;;ACjBF;;;;;;;;AAQA,AAAe,SAASC,OAAT,CAAiBrH,OAAjB,EAA0B;MACjCS,WAAWT,QAAQS,QAAzB;MACIA,aAAa,MAAb,IAAuBA,aAAa,MAAxC,EAAgD;WACvC,KAAP;;MAEEV,yBAAyBC,OAAzB,EAAkC,UAAlC,MAAkD,OAAtD,EAA+D;WACtD,IAAP;;MAEIU,aAAaF,cAAcR,OAAd,CAAnB;MACI,CAACU,UAAL,EAAiB;WACR,KAAP;;SAEK2G,QAAQ3G,UAAR,CAAP;;;ACrBF;;;;;;;;AAQA,AAAe,SAAS4G,4BAAT,CAAsCtH,OAAtC,EAA+C;;MAEvD,CAACA,OAAD,IAAY,CAACA,QAAQuH,aAArB,IAAsC1F,MAA1C,EAAkD;WAC1ChB,SAASmB,eAAhB;;MAEEwF,KAAKxH,QAAQuH,aAAjB;SACOC,MAAMzH,yBAAyByH,EAAzB,EAA6B,WAA7B,MAA8C,MAA3D,EAAmE;SAC5DA,GAAGD,aAAR;;SAEKC,MAAM3G,SAASmB,eAAtB;;;ACTF;;;;;;;;;;;AAWA,AAAe,SAASyF,aAAT,CACbC,MADa,EAEbtG,SAFa,EAGbuG,OAHa,EAIbC,iBAJa,EAMb;MADAvB,aACA,uEADgB,KAChB;;;;MAGIwB,aAAa,EAAEzD,KAAK,CAAP,EAAUE,MAAM,CAAhB,EAAjB;MACMpC,eAAemE,gBAAgBiB,6BAA6BI,MAA7B,CAAhB,GAAuDjF,uBAAuBiF,MAAvB,EAA+BvG,iBAAiBC,SAAjB,CAA/B,CAA5E;;;MAGIwG,sBAAsB,UAA1B,EAAuC;iBACxBb,8CAA8C7E,YAA9C,EAA4DmE,aAA5D,CAAb;GADF,MAIK;;QAECyB,uBAAJ;QACIF,sBAAsB,cAA1B,EAA0C;uBACvBhH,gBAAgBJ,cAAcY,SAAd,CAAhB,CAAjB;UACI0G,eAAerH,QAAf,KAA4B,MAAhC,EAAwC;yBACrBiH,OAAOtH,aAAP,CAAqB4B,eAAtC;;KAHJ,MAKO,IAAI4F,sBAAsB,QAA1B,EAAoC;uBACxBF,OAAOtH,aAAP,CAAqB4B,eAAtC;KADK,MAEA;uBACY4F,iBAAjB;;;QAGIvC,UAAUa,qCACd4B,cADc,EAEd5F,YAFc,EAGdmE,aAHc,CAAhB;;;QAOIyB,eAAerH,QAAf,KAA4B,MAA5B,IAAsC,CAAC4G,QAAQnF,YAAR,CAA3C,EAAkE;4BACtCiD,eAAeuC,OAAOtH,aAAtB,CADsC;UACxDmF,MADwD,mBACxDA,MADwD;UAChDD,KADgD,mBAChDA,KADgD;;iBAErDlB,GAAX,IAAkBiB,QAAQjB,GAAR,GAAciB,QAAQwB,SAAxC;iBACWxC,MAAX,GAAoBkB,SAASF,QAAQjB,GAArC;iBACWE,IAAX,IAAmBe,QAAQf,IAAR,GAAee,QAAQyB,UAA1C;iBACWvC,KAAX,GAAmBe,QAAQD,QAAQf,IAAnC;KALF,MAMO;;mBAEQe,OAAb;;;;;YAKMsC,WAAW,CAArB;MACMI,kBAAkB,OAAOJ,OAAP,KAAmB,QAA3C;aACWrD,IAAX,IAAmByD,kBAAkBJ,OAAlB,GAA4BA,QAAQrD,IAAR,IAAgB,CAA/D;aACWF,GAAX,IAAkB2D,kBAAkBJ,OAAlB,GAA4BA,QAAQvD,GAAR,IAAe,CAA7D;aACWG,KAAX,IAAoBwD,kBAAkBJ,OAAlB,GAA4BA,QAAQpD,KAAR,IAAiB,CAAjE;aACWF,MAAX,IAAqB0D,kBAAkBJ,OAAlB,GAA4BA,QAAQtD,MAAR,IAAkB,CAAnE;;SAEOwD,UAAP;;;AC7EF,SAASG,OAAT,OAAoC;MAAjB1C,KAAiB,QAAjBA,KAAiB;MAAVC,MAAU,QAAVA,MAAU;;SAC3BD,QAAQC,MAAf;;;;;;;;;;;;AAYF,AAAe,SAAS0C,oBAAT,CACbC,SADa,EAEbC,OAFa,EAGbT,MAHa,EAIbtG,SAJa,EAKbwG,iBALa,EAOb;MADAD,OACA,uEADU,CACV;;MACIO,UAAU9F,OAAV,CAAkB,MAAlB,MAA8B,CAAC,CAAnC,EAAsC;WAC7B8F,SAAP;;;MAGIL,aAAaJ,cACjBC,MADiB,EAEjBtG,SAFiB,EAGjBuG,OAHiB,EAIjBC,iBAJiB,CAAnB;;MAOMQ,QAAQ;SACP;aACIP,WAAWvC,KADf;cAEK6C,QAAQ/D,GAAR,GAAcyD,WAAWzD;KAHvB;WAKL;aACEyD,WAAWtD,KAAX,GAAmB4D,QAAQ5D,KAD7B;cAEGsD,WAAWtC;KAPT;YASJ;aACCsC,WAAWvC,KADZ;cAEEuC,WAAWxD,MAAX,GAAoB8D,QAAQ9D;KAX1B;UAaN;aACG8D,QAAQ7D,IAAR,GAAeuD,WAAWvD,IAD7B;cAEIuD,WAAWtC;;GAfvB;;MAmBM8C,cAAcC,OAAOC,IAAP,CAAYH,KAAZ,EACjBI,GADiB,CACb;;;OAEAJ,MAAMK,GAAN,CAFA;YAGGT,QAAQI,MAAMK,GAAN,CAAR;;GAJU,EAMjBC,IANiB,CAMZ,UAACC,CAAD,EAAIC,CAAJ;WAAUA,EAAEC,IAAF,GAASF,EAAEE,IAArB;GANY,CAApB;;MAQMC,gBAAgBT,YAAYU,MAAZ,CACpB;QAAGzD,KAAH,SAAGA,KAAH;QAAUC,MAAV,SAAUA,MAAV;WACED,SAASoC,OAAO9B,WAAhB,IAA+BL,UAAUmC,OAAO7B,YADlD;GADoB,CAAtB;;MAKMmD,oBAAoBF,cAAcG,MAAd,GAAuB,CAAvB,GACtBH,cAAc,CAAd,EAAiBL,GADK,GAEtBJ,YAAY,CAAZ,EAAeI,GAFnB;;MAIMS,YAAYhB,UAAUiB,KAAV,CAAgB,GAAhB,EAAqB,CAArB,CAAlB;;SAEOH,qBAAqBE,kBAAgBA,SAAhB,GAA8B,EAAnD,CAAP;;;ACtEF,IAAME,kBAAmB,YAAU;MAC3BC,wBAAwB,CAAC,MAAD,EAAS,SAAT,EAAoB,SAApB,CAA9B;OACK,IAAIC,IAAI,CAAb,EAAgBA,IAAID,sBAAsBJ,MAA1C,EAAkDK,KAAK,CAAvD,EAA0D;QACpD9H,aAAaF,UAAUM,SAAV,CAAoBQ,OAApB,CAA4BiH,sBAAsBC,CAAtB,CAA5B,KAAyD,CAA1E,EAA6E;aACpE,CAAP;;;SAGG,CAAP;CAPuB,EAAzB;;AAUA,AAAO,SAASC,iBAAT,CAA2BC,EAA3B,EAA+B;MAChCC,SAAS,KAAb;SACO,YAAM;QACPA,MAAJ,EAAY;;;aAGH,IAAT;WACOC,OAAP,CAAeC,OAAf,GAAyBC,IAAzB,CAA8B,YAAM;eACzB,KAAT;;KADF;GALF;;;AAYF,AAAO,SAASC,YAAT,CAAsBL,EAAtB,EAA0B;MAC3BM,YAAY,KAAhB;SACO,YAAM;QACP,CAACA,SAAL,EAAgB;kBACF,IAAZ;iBACW,YAAM;oBACH,KAAZ;;OADF,EAGGV,eAHH;;GAHJ;;;AAWF,IAAMW,qBAAqBvI,aAAarB,OAAOuJ,OAA/C;;;;;;;;;;;AAYA,eAAgBK,qBACZR,iBADY,GAEZM,YAFJ;;ACnDA;;;;;;;;;AASA,AAAe,SAASG,IAAT,CAAcC,GAAd,EAAmBC,KAAnB,EAA0B;;MAEnCC,MAAMC,SAAN,CAAgBJ,IAApB,EAA0B;WACjBC,IAAID,IAAJ,CAASE,KAAT,CAAP;;;;SAIKD,IAAIlB,MAAJ,CAAWmB,KAAX,EAAkB,CAAlB,CAAP;;;ACdF;;;;;;;;;AASA,AAAe,SAASG,SAAT,CAAmBJ,GAAnB,EAAwBK,IAAxB,EAA8BC,KAA9B,EAAqC;;MAE9CJ,MAAMC,SAAN,CAAgBC,SAApB,EAA+B;WACtBJ,IAAII,SAAJ,CAAc;aAAOG,IAAIF,IAAJ,MAAcC,KAArB;KAAd,CAAP;;;;MAIIE,QAAQT,KAAKC,GAAL,EAAU;WAAOS,IAAIJ,IAAJ,MAAcC,KAArB;GAAV,CAAd;SACON,IAAI7H,OAAJ,CAAYqI,KAAZ,CAAP;;;AChBF;;;;;;;AAOA,AAAe,SAASE,aAAT,CAAuB3K,OAAvB,EAAgC;MACzC4K,oBAAJ;MACI5K,QAAQS,QAAR,KAAqB,MAAzB,EAAiC;0BACL0E,eAAenF,QAAQI,aAAvB,CADK;QACvBkF,KADuB,mBACvBA,KADuB;QAChBC,MADgB,mBAChBA,MADgB;;kBAEjB;kBAAA;oBAAA;YAGN,CAHM;WAIP;KAJP;GAFF,MAQO;kBACS;aACLvF,QAAQ+F,WADH;cAEJ/F,QAAQiG,YAFJ;YAGNjG,QAAQ6K,UAHF;WAIP7K,QAAQ8K;KAJf;;;;SASK1F,cAAcwF,WAAd,CAAP;;;AC9BF;;;;;;;AAOA,AAAe,SAASG,aAAT,CAAuB/K,OAAvB,EAAgC;MACvCG,SAASH,QAAQI,aAAR,CAAsBC,WAArC;MACMoE,SAAStE,OAAOI,gBAAP,CAAwBP,OAAxB,CAAf;MACMgL,IAAInG,WAAWJ,OAAOoC,SAAP,IAAoB,CAA/B,IAAoChC,WAAWJ,OAAOwG,YAAP,IAAuB,CAAlC,CAA9C;MACMC,IAAIrG,WAAWJ,OAAOqC,UAAP,IAAqB,CAAhC,IAAqCjC,WAAWJ,OAAO0G,WAAP,IAAsB,CAAjC,CAA/C;MACMzF,SAAS;WACN1F,QAAQ+F,WAAR,GAAsBmF,CADhB;YAELlL,QAAQiG,YAAR,GAAuB+E;GAFjC;SAIOtF,MAAP;;;AChBF;;;;;;;AAOA,AAAe,SAAS0F,oBAAT,CAA8BlD,SAA9B,EAAyC;MAChDmD,OAAO,EAAE/G,MAAM,OAAR,EAAiBC,OAAO,MAAxB,EAAgCF,QAAQ,KAAxC,EAA+CD,KAAK,QAApD,EAAb;SACO8D,UAAUoD,OAAV,CAAkB,wBAAlB,EAA4C;WAAWD,KAAKE,OAAL,CAAX;GAA5C,CAAP;;;ACNF;;;;;;;;;;AAUA,AAAe,SAASC,gBAAT,CAA0B9D,MAA1B,EAAkC+D,gBAAlC,EAAoDvD,SAApD,EAA+D;cAChEA,UAAUiB,KAAV,CAAgB,GAAhB,EAAqB,CAArB,CAAZ;;;MAGMuC,aAAaX,cAAcrD,MAAd,CAAnB;;;MAGMiE,gBAAgB;WACbD,WAAWpG,KADE;YAEZoG,WAAWnG;GAFrB;;;MAMMqG,UAAU,CAAC,OAAD,EAAU,MAAV,EAAkBxJ,OAAlB,CAA0B8F,SAA1B,MAAyC,CAAC,CAA1D;MACM2D,WAAWD,UAAU,KAAV,GAAkB,MAAnC;MACME,gBAAgBF,UAAU,MAAV,GAAmB,KAAzC;MACMG,cAAcH,UAAU,QAAV,GAAqB,OAAzC;MACMI,uBAAuB,CAACJ,OAAD,GAAW,QAAX,GAAsB,OAAnD;;gBAEcC,QAAd,IACEJ,iBAAiBI,QAAjB,IACAJ,iBAAiBM,WAAjB,IAAgC,CADhC,GAEAL,WAAWK,WAAX,IAA0B,CAH5B;MAII7D,cAAc4D,aAAlB,EAAiC;kBACjBA,aAAd,IACEL,iBAAiBK,aAAjB,IAAkCJ,WAAWM,oBAAX,CADpC;GADF,MAGO;kBACSF,aAAd,IACEL,iBAAiBL,qBAAqBU,aAArB,CAAjB,CADF;;;SAIKH,aAAP;;;ACvCF;;;;;;;;;;AAUA,AAAe,SAASM,mBAAT,CAA6BC,KAA7B,EAAoCxE,MAApC,EAA4CtG,SAA5C,EAA6E;MAAtBiF,aAAsB,uEAAN,IAAM;;MACpF8F,qBAAqB9F,gBAAgBiB,6BAA6BI,MAA7B,CAAhB,GAAuDjF,uBAAuBiF,MAAvB,EAA+BvG,iBAAiBC,SAAjB,CAA/B,CAAlF;SACO8E,qCAAqC9E,SAArC,EAAgD+K,kBAAhD,EAAoE9F,aAApE,CAAP;;;ACjBF;;;;;;;AAOA,AAAe,SAAS+F,wBAAT,CAAkCnM,QAAlC,EAA4C;MACnDoM,WAAW,CAAC,KAAD,EAAQ,IAAR,EAAc,QAAd,EAAwB,KAAxB,EAA+B,GAA/B,CAAjB;MACMC,YAAYrM,SAASsM,MAAT,CAAgB,CAAhB,EAAmBC,WAAnB,KAAmCvM,SAASwM,KAAT,CAAe,CAAf,CAArD;;OAEK,IAAInD,IAAI,CAAb,EAAgBA,IAAI+C,SAASpD,MAA7B,EAAqCK,GAArC,EAA0C;QAClCoD,SAASL,SAAS/C,CAAT,CAAf;QACMqD,UAAUD,cAAYA,MAAZ,GAAqBJ,SAArB,GAAmCrM,QAAnD;QACI,OAAOY,SAASC,IAAT,CAAc8L,KAAd,CAAoBD,OAApB,CAAP,KAAwC,WAA5C,EAAyD;aAChDA,OAAP;;;SAGG,IAAP;;;AClBF;;;;;;;AAOA,AAAe,SAASE,UAAT,CAAoBC,eAApB,EAAqC;MAC5CC,UAAU,EAAhB;SAEED,mBACAC,QAAQC,QAAR,CAAiBC,IAAjB,CAAsBH,eAAtB,MAA2C,mBAF7C;;;ACTF;;;;;;AAMA,AAAe,SAASI,iBAAT,CAA2BC,SAA3B,EAAsCC,YAAtC,EAAoD;SAC1DD,UAAUE,IAAV,CACL;QAAGC,IAAH,QAAGA,IAAH;QAASC,OAAT,QAASA,OAAT;WAAuBA,WAAWD,SAASF,YAA3C;GADK,CAAP;;;ACLF;;;;;;;;;;AAUA,AAAe,SAASI,kBAAT,CACbL,SADa,EAEbM,cAFa,EAGbC,aAHa,EAIb;MACMC,aAAa3D,KAAKmD,SAAL,EAAgB;QAAGG,IAAH,QAAGA,IAAH;WAAcA,SAASG,cAAvB;GAAhB,CAAnB;;MAEMG,aACJ,CAAC,CAACD,UAAF,IACAR,UAAUE,IAAV,CAAe,oBAAY;WAEvBlJ,SAASmJ,IAAT,KAAkBI,aAAlB,IACAvJ,SAASoJ,OADT,IAEApJ,SAASvB,KAAT,GAAiB+K,WAAW/K,KAH9B;GADF,CAFF;;MAUI,CAACgL,UAAL,EAAiB;QACTD,oBAAkBF,cAAlB,MAAN;QACMI,kBAAiBH,aAAjB,MAAN;YACQI,IAAR,CACKD,SADL,iCAC0CF,WAD1C,iEACgHA,WADhH;;SAIKC,UAAP;;;ACpCF;;;;;;;AAOA,AAAe,SAASG,SAAT,CAAmBC,CAAnB,EAAsB;SAC5BA,MAAM,EAAN,IAAY,CAACC,MAAMpJ,WAAWmJ,CAAX,CAAN,CAAb,IAAqCE,SAASF,CAAT,CAA5C;;;ACRF;;;;;AAKA,AAAe,SAASG,SAAT,CAAmBnO,OAAnB,EAA4B;MACnCI,gBAAgBJ,QAAQI,aAA9B;SACOA,gBAAgBA,cAAcC,WAA9B,GAA4CF,MAAnD;;;ACLF;;;;;;AAMA,AAAe,SAASiO,oBAAT,CAA8BhN,SAA9B,EAAyC8K,KAAzC,EAAgD;;YAEnD9K,SAAV,EAAqBiN,mBAArB,CAAyC,QAAzC,EAAmDnC,MAAMoC,WAAzD;;;QAGMC,aAAN,CAAoBC,OAApB,CAA4B,kBAAU;WAC7BH,mBAAP,CAA2B,QAA3B,EAAqCnC,MAAMoC,WAA3C;GADF;;;QAKMA,WAAN,GAAoB,IAApB;QACMC,aAAN,GAAsB,EAAtB;QACME,aAAN,GAAsB,IAAtB;QACMC,aAAN,GAAsB,KAAtB;SACOxC,KAAP;;;AClBF;;;;;;;;;;AAUA,AAAe,SAASyC,YAAT,CAAsBxB,SAAtB,EAAiCyB,IAAjC,EAAuCC,IAAvC,EAA6C;MACpDC,iBAAiBD,SAASE,SAAT,GACnB5B,SADmB,GAEnBA,UAAUV,KAAV,CAAgB,CAAhB,EAAmBpC,UAAU8C,SAAV,EAAqB,MAArB,EAA6B0B,IAA7B,CAAnB,CAFJ;;iBAIeL,OAAf,CAAuB,oBAAY;QAC7BrK,SAAS,UAAT,CAAJ,EAA0B;;cAChB2J,IAAR,CAAa,uDAAb;;QAEItE,KAAKrF,SAAS,UAAT,KAAwBA,SAASqF,EAA5C,CAJiC;QAK7BrF,SAASoJ,OAAT,IAAoBV,WAAWrD,EAAX,CAAxB,EAAwC;;;;WAIjCnE,OAAL,CAAaqC,MAAb,GAAsBtC,cAAcwJ,KAAKvJ,OAAL,CAAaqC,MAA3B,CAAtB;WACKrC,OAAL,CAAajE,SAAb,GAAyBgE,cAAcwJ,KAAKvJ,OAAL,CAAajE,SAA3B,CAAzB;;aAEOoI,GAAGoF,IAAH,EAASzK,QAAT,CAAP;;GAZJ;;SAgBOyK,IAAP;;;ACnCF;;;;;;;;AAQA,AAAe,SAASI,aAAT,CAAuBhP,OAAvB,EAAgCiP,UAAhC,EAA4C;SAClD1G,IAAP,CAAY0G,UAAZ,EAAwBT,OAAxB,CAAgC,UAASlE,IAAT,EAAe;QACvCC,QAAQ0E,WAAW3E,IAAX,CAAd;QACIC,UAAU,KAAd,EAAqB;cACX2E,YAAR,CAAqB5E,IAArB,EAA2B2E,WAAW3E,IAAX,CAA3B;KADF,MAEO;cACG6E,eAAR,CAAwB7E,IAAxB;;GALJ;;;ACPF;;;;;;;;AAQA,AAAe,SAAS8E,SAAT,CAAmBpP,OAAnB,EAA4ByE,MAA5B,EAAoC;SAC1C8D,IAAP,CAAY9D,MAAZ,EAAoB+J,OAApB,CAA4B,gBAAQ;QAC9Ba,OAAO,EAAX;;QAGE,CAAC,OAAD,EAAU,QAAV,EAAoB,KAApB,EAA2B,OAA3B,EAAoC,QAApC,EAA8C,MAA9C,EAAsDjN,OAAtD,CAA8DkI,IAA9D,MACE,CAAC,CADH,IAEAyD,UAAUtJ,OAAO6F,IAAP,CAAV,CAHF,EAIE;aACO,IAAP;;YAEMsC,KAAR,CAActC,IAAd,IAAsB7F,OAAO6F,IAAP,IAAe+E,IAArC;GAVF;;;ACRF,SAASC,qBAAT,CAA+B5I,YAA/B,EAA6C6I,KAA7C,EAAoDC,QAApD,EAA8DjB,aAA9D,EAA6E;MACrEkB,SAAS/I,aAAajG,QAAb,KAA0B,MAAzC;MACMiP,SAASD,SAAS/I,aAAatG,aAAb,CAA2BC,WAApC,GAAkDqG,YAAjE;SACOiJ,gBAAP,CAAwBJ,KAAxB,EAA+BC,QAA/B,EAAyC,EAAEI,SAAS,IAAX,EAAzC;;MAEI,CAACH,MAAL,EAAa;0BAET7O,gBAAgB8O,OAAOhP,UAAvB,CADF,EAEE6O,KAFF,EAGEC,QAHF,EAIEjB,aAJF;;gBAOYsB,IAAd,CAAmBH,MAAnB;;;;;;;;;AASF,AAAe,SAASI,mBAAT,CACb1O,SADa,EAEb2O,OAFa,EAGb7D,KAHa,EAIboC,WAJa,EAKb;;QAEMA,WAAN,GAAoBA,WAApB;YACUlN,SAAV,EAAqBuO,gBAArB,CAAsC,QAAtC,EAAgDzD,MAAMoC,WAAtD,EAAmE,EAAEsB,SAAS,IAAX,EAAnE;;;MAGMnB,gBAAgB7N,gBAAgBQ,SAAhB,CAAtB;wBAEEqN,aADF,EAEE,QAFF,EAGEvC,MAAMoC,WAHR,EAIEpC,MAAMqC,aAJR;QAMME,aAAN,GAAsBA,aAAtB;QACMC,aAAN,GAAsB,IAAtB;;SAEOxC,KAAP;;;ACiBF;;;;;;AAMA,YAAe;4CAAA;oBAAA;sBAAA;gCAAA;8BAAA;8CAAA;8BAAA;kCAAA;8BAAA;4EAAA;8BAAA;8BAAA;oCAAA;0CAAA;sBAAA;kCAAA;oDAAA;oDAAA;gCAAA;kBAAA;wBAAA;sCAAA;wCAAA;sBAAA;4CAAA;4BAAA;8BAAA;sBAAA;;CAAf;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}