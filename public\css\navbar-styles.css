/* Navbar Styles - Main navbar styling */
@import url('navbar-variables.css');

/* Base navbar styles */
.navbar {
  z-index: 2;
  position: relative;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
  padding: 0.5rem 1rem;
}

.navbar .dropdown:focus,
.navbar .dropdown-toggle:focus {
  outline: 0;
}

.navbar .navbar-toggler {
  border: none;
  padding: 0.5rem;
  font-size: 1.25rem;
}

.navbar .navbar-toggler:focus {
  box-shadow: none;
}

.navbar-brand {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
  margin-right: 1rem;
  font-size: 1.25rem;
  color: var(--headings-color);
  text-decoration: none;
  white-space: nowrap;
}

.navbar-nav {
  display: flex;
  flex-direction: column;
  padding-left: 0;
  margin-bottom: 0;
  list-style: none;
}

.navbar-nav .nav-link {
  padding: 0.5rem 1rem;
  color: var(--body-color);
}

.navbar-nav .nav-link:hover,
.navbar-nav .nav-link:focus {
  color: var(--primary);
}

.navbar-nav .show > .nav-link,
.navbar-nav .nav-link.active {
  color: var(--primary);
}

.navbar-expand-xl {
  flex-wrap: nowrap;
  justify-content: flex-start;
}

@media (min-width: 1200px) {
  .navbar-expand-xl .navbar-nav {
    flex-direction: row;
  }

  .navbar-expand-xl .navbar-nav .dropdown-menu {
    position: absolute;
  }

  .navbar-expand-xl .navbar-nav .nav-link {
    padding-right: 0.5rem;
    padding-left: 0.5rem;
  }

  .navbar-expand-xl .navbar-collapse {
    display: flex !important;
    flex-basis: auto;
  }

  .navbar-expand-xl .navbar-toggler {
    display: none;
  }
}

/* Layout navbar specific styles */
.layout-navbar {
  position: relative;
  padding-top: 0.25rem;
  padding-bottom: 0.2rem;
  height: var(--navbar-height);
  flex-wrap: nowrap;
  color: var(--body-color);
  z-index: 10;
  background-color: rgba(255, 255, 255, 0.95) !important;
  backdrop-filter: saturate(200%) blur(6px);
}

.navbar-detached {
  margin: 1.5rem 1.5rem 0;
  border-radius: var(--border-radius);
  box-shadow: 0 0 0.375rem 0.25rem rgba(161, 172, 184, 0.15);
}

.container-xxl {
  max-width: 1440px;
  width: 100%;
  padding-right: 1.5rem;
  padding-left: 1.5rem;
  margin-right: auto;
  margin-left: auto;
}

/* Navbar search */
.navbar-search-wrapper .navbar-search-icon,
.navbar-search-wrapper .search-input {
  color: var(--body-color);
}

.search-input-wrapper .search-input,
.search-input-wrapper .search-toggler {
  background-color: transparent !important;
  color: var(--body-color);
}

.search-input-wrapper .search-input {
  border: none;
  background-color: transparent;
  border-radius: 0;
  padding-left: 0.5rem;
}

.search-input-wrapper .search-input:focus {
  box-shadow: none;
  border: none;
}

/* Navbar menu toggle */
.layout-menu-toggle {
  display: block;
}

@media (min-width: 1200px) {
  .layout-menu-toggle.d-xl-none {
    display: none !important;
  }
}

/* Navbar dropdown */
.dropdown-menu {
  position: absolute;
  z-index: 1000;
  display: none;
  min-width: 10rem;
  padding: 0.5rem 0;
  margin: 0;
  font-size: 0.9375rem;
  color: var(--body-color);
  text-align: left;
  list-style: none;
  background-color: var(--card-bg);
  background-clip: padding-box;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  box-shadow: 0 0.25rem 1rem rgba(161, 172, 184, 0.45);
}

.dropdown-menu-end {
  right: 0;
  left: auto;
}

.dropdown-item {
  display: block;
  width: 100%;
  padding: 0.532rem 1.25rem;
  clear: both;
  font-weight: 400;
  color: var(--body-color);
  text-align: inherit;
  white-space: nowrap;
  background-color: transparent;
  border: 0;
  text-decoration: none;
}

.dropdown-item:hover,
.dropdown-item:focus {
  color: var(--headings-color);
  background-color: rgba(67, 89, 113, 0.04);
}

.dropdown-divider {
  height: 0;
  margin: 0.5rem 0;
  overflow: hidden;
  border-top: 1px solid var(--border-color);
}

/* Avatar */
.avatar {
  position: relative;
  width: 2.375rem;
  height: 2.375rem;
  cursor: pointer;
}

.avatar img {
  width: 100%;
  height: 100%;
}

.avatar-online::before {
  content: '';
  position: absolute;
  bottom: 0;
  right: 0;
  width: 0.75rem;
  height: 0.75rem;
  border-radius: 50%;
  background-color: #71dd37;
  border: 2px solid var(--card-bg);
}

.w-px-40 {
  width: 40px !important;
}

.h-auto {
  height: auto !important;
}

.rounded-circle {
  border-radius: 50% !important;
}

/* Notification badges */
.badge-notifications {
  font-size: 0.65rem;
  padding: 0.2rem 0.4rem;
}

.badge-dot {
  width: 8px;
  height: 8px;
  padding: 0;
  border-radius: 50%;
  vertical-align: middle;
}

/* Dropdown notifications */
.dropdown-notifications-list {
  max-height: 350px;
  overflow-y: auto;
}

.dropdown-notifications-item {
  padding: 0.75rem 1rem;
  cursor: pointer;
  transition: background-color 0.15s ease-in-out;
}

.dropdown-notifications-item:hover {
  background-color: rgba(67, 89, 113, 0.04);
}

.dropdown-notifications-read,
.dropdown-notifications-archive {
  color: #697a8d;
  font-size: 0.85rem;
}

.dropdown-notifications-read:hover,
.dropdown-notifications-archive:hover {
  color: #566a7f;
}

.dropdown-notifications-actions {
  display: flex;
  flex-direction: column;
}

.dropdown-header {
  padding: 0.5rem 1.25rem;
}

.avatar-initial {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  font-weight: 600;
  color: #fff;
}

.bg-primary {
  background-color: #696cff !important;
}

.bg-success {
  background-color: #71DD37 !important;
}

.bg-info {
  background-color: #03C3EC !important;
}

.bg-danger {
  background-color: #FF3E1D !important;
}

.scrollable-container {
  overflow-y: auto;
  max-height: 300px;
}

/* Dark mode overrides */
[data-bs-theme=dark] .layout-navbar {
  background-color: rgba(33, 37, 41, 0.95) !important;
}

[data-bs-theme=dark] .navbar-detached {
  box-shadow: 0 0 0.375rem 0.25rem rgba(0, 0, 0, 0.2);
}
