.dashboard-title,
.dashboard-links {
    text-align: center;
}

.dashboard-title {
    margin-top: 2rem;
    font-size: 3rem;
}

.dashboard-links {
    padding-bottom: 2rem;
}

.products-list {
    list-style: none;
    margin: 0;
    padding: 0;

    .product-img {
        float: left;
    }

    > .item {
        border-radius: 3px;
        -webkit-box-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);
        box-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);
        padding: 10px 0;
        background: #fff;
    }

    > .item:before,
    > .item:after {
        content: " ";
        display: table;
    }

    > .item:after {
        clear: both;
    }

    .product-img {
        float: left;
    }

    .product-img .fa-2x {
        font-size: 1.75rem;
    }

    .product-info {
        margin-left: 60px;
    }

    .product-title {
        font-weight: normal;
    }

    .product-description {
        display: block;
        color: #999;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
    }

    .product-description {
        display: block;
        color: #999;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
    }
}

.product-list-in-box {
    > .item {
        -webkit-box-shadow: none;
        box-shadow: none;
        border-radius: 0;
        border-bottom: 1px solid #f4f4f4;
    }
    > .item:last-of-type {
        border-bottom-width: 0;
    }
}
