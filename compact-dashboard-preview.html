<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Compact Modern Dashboard - Preview</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/boxicons@2.1.4/css/boxicons.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/fullcalendar@5.11.3/main.min.css" rel="stylesheet">
    <style>
        /* Compact Modern Dashboard Styles */
        .compact-dashboard {
            min-height: 100vh;
            position: relative;
            background: #f8fafc;
            padding: 0;
            margin: 0;
        }

        /* Background Effects */
        .dashboard-background {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            overflow: hidden;
        }

        .blur-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg,
                rgba(99, 102, 241, 0.1) 0%,
                rgba(168, 85, 247, 0.1) 25%,
                rgba(236, 72, 153, 0.1) 50%,
                rgba(59, 130, 246, 0.1) 75%,
                rgba(16, 185, 129, 0.1) 100%);
            backdrop-filter: blur(100px);
        }

        .gradient-mesh {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 20%, rgba(99, 102, 241, 0.15) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(168, 85, 247, 0.15) 0%, transparent 50%),
                radial-gradient(circle at 40% 60%, rgba(236, 72, 153, 0.15) 0%, transparent 50%);
            animation: meshMove 20s ease-in-out infinite;
        }

        @keyframes meshMove {
            0%, 100% { transform: translate(0, 0) scale(1); }
            33% { transform: translate(-20px, -20px) scale(1.1); }
            66% { transform: translate(20px, -10px) scale(0.9); }
        }

        /* Compact Header */
        .compact-header {
            position: relative;
            z-index: 10;
            padding: 1rem 1.5rem;
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .header-container {
            display: flex;
            align-items: center;
            justify-content: space-between;
            max-width: 1400px;
            margin: 0 auto;
        }

        .page-title {
            font-size: 1.75rem;
            font-weight: 700;
            color: #1f2937;
            margin: 0;
            line-height: 1.2;
        }

        .page-subtitle {
            font-size: 0.875rem;
            color: #6b7280;
            margin: 0.25rem 0 0 0;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .date-badge {
            background: linear-gradient(135deg, #d97706, #ea580c);
            color: white;
            padding: 0.125rem 0.5rem;
            border-radius: 0.375rem;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 1.5rem;
        }

        .header-stats {
            display: flex;
            gap: 1.5rem;
        }

        .mini-stat-number {
            display: block;
            font-size: 1.25rem;
            font-weight: 700;
            color: #1f2937;
            line-height: 1;
        }

        .mini-stat-label {
            font-size: 0.75rem;
            color: #6b7280;
            margin-top: 0.125rem;
        }

        .action-btn {
            width: 2.5rem;
            height: 2.5rem;
            border-radius: 0.75rem;
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.125rem;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .action-btn.secondary {
            background: rgba(107, 114, 128, 0.1);
            color: #6b7280;
        }

        .action-btn.secondary:hover {
            background: rgba(107, 114, 128, 0.2);
            color: #374151;
            transform: translateY(-1px);
        }

        .action-btn.primary {
            background: linear-gradient(135deg, #d97706, #ea580c);
            color: white;
            padding: 0 1rem;
            width: auto;
            gap: 0.5rem;
        }

        .action-btn.primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(217, 119, 6, 0.4);
        }

        /* Dashboard Content */
        .dashboard-content {
            position: relative;
            z-index: 5;
            padding: 1.5rem;
            max-width: 1400px;
            margin: 0 auto;
        }

        /* Stats Grid */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            position: relative;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 1rem;
            padding: 1.5rem;
            overflow: hidden;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .stat-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }

        .stat-card-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .stat-icon {
            width: 3rem;
            height: 3rem;
            border-radius: 0.75rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.25rem;
            color: white;
        }

        .stat-card.revenue .stat-icon {
            background: linear-gradient(135deg, #d97706, #ea580c);
        }

        .stat-card.students .stat-icon {
            background: linear-gradient(135deg, #dc2626, #ef4444);
        }

        .stat-card.tutors .stat-icon {
            background: linear-gradient(135deg, #7c3aed, #8b5cf6);
        }

        .stat-card.sessions .stat-icon {
            background: linear-gradient(135deg, #059669, #10b981);
        }

        .stat-info {
            text-align: right;
            flex: 1;
            margin-left: 1rem;
        }

        .stat-number {
            font-size: 1.875rem;
            font-weight: 700;
            color: #1f2937;
            margin: 0;
            line-height: 1;
        }

        .stat-label {
            font-size: 0.875rem;
            color: #6b7280;
            margin: 0.25rem 0 0.5rem 0;
        }

        .stat-change {
            font-size: 0.75rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: flex-end;
            gap: 0.25rem;
        }

        .stat-change.positive {
            color: #10b981;
        }

        /* Content Grid */
        .content-grid {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 1.5rem;
        }

        .activity-card, .calendar-card {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 1rem;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .activity-card:hover, .calendar-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .card-header {
            padding: 1.25rem 1.5rem;
            border-bottom: 1px solid rgba(229, 231, 235, 0.5);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .card-title {
            font-size: 1.125rem;
            font-weight: 600;
            color: #1f2937;
            margin: 0;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .card-content {
            padding: 1.5rem;
        }

        .activity-item {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 0.75rem;
            border-radius: 0.75rem;
            transition: background-color 0.2s ease;
            margin-bottom: 1rem;
        }

        .activity-item:hover {
            background: rgba(243, 244, 246, 0.5);
        }

        .activity-avatar {
            width: 2.5rem;
            height: 2.5rem;
            border-radius: 0.75rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1rem;
            color: white;
            background: linear-gradient(135deg, #6366f1, #8b5cf6);
        }

        .activity-title {
            font-size: 0.875rem;
            font-weight: 600;
            color: #1f2937;
            margin: 0 0 0.25rem 0;
        }

        .activity-desc {
            font-size: 0.75rem;
            color: #6b7280;
            margin: 0;
        }

        /* Calendar */
        .calendar-container {
            background: rgba(255, 255, 255, 0.5);
            border-radius: 0.75rem;
            padding: 1rem;
            backdrop-filter: blur(10px);
        }

        .fc-toolbar {
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(10px);
            padding: 0.75rem 1rem;
            border-radius: 0.75rem;
            margin-bottom: 1rem;
            border: 1px solid rgba(229, 231, 235, 0.5);
        }

        .fc-button {
            background: linear-gradient(135deg, #6366f1, #8b5cf6) !important;
            border: none !important;
            color: white !important;
            border-radius: 0.5rem !important;
            padding: 0.5rem 1rem !important;
            font-weight: 500 !important;
        }

        .fc-event {
            background: linear-gradient(135deg, #6366f1, #8b5cf6) !important;
            border: none !important;
            border-radius: 0.5rem !important;
            padding: 0.25rem 0.5rem !important;
        }

        /* Responsive */
        @media (max-width: 1024px) {
            .content-grid {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 768px) {
            .stats-grid {
                grid-template-columns: 1fr;
            }

            .header-container {
                flex-direction: column;
                gap: 1rem;
                align-items: flex-start;
            }
        }
    </style>
</head>
<body>
    <!-- Compact Modern Dashboard -->
    <div class="compact-dashboard">
        <!-- Background Blur Effect -->
        <div class="dashboard-background">
            <div class="blur-overlay"></div>
            <div class="gradient-mesh"></div>
        </div>

        <!-- Compact Header -->
        <div class="compact-header">
            <div class="header-container">
                <div class="header-left">
                    <h1 class="page-title">Dashboard</h1>
                    <p class="page-subtitle">
                        Good morning, John
                        <span class="date-badge">Dec 16</span>
                    </p>
                </div>
                <div class="header-right">
                    <div class="header-stats">
                        <div class="mini-stat">
                            <span class="mini-stat-number">125</span>
                            <span class="mini-stat-label">Students</span>
                        </div>
                        <div class="mini-stat">
                            <span class="mini-stat-number">18</span>
                            <span class="mini-stat-label">Tutors</span>
                        </div>
                    </div>
                    <div class="header-actions">
                        <button class="action-btn secondary">
                            <i class="bx bx-search"></i>
                        </button>
                        <button class="action-btn secondary">
                            <i class="bx bx-bell"></i>
                        </button>
                        <button class="action-btn primary">
                            <i class="bx bx-plus"></i>
                            <span>New</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="dashboard-content">
            <!-- Stats Grid -->
            <div class="stats-grid">
                <!-- Revenue Card -->
                <div class="stat-card revenue">
                    <div class="stat-card-content">
                        <div class="stat-icon">
                            <i class="bx bx-trending-up"></i>
                        </div>
                        <div class="stat-info">
                            <h3 class="stat-number">$24.5K</h3>
                            <p class="stat-label">Revenue</p>
                            <span class="stat-change positive">+12.5%</span>
                        </div>
                    </div>
                </div>

                <!-- Students Card -->
                <div class="stat-card students">
                    <div class="stat-card-content">
                        <div class="stat-icon">
                            <i class="bx bx-user-pin"></i>
                        </div>
                        <div class="stat-info">
                            <h3 class="stat-number">125</h3>
                            <p class="stat-label">Students</p>
                            <span class="stat-change positive">+8.2%</span>
                        </div>
                    </div>
                </div>

                <!-- Tutors Card -->
                <div class="stat-card tutors">
                    <div class="stat-card-content">
                        <div class="stat-icon">
                            <i class="bx bx-user-voice"></i>
                        </div>
                        <div class="stat-info">
                            <h3 class="stat-number">18</h3>
                            <p class="stat-label">Tutors</p>
                            <span class="stat-change neutral">0%</span>
                        </div>
                    </div>
                </div>

                <!-- Sessions Card -->
                <div class="stat-card sessions">
                    <div class="stat-card-content">
                        <div class="stat-icon">
                            <i class="bx bx-calendar-event"></i>
                        </div>
                        <div class="stat-info">
                            <h3 class="stat-number">156</h3>
                            <p class="stat-label">Sessions</p>
                            <span class="stat-change positive">+15.3%</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Content Grid -->
            <div class="content-grid">
                <!-- Recent Activity Card -->
                <div class="activity-card">
                    <div class="card-header">
                        <h3 class="card-title">Recent Activity</h3>
                    </div>
                    <div class="card-content">
                        <div class="activity-item">
                            <div class="activity-avatar">
                                <i class="bx bx-book-reader"></i>
                            </div>
                            <div class="activity-details">
                                <h4 class="activity-title">Math Tutoring Session</h4>
                                <p class="activity-desc">Regular - $45.00</p>
                            </div>
                        </div>
                        <div class="activity-item">
                            <div class="activity-avatar">
                                <i class="bx bx-book-reader"></i>
                            </div>
                            <div class="activity-details">
                                <h4 class="activity-title">SAT Prep Session</h4>
                                <p class="activity-desc">Premium - $65.00</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Modern Calendar Card -->
                <div class="calendar-card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="bx bx-calendar-event"></i>
                            Schedule Calendar
                        </h3>
                    </div>
                    <div class="card-content">
                        <div class="calendar-container">
                            <div id="calendar"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/fullcalendar@5.11.3/main.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // Calendar initialization
            var calendarEl = document.getElementById('calendar');
            if (calendarEl) {
                var calendar = new FullCalendar.Calendar(calendarEl, {
                    initialView: 'dayGridMonth',
                    height: 'auto',
                    headerToolbar: {
                        left: 'prev,next today',
                        center: 'title',
                        right: 'dayGridMonth,timeGridWeek'
                    },
                    events: [
                        {
                            title: 'Math Tutoring',
                            start: '2024-12-18',
                            backgroundColor: '#6366f1',
                            borderColor: '#6366f1'
                        },
                        {
                            title: 'SAT Prep',
                            start: '2024-12-20',
                            backgroundColor: '#8b5cf6',
                            borderColor: '#8b5cf6'
                        }
                    ]
                });
                calendar.render();
            }

            // Interactive animations
            document.querySelectorAll('.stat-card').forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-6px) scale(1.02)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });
        });
    </script>
</body>
</html>
