/* Navbar Utilities - Utility classes for navbar */

/* Flex utilities */
.d-flex {
  display: flex !important;
}

.flex-row {
  flex-direction: row !important;
}

.flex-column {
  flex-direction: column !important;
}

.flex-grow-1 {
  flex-grow: 1 !important;
}

.flex-shrink-0 {
  flex-shrink: 0 !important;
}

.justify-content-between {
  justify-content: space-between !important;
}

.justify-content-center {
  justify-content: center !important;
}

.align-items-center {
  align-items: center !important;
}

.align-self-center {
  align-self: center !important;
}

/* Margin utilities */
.m-0 {
  margin: 0 !important;
}

.me-2 {
  margin-right: 0.5rem !important;
}

.me-3 {
  margin-right: 1rem !important;
}

.me-4 {
  margin-right: 1.5rem !important;
}

.me-auto {
  margin-right: auto !important;
}

.ms-auto {
  margin-left: auto !important;
}

.ms-2 {
  margin-left: 0.5rem !important;
}

.ms-3 {
  margin-left: 1rem !important;
}

.mb-0 {
  margin-bottom: 0 !important;
}

.mb-2 {
  margin-bottom: 0.5rem !important;
}

/* Padding utilities */
.p-0 {
  padding: 0 !important;
}

.p-2 {
  padding: 0.5rem !important;
}

.px-0 {
  padding-left: 0 !important;
  padding-right: 0 !important;
}

.py-1 {
  padding-top: 0.25rem !important;
  padding-bottom: 0.25rem !important;
}

/* Text utilities */
.text-muted {
  color: var(--text-muted) !important;
}

.fw-semibold {
  font-weight: 600 !important;
}

.d-block {
  display: block !important;
}

/* Hide arrow from dropdown toggle */
.hide-arrow::after {
  display: none !important;
}

/* Icon utilities */
.fs-4 {
  font-size: 1.25rem !important;
}

.lh-0 {
  line-height: 0 !important;
}

.bx-sm {
  font-size: 1.25rem !important;
}

/* Responsive utilities */
@media (min-width: 1200px) {
  .d-xl-none {
    display: none !important;
  }
  
  .d-xl-block {
    display: block !important;
  }
  
  .d-xl-flex {
    display: flex !important;
  }
}

@media (max-width: 1199.98px) {
  .navbar-nav .nav-item.dropdown {
    position: static;
  }
  
  .navbar-nav .nav-item.dropdown .dropdown-menu {
    position: absolute;
    left: 0.9rem;
    right: 0.9rem;
    width: auto;
  }
}

/* Form control for search */
.form-control {
  display: block;
  width: 100%;
  padding: 0.4375rem 0.875rem;
  font-size: 0.9375rem;
  font-weight: 400;
  line-height: 1.53;
  color: var(--body-color);
  background-color: var(--card-bg);
  background-clip: padding-box;
  border: 1px solid var(--border-color);
  appearance: none;
  border-radius: var(--border-radius);
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus {
  color: var(--body-color);
  background-color: var(--card-bg);
  border-color: var(--primary);
  outline: 0;
  box-shadow: 0 0 0.25rem 0.05rem rgba(105, 108, 255, 0.1);
}

.border-0 {
  border: 0 !important;
}

.shadow-none {
  box-shadow: none !important;
}
